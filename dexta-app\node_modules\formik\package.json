{"name": "formik", "description": "Build forms in React, without the tears", "version": "2.4.3", "license": "Apache-2.0", "author": "<PERSON> <<EMAIL>> (https://jaredpalmer.com)", "contributors": ["<PERSON> <<EMAIL>> (https://probablyup.com)"], "repository": "jaredpalmer/formik", "homepage": "https://formik.org", "keywords": ["formik", "form", "forms", "react", "react-dom", "hooks", "react hooks", "validation", "render props", "validation", "higher order component", "hoc"], "funding": [{"type": "individual", "url": "https://opencollective.com/formik"}], "main": "dist/index.js", "umd:main": "dist/formik.umd.production.js", "module": "dist/formik.esm.js", "typings": "dist/index.d.ts", "files": ["dist"], "peerDependencies": {"react": ">=16.8.0"}, "scripts": {"test": "tsdx test --env=jsdom", "test:watch": "npm run test -- --watchAll", "start": "tsdx watch --tsconfig tsconfig.build.json --verbose --noClean", "build": "tsdx build --tsconfig tsconfig.build.json", "lint": "tsdx lint"}, "dependencies": {"deepmerge": "^2.1.1", "hoist-non-react-statics": "^3.3.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react-fast-compare": "^2.0.1", "tiny-warning": "^1.0.2", "tslib": "^2.0.0"}, "devDependencies": {"@testing-library/react": "^14.0.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/lodash": "^4.14.119", "@types/react": "^18.2.7", "@types/react-dom": "^18.2.4", "@types/warning": "^3.0.0", "just-debounce-it": "^1.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tsdx": "^0.14.1", "typescript": "^4.0.3", "yup": "^0.32.0"}, "jest": {"globals": {"__DEV__": "boolean"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}"], "setupFilesAfterEnv": ["<rootDir>/test/setupTests.ts"]}}