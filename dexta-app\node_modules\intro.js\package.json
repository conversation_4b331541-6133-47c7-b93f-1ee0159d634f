{"name": "intro.js", "version": "7.2.0", "description": "User Onboarding and Product Walkthrough Library", "keywords": ["onboarding", "tour", "introjs", "intro", "guide", "walkthrough", "user-onboarding"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://introjs.com", "repository": {"type": "git", "url": "https://github.com/usablica/intro.js"}, "main": "intro.js", "module": "intro.module.js", "types": "src/index.d.ts", "scripts": {"prettier": "prettier --write '(src|tests)/**/*.(js|ts|json|html)' '!tests/cypress/setup/dist'", "test": "run-p test:prettier test:jest test:jshint", "test:prettier": "prettier --check '(src|tests)/**/*.(js|ts|json|html)' '!tests/cypress/setup/dist'", "test:watch": "jest --watch", "test:jest": "jest --coverage --silent --ci --coverage --coverageReporters=\"text\" --coverageReporters=\"text-summary\"", "test:jshint": "jshint ./src --verbose && jshint ./tests --verbose", "test:cypress": "npm run build && cp -r ./dist ./tests/cypress/setup && cd ./tests && cypress run --env type=actual", "release": "./bin/release.sh || true", "prebuild": "rimraf ./dist", "build": "rollup -c", "build:watch": "rollup -c -w"}, "devDependencies": {"@babel/core": "^7.12.3", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.21.0", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-typescript": "^11.1.2", "@types/jest": "^29.4.2", "@types/node": "^20.4.9", "autoprefixer": "^9.0.0", "babel-jest": "^29.2.2", "core-js": "^3.6.5", "cypress": "^12.8.1", "cypress-real-events": "^1.7.6", "cypress-visual-regression": "^2.1.1", "eslint": "^8.0.1", "jest": "^29.5.0", "jest-extended": "^3.2.4", "jsdom": "^21.1.1", "jshint": "^2.12.0", "lerna-changelog": "^2.1.0", "minify": "^9.1.0", "node-sass": "^8.0.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-clean": "^1.2.0", "postcss-normalize": "^8.0.1", "prettier": "2.8.8", "rimraf": "^3.0.2", "rollup": "^2.48.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-eslint": "^7.0.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-multi-entry": "^2.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-progress": "^1.1.2", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.1.1", "tslib": "^2.6.0", "typescript": "^5.1.6"}, "engine": ["node >=0.1.90"], "files": ["*.js", "*.css", "*.map", "**/*.d.ts", "themes/*", "minified/*", "!tests/**/*"], "license": "AGPL-3.0", "changelog": {"labels": {"new feature": ":rocket: New Feature", "breaking": ":boom: Breaking Change", "bug": ":bug: Bug Fix", "polish": ":nail_care: Polish", "documentation": ":memo: Documentation", "internal": ":house: Internal", "performance": ":running_woman: Performance"}, "cacheDir": ".changelog"}}