import React, { useState } from "react";
import { GoArrowRight } from "react-icons/go";
import Suggestions from "./suggestions";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createChat } from "./hooks/createChat";

const Welcome = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchText, setSearchText] = useState("");

  const { mutate: chatMutate, isLoading: chatLoading } = useMutation(
    createChat,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/langchain/chats");
        const newChatId = response?.chatId;
        if (newChatId) {
          navigate(`/chatuser?chat_id=${newChatId}`);
        } else {
          console.error(
            "Failed to get new chat ID from createChat response",
            response
          );
        }
      },
      onError: (error) => {
        console.error("Error creating chat:", error);
      },
    }
  );

  const HandleCreateChat = () => {
    let data = JSON.stringify({
      query: searchText,
    });
    try {
      chatMutate(data);
    } catch (err) {
      console.log(err.message);
    }
  };

  console.log(searchText, "search text");

  return (
    <div className="w-full md:w-4/5 lg:w-3/4 xl:w-1/2 mx-auto container px-4 py-8 mt-[2rem]">
      <p className="text-2xl" style={{ fontFamily: "Archia Semibold" }}>
        Hello there!
      </p>
      <h1
        className="text-6xl font-bold mt-2"
        style={{ fontFamily: "Archia Bold" }}
      >
        Welcome to Dexta AI
      </h1>

      {/* Search input */}
      <div className="relative mt-8">
        <input
          type="text"
          className="w-full px-6 py-4 pr-16 border border-gray-300 rounded-full focus:outline-none focus:border-gray-400"
          placeholder="Need a test for a role? Type it here."
          value={searchText}
          style={{ fontFamily: "Silka" }}
          onChange={(e) => setSearchText(e.target.value)}
        />
        <button
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primaryGreen rounded-full p-3 flex items-center justify-center w-[44px] h-[44px]" // Added flex, items-center, justify-center and fixed size
          onClick={HandleCreateChat}
          disabled={chatLoading}
        >
          {chatLoading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-black"></div>
          ) : (
            <GoArrowRight className="text-xl text-black" />
          )}
        </button>
      </div>
      <Suggestions />
    </div>
  );
};

export default Welcome;
