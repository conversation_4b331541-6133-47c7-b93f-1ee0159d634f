!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):"object"==typeof exports?exports.InputRange=t(require("react")):e.InputRange=t(e.React)}(this,function(e){return function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=11)}([function(e,t){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function o(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function a(e){if(p===clearTimeout)return clearTimeout(e);if((p===r||!p)&&clearTimeout)return p=clearTimeout,clearTimeout(e);try{return p(e)}catch(t){try{return p.call(null,e)}catch(t){return p.call(this,e)}}}function i(){m&&d&&(m=!1,d.length?h=d.concat(h):y=-1,h.length&&u())}function u(){if(!m){var e=o(i);m=!0;for(var t=h.length;t;){for(d=h,h=[];++y<t;)d&&d[y].run();y=-1,t=h.length}d=null,m=!1,a(e)}}function s(e,t){this.fun=e,this.array=t}function l(){}var c,p,f=e.exports={};!function(){try{c="function"==typeof setTimeout?setTimeout:n}catch(e){c=n}try{p="function"==typeof clearTimeout?clearTimeout:r}catch(e){p=r}}();var d,h=[],m=!1,y=-1;f.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];h.push(new s(e,t)),1!==h.length||m||o(u)},s.prototype.run=function(){this.fun.apply(null,this.array)},f.title="browser",f.browser=!0,f.env={},f.argv=[],f.version="",f.versions={},f.on=l,f.addListener=l,f.once=l,f.off=l,f.removeListener=l,f.removeAllListeners=l,f.emit=l,f.binding=function(e){throw new Error("process.binding is not supported")},f.cwd=function(){return"/"},f.chdir=function(e){throw new Error("process.chdir is not supported")},f.umask=function(){return 0}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var o=n(19);Object.defineProperty(t,"captialize",{enumerable:!0,get:function(){return r(o).default}});var a=n(20);Object.defineProperty(t,"clamp",{enumerable:!0,get:function(){return r(a).default}});var i=n(21);Object.defineProperty(t,"distanceTo",{enumerable:!0,get:function(){return r(i).default}});var u=n(22);Object.defineProperty(t,"isDefined",{enumerable:!0,get:function(){return r(u).default}});var s=n(23);Object.defineProperty(t,"isNumber",{enumerable:!0,get:function(){return r(s).default}});var l=n(24);Object.defineProperty(t,"isObject",{enumerable:!0,get:function(){return r(l).default}});var c=n(25);Object.defineProperty(t,"length",{enumerable:!0,get:function(){return r(c).default}})},function(e,t,n){(function(t){if("production"!==t.env.NODE_ENV){var r="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,o=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r};e.exports=n(28)(o,!0)}else e.exports=n(27)()}).call(t,n(0))},function(t,n){t.exports=e},function(e,t,n){"use strict";function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return 1===t.length?o.apply(void 0,t):a.apply(void 0,t)}function o(e){var t=void 0;return"undefined"!=typeof Reflect&&"function"==typeof Reflect.ownKeys?t=Reflect.ownKeys(e.prototype):(t=Object.getOwnPropertyNames(e.prototype),"function"==typeof Object.getOwnPropertySymbols&&(t=t.concat(Object.getOwnPropertySymbols(e.prototype)))),t.forEach(function(t){if("constructor"!==t){var n=Object.getOwnPropertyDescriptor(e.prototype,t);"function"==typeof n.value&&Object.defineProperty(e.prototype,t,a(e,t,n))}}),e}function a(e,t,n){var r=n.value;if("function"!=typeof r)throw new Error("@autobind decorator can only be applied to methods not: "+typeof r);var o=!1;return{configurable:!0,get:function(){if(o||this===e.prototype||this.hasOwnProperty(t))return r;var n=r.bind(this);return o=!0,Object.defineProperty(this,t,{value:n,configurable:!0,writable:!0}),o=!1,n}}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e){return function(){return e}}var o=function(){};o.thatReturns=r,o.thatReturnsFalse=r(!1),o.thatReturnsTrue=r(!0),o.thatReturnsNull=r(null),o.thatReturnsThis=function(){return this},o.thatReturnsArgument=function(e){return e},e.exports=o},function(e,t,n){"use strict";(function(t){function n(e,t,n,o,a,i,u,s){if(r(t),!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,o,a,i,u,s],p=0;l=new Error(t.replace(/%s/g,function(){return c[p++]})),l.name="Invariant Violation"}throw l.framesToPop=1,l}}var r=function(e){};"production"!==t.env.NODE_ENV&&(r=function(e){if(void 0===e)throw new Error("invariant requires an error message argument")}),e.exports=n}).call(t,n(0))},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e){var t=e.formatLabel?e.formatLabel(e.children,e.type):e.children;return i.default.createElement("span",{className:e.classNames[e.type+"Label"]},i.default.createElement("span",{className:e.classNames.labelContainer},t))}Object.defineProperty(t,"__esModule",{value:!0}),t.default=o;var a=n(3),i=r(a),u=n(2),s=r(u);o.propTypes={children:s.default.node.isRequired,classNames:s.default.objectOf(s.default.string).isRequired,formatLabel:s.default.func,type:s.default.string.isRequired},e.exports=t.default},function(e,t,n){"use strict";(function(t){var r=n(5),o=r;"production"!==t.env.NODE_ENV&&function(){var e=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=0,a="Warning: "+e.replace(/%s/g,function(){return n[o++]});"undefined"!=typeof console&&console.error(a);try{throw new Error(a)}catch(e){}};o=function(t,n){if(void 0===n)throw new Error("`warning(condition, format, ...args)` requires a warning message argument");if(0!==n.indexOf("Failed Composite propType: ")&&!t){for(var r=arguments.length,o=Array(r>2?r-2:0),a=2;a<r;a++)o[a-2]=arguments[a];e.apply(void 0,[n].concat(o))}}}(),e.exports=o}).call(t,n(0))},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function u(e,t,n,r,o){var a={};return Object.keys(r).forEach(function(e){a[e]=r[e]}),a.enumerable=!!a.enumerable,a.configurable=!!a.configurable,("value"in a||a.initializer)&&(a.writable=!0),a=n.slice().reverse().reduce(function(n,r){return r(e,t,n)||n},a),o&&void 0!==a.initializer&&(a.value=a.initializer?a.initializer.call(o):void 0,a.initializer=void 0),void 0===a.initializer&&(Object.defineProperty(e,t,a),a=null),a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s,l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(3),p=r(c),f=n(2),d=r(f),h=n(4),m=r(h),y=n(18),v=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(y),b=n(12),g=r(b),w=n(7),O=r(w),D=n(14),M=r(D),T=n(17),k=r(T),P=n(15),_=r(P),V=n(16),E=r(V),x=n(1),j=n(13),L=(s=function(e){function t(e){o(this,t);var n=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.startValue=null,n.node=null,n.trackNode=null,n.isSliderDragging=!1,n.lastKeyMoved=null,n}return i(t,e),l(t,null,[{key:"propTypes",get:function(){return{allowSameValues:d.default.bool,ariaLabelledby:d.default.string,ariaControls:d.default.string,classNames:d.default.objectOf(d.default.string),disabled:d.default.bool,draggableTrack:d.default.bool,formatLabel:d.default.func,maxValue:M.default,minValue:M.default,name:d.default.string,onChangeStart:d.default.func,onChange:d.default.func.isRequired,onChangeComplete:d.default.func,step:d.default.number,value:k.default}}},{key:"defaultProps",get:function(){return{allowSameValues:!1,classNames:g.default,disabled:!1,maxValue:10,minValue:0,step:1}}}]),l(t,[{key:"componentWillUnmount",value:function(){this.removeDocumentMouseUpListener(),this.removeDocumentTouchEndListener()}},{key:"getComponentClassName",value:function(){return this.props.disabled?this.props.classNames.disabledInputRange:this.props.classNames.inputRange}},{key:"getTrackClientRect",value:function(){return this.trackNode.getClientRect()}},{key:"getKeyByPosition",value:function(e){var t=v.getValueFromProps(this.props,this.isMultiValue()),n=v.getPositionsFromValues(t,this.props.minValue,this.props.maxValue,this.getTrackClientRect());if(this.isMultiValue()){if((0,x.distanceTo)(e,n.min)<(0,x.distanceTo)(e,n.max))return"min"}return"max"}},{key:"getKeys",value:function(){return this.isMultiValue()?["min","max"]:["max"]}},{key:"hasStepDifference",value:function(e){var t=v.getValueFromProps(this.props,this.isMultiValue());return(0,x.length)(e.min,t.min)>=this.props.step||(0,x.length)(e.max,t.max)>=this.props.step}},{key:"isMultiValue",value:function(){return(0,x.isObject)(this.props.value)}},{key:"isWithinRange",value:function(e){return this.isMultiValue()?e.min>=this.props.minValue&&e.max<=this.props.maxValue&&this.props.allowSameValues?e.min<=e.max:e.min<e.max:e.max>=this.props.minValue&&e.max<=this.props.maxValue}},{key:"shouldUpdate",value:function(e){return this.isWithinRange(e)&&this.hasStepDifference(e)}},{key:"updatePosition",value:function(e,t){var n=v.getValueFromProps(this.props,this.isMultiValue()),r=v.getPositionsFromValues(n,this.props.minValue,this.props.maxValue,this.getTrackClientRect());r[e]=t,this.lastKeyMoved=e,this.updatePositions(r)}},{key:"updatePositions",value:function(e){var t={min:v.getValueFromPosition(e.min,this.props.minValue,this.props.maxValue,this.getTrackClientRect()),max:v.getValueFromPosition(e.max,this.props.minValue,this.props.maxValue,this.getTrackClientRect())},n={min:v.getStepValueFromValue(t.min,this.props.step),max:v.getStepValueFromValue(t.max,this.props.step)};this.updateValues(n)}},{key:"updateValue",value:function(e,t){var n=v.getValueFromProps(this.props,this.isMultiValue());n[e]=t,this.updateValues(n)}},{key:"updateValues",value:function(e){this.shouldUpdate(e)&&this.props.onChange(this.isMultiValue()?e:e.max)}},{key:"incrementValue",value:function(e){var t=v.getValueFromProps(this.props,this.isMultiValue()),n=t[e]+this.props.step;this.updateValue(e,n)}},{key:"decrementValue",value:function(e){var t=v.getValueFromProps(this.props,this.isMultiValue()),n=t[e]-this.props.step;this.updateValue(e,n)}},{key:"addDocumentMouseUpListener",value:function(){this.removeDocumentMouseUpListener(),this.node.ownerDocument.addEventListener("mouseup",this.handleMouseUp)}},{key:"addDocumentTouchEndListener",value:function(){this.removeDocumentTouchEndListener(),this.node.ownerDocument.addEventListener("touchend",this.handleTouchEnd)}},{key:"removeDocumentMouseUpListener",value:function(){this.node.ownerDocument.removeEventListener("mouseup",this.handleMouseUp)}},{key:"removeDocumentTouchEndListener",value:function(){this.node.ownerDocument.removeEventListener("touchend",this.handleTouchEnd)}},{key:"handleSliderDrag",value:function(e,t){var n=this;if(!this.props.disabled){var r=v.getPositionFromEvent(e,this.getTrackClientRect());this.isSliderDragging=!0,requestAnimationFrame(function(){return n.updatePosition(t,r)})}}},{key:"handleTrackDrag",value:function(e,t){if(!this.props.disabled&&this.props.draggableTrack&&!this.isSliderDragging){var n=this.props,r=n.maxValue,o=n.minValue,a=n.value,i=a.max,u=a.min,s=v.getPositionFromEvent(e,this.getTrackClientRect()),l=v.getValueFromPosition(s,o,r,this.getTrackClientRect()),c=v.getStepValueFromValue(l,this.props.step),p=v.getPositionFromEvent(t,this.getTrackClientRect()),f=v.getValueFromPosition(p,o,r,this.getTrackClientRect()),d=v.getStepValueFromValue(f,this.props.step),h=d-c,m={min:u-h,max:i-h};this.updateValues(m)}}},{key:"handleSliderKeyDown",value:function(e,t){if(!this.props.disabled)switch(e.keyCode){case j.LEFT_ARROW:case j.DOWN_ARROW:e.preventDefault(),this.decrementValue(t);break;case j.RIGHT_ARROW:case j.UP_ARROW:e.preventDefault(),this.incrementValue(t)}}},{key:"handleTrackMouseDown",value:function(e,t){if(!this.props.disabled){var n=this.props,r=n.maxValue,o=n.minValue,a=n.value,i=a.max,u=a.min;e.preventDefault();var s=v.getValueFromPosition(t,o,r,this.getTrackClientRect()),l=v.getStepValueFromValue(s,this.props.step);(!this.props.draggableTrack||l>i||l<u)&&this.updatePosition(this.getKeyByPosition(t),t)}}},{key:"handleInteractionStart",value:function(){this.props.onChangeStart&&this.props.onChangeStart(this.props.value),this.props.onChangeComplete&&!(0,x.isDefined)(this.startValue)&&(this.startValue=this.props.value)}},{key:"handleInteractionEnd",value:function(){this.isSliderDragging&&(this.isSliderDragging=!1),this.props.onChangeComplete&&(0,x.isDefined)(this.startValue)&&(this.startValue!==this.props.value&&this.props.onChangeComplete(this.props.value),this.startValue=null)}},{key:"handleKeyDown",value:function(e){this.handleInteractionStart(e)}},{key:"handleKeyUp",value:function(e){this.handleInteractionEnd(e)}},{key:"handleMouseDown",value:function(e){this.handleInteractionStart(e),this.addDocumentMouseUpListener()}},{key:"handleMouseUp",value:function(e){this.handleInteractionEnd(e),this.removeDocumentMouseUpListener()}},{key:"handleTouchStart",value:function(e){this.handleInteractionStart(e),this.addDocumentTouchEndListener()}},{key:"handleTouchEnd",value:function(e){this.handleInteractionEnd(e),this.removeDocumentTouchEndListener()}},{key:"renderSliders",value:function(){var e=this,t=v.getValueFromProps(this.props,this.isMultiValue()),n=v.getPercentagesFromValues(t,this.props.minValue,this.props.maxValue);return(this.props.allowSameValues&&"min"===this.lastKeyMoved?this.getKeys().reverse():this.getKeys()).map(function(r){var o=t[r],a=n[r],i=e.props,u=i.maxValue,s=i.minValue;return"min"===r?u=t.max:s=t.min,p.default.createElement(_.default,{ariaLabelledby:e.props.ariaLabelledby,ariaControls:e.props.ariaControls,classNames:e.props.classNames,formatLabel:e.props.formatLabel,key:r,maxValue:u,minValue:s,onSliderDrag:e.handleSliderDrag,onSliderKeyDown:e.handleSliderKeyDown,percentage:a,type:r,value:o})})}},{key:"renderHiddenInputs",value:function(){var e=this;if(!this.props.name)return[];var t=this.isMultiValue(),n=v.getValueFromProps(this.props,t);return this.getKeys().map(function(r){var o=n[r],a=t?""+e.props.name+(0,x.captialize)(r):e.props.name;return p.default.createElement("input",{key:r,type:"hidden",name:a,value:o})})}},{key:"render",value:function(){var e=this,t=this.getComponentClassName(),n=v.getValueFromProps(this.props,this.isMultiValue()),r=v.getPercentagesFromValues(n,this.props.minValue,this.props.maxValue);return p.default.createElement("div",{"aria-disabled":this.props.disabled,ref:function(t){e.node=t},className:t,onKeyDown:this.handleKeyDown,onKeyUp:this.handleKeyUp,onMouseDown:this.handleMouseDown,onTouchStart:this.handleTouchStart},p.default.createElement(O.default,{classNames:this.props.classNames,formatLabel:this.props.formatLabel,type:"min"},this.props.minValue),p.default.createElement(E.default,{classNames:this.props.classNames,draggableTrack:this.props.draggableTrack,ref:function(t){e.trackNode=t},percentages:r,onTrackDrag:this.handleTrackDrag,onTrackMouseDown:this.handleTrackMouseDown},this.renderSliders()),p.default.createElement(O.default,{classNames:this.props.classNames,formatLabel:this.props.formatLabel,type:"max"},this.props.maxValue),this.renderHiddenInputs())}}]),t}(p.default.Component),u(s.prototype,"handleSliderDrag",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleSliderDrag"),s.prototype),u(s.prototype,"handleTrackDrag",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTrackDrag"),s.prototype),u(s.prototype,"handleSliderKeyDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleSliderKeyDown"),s.prototype),u(s.prototype,"handleTrackMouseDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTrackMouseDown"),s.prototype),u(s.prototype,"handleInteractionStart",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleInteractionStart"),s.prototype),u(s.prototype,"handleInteractionEnd",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleInteractionEnd"),s.prototype),u(s.prototype,"handleKeyDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleKeyDown"),s.prototype),u(s.prototype,"handleKeyUp",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleKeyUp"),s.prototype),u(s.prototype,"handleMouseDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseDown"),s.prototype),u(s.prototype,"handleMouseUp",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseUp"),s.prototype),u(s.prototype,"handleTouchStart",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTouchStart"),s.prototype),u(s.prototype,"handleTouchEnd",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTouchEnd"),s.prototype),s);t.default=L,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(10),o=function(e){return e&&e.__esModule?e:{default:e}}(r);t.default=o.default,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={activeTrack:"input-range__track input-range__track--active",disabledInputRange:"input-range input-range--disabled",inputRange:"input-range",labelContainer:"input-range__label-container",maxLabel:"input-range__label input-range__label--max",minLabel:"input-range__label input-range__label--min",slider:"input-range__slider",sliderContainer:"input-range__slider-container",track:"input-range__track input-range__track--background",valueLabel:"input-range__label input-range__label--value"};t.default=r,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.DOWN_ARROW=40,t.LEFT_ARROW=37,t.RIGHT_ARROW=39,t.UP_ARROW=38},function(e,t,n){"use strict";function r(e){var t=e.maxValue,n=e.minValue;return(0,o.isNumber)(n)&&(0,o.isNumber)(t)?n>=t?new Error('"minValue" must be smaller than "maxValue"'):void 0:new Error('"minValue" and "maxValue" must be a number')}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r;var o=n(1);e.exports=t.default},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function u(e,t,n,r,o){var a={};return Object.keys(r).forEach(function(e){a[e]=r[e]}),a.enumerable=!!a.enumerable,a.configurable=!!a.configurable,("value"in a||a.initializer)&&(a.writable=!0),a=n.slice().reverse().reduce(function(n,r){return r(e,t,n)||n},a),o&&void 0!==a.initializer&&(a.value=a.initializer?a.initializer.call(o):void 0,a.initializer=void 0),void 0===a.initializer&&(Object.defineProperty(e,t,a),a=null),a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s,l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(3),p=r(c),f=n(2),d=r(f),h=n(4),m=r(h),y=n(7),v=r(y),b=(s=function(e){function t(e){o(this,t);var n=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.node=null,n}return i(t,e),l(t,null,[{key:"propTypes",get:function(){return{ariaLabelledby:d.default.string,ariaControls:d.default.string,classNames:d.default.objectOf(d.default.string).isRequired,formatLabel:d.default.func,maxValue:d.default.number,minValue:d.default.number,onSliderDrag:d.default.func.isRequired,onSliderKeyDown:d.default.func.isRequired,percentage:d.default.number.isRequired,type:d.default.string.isRequired,value:d.default.number.isRequired}}}]),l(t,[{key:"componentWillUnmount",value:function(){this.removeDocumentMouseMoveListener(),this.removeDocumentMouseUpListener(),this.removeDocumentTouchEndListener(),this.removeDocumentTouchMoveListener()}},{key:"getStyle",value:function(){return{position:"absolute",left:100*(this.props.percentage||0)+"%"}}},{key:"addDocumentMouseMoveListener",value:function(){this.removeDocumentMouseMoveListener(),this.node.ownerDocument.addEventListener("mousemove",this.handleMouseMove)}},{key:"addDocumentMouseUpListener",value:function(){this.removeDocumentMouseUpListener(),this.node.ownerDocument.addEventListener("mouseup",this.handleMouseUp)}},{key:"addDocumentTouchMoveListener",value:function(){this.removeDocumentTouchMoveListener(),this.node.ownerDocument.addEventListener("touchmove",this.handleTouchMove)}},{key:"addDocumentTouchEndListener",value:function(){this.removeDocumentTouchEndListener(),this.node.ownerDocument.addEventListener("touchend",this.handleTouchEnd)}},{key:"removeDocumentMouseMoveListener",value:function(){this.node.ownerDocument.removeEventListener("mousemove",this.handleMouseMove)}},{key:"removeDocumentMouseUpListener",value:function(){this.node.ownerDocument.removeEventListener("mouseup",this.handleMouseUp)}},{key:"removeDocumentTouchMoveListener",value:function(){this.node.ownerDocument.removeEventListener("touchmove",this.handleTouchMove)}},{key:"removeDocumentTouchEndListener",value:function(){this.node.ownerDocument.removeEventListener("touchend",this.handleTouchEnd)}},{key:"handleMouseDown",value:function(){this.addDocumentMouseMoveListener(),this.addDocumentMouseUpListener()}},{key:"handleMouseUp",value:function(){this.removeDocumentMouseMoveListener(),this.removeDocumentMouseUpListener()}},{key:"handleMouseMove",value:function(e){this.props.onSliderDrag(e,this.props.type)}},{key:"handleTouchStart",value:function(){this.addDocumentTouchEndListener(),this.addDocumentTouchMoveListener()}},{key:"handleTouchMove",value:function(e){this.props.onSliderDrag(e,this.props.type)}},{key:"handleTouchEnd",value:function(){this.removeDocumentTouchMoveListener(),this.removeDocumentTouchEndListener()}},{key:"handleKeyDown",value:function(e){this.props.onSliderKeyDown(e,this.props.type)}},{key:"render",value:function(){var e=this,t=this.getStyle();return p.default.createElement("span",{className:this.props.classNames.sliderContainer,ref:function(t){e.node=t},style:t},p.default.createElement(v.default,{classNames:this.props.classNames,formatLabel:this.props.formatLabel,type:"value"},this.props.value),p.default.createElement("div",{"aria-labelledby":this.props.ariaLabelledby,"aria-controls":this.props.ariaControls,"aria-valuemax":this.props.maxValue,"aria-valuemin":this.props.minValue,"aria-valuenow":this.props.value,className:this.props.classNames.slider,draggable:"false",onKeyDown:this.handleKeyDown,onMouseDown:this.handleMouseDown,onTouchStart:this.handleTouchStart,role:"slider",tabIndex:"0"}))}}]),t}(p.default.Component),u(s.prototype,"handleMouseDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseDown"),s.prototype),u(s.prototype,"handleMouseUp",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseUp"),s.prototype),u(s.prototype,"handleMouseMove",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseMove"),s.prototype),u(s.prototype,"handleTouchStart",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTouchStart"),s.prototype),u(s.prototype,"handleTouchMove",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTouchMove"),s.prototype),u(s.prototype,"handleTouchEnd",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTouchEnd"),s.prototype),u(s.prototype,"handleKeyDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleKeyDown"),s.prototype),s);t.default=b,e.exports=t.default},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function u(e,t,n,r,o){var a={};return Object.keys(r).forEach(function(e){a[e]=r[e]}),a.enumerable=!!a.enumerable,a.configurable=!!a.configurable,("value"in a||a.initializer)&&(a.writable=!0),a=n.slice().reverse().reduce(function(n,r){return r(e,t,n)||n},a),o&&void 0!==a.initializer&&(a.value=a.initializer?a.initializer.call(o):void 0,a.initializer=void 0),void 0===a.initializer&&(Object.defineProperty(e,t,a),a=null),a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s,l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(3),p=r(c),f=n(2),d=r(f),h=n(4),m=r(h),y=(s=function(e){function t(e){o(this,t);var n=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.node=null,n.trackDragEvent=null,n}return i(t,e),l(t,null,[{key:"propTypes",get:function(){return{children:d.default.node.isRequired,classNames:d.default.objectOf(d.default.string).isRequired,draggableTrack:d.default.bool,onTrackDrag:d.default.func,onTrackMouseDown:d.default.func.isRequired,percentages:d.default.objectOf(d.default.number).isRequired}}}]),l(t,[{key:"getClientRect",value:function(){return this.node.getBoundingClientRect()}},{key:"getActiveTrackStyle",value:function(){var e=100*(this.props.percentages.max-this.props.percentages.min)+"%";return{left:100*this.props.percentages.min+"%",width:e}}},{key:"addDocumentMouseMoveListener",value:function(){this.removeDocumentMouseMoveListener(),this.node.ownerDocument.addEventListener("mousemove",this.handleMouseMove)}},{key:"addDocumentMouseUpListener",value:function(){this.removeDocumentMouseUpListener(),this.node.ownerDocument.addEventListener("mouseup",this.handleMouseUp)}},{key:"removeDocumentMouseMoveListener",value:function(){this.node.ownerDocument.removeEventListener("mousemove",this.handleMouseMove)}},{key:"removeDocumentMouseUpListener",value:function(){this.node.ownerDocument.removeEventListener("mouseup",this.handleMouseUp)}},{key:"handleMouseMove",value:function(e){this.props.draggableTrack&&(null!==this.trackDragEvent&&this.props.onTrackDrag(e,this.trackDragEvent),this.trackDragEvent=e)}},{key:"handleMouseUp",value:function(){this.props.draggableTrack&&(this.removeDocumentMouseMoveListener(),this.removeDocumentMouseUpListener(),this.trackDragEvent=null)}},{key:"handleMouseDown",value:function(e){var t=e.touches?e.touches[0].clientX:e.clientX,n=this.getClientRect(),r={x:t-n.left,y:0};this.props.onTrackMouseDown(e,r),this.props.draggableTrack&&(this.addDocumentMouseMoveListener(),this.addDocumentMouseUpListener())}},{key:"handleTouchStart",value:function(e){e.preventDefault(),this.handleMouseDown(e)}},{key:"render",value:function(){var e=this,t=this.getActiveTrackStyle();return p.default.createElement("div",{className:this.props.classNames.track,onMouseDown:this.handleMouseDown,onTouchStart:this.handleTouchStart,ref:function(t){e.node=t}},p.default.createElement("div",{style:t,className:this.props.classNames.activeTrack}),this.props.children)}}]),t}(p.default.Component),u(s.prototype,"handleMouseMove",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseMove"),s.prototype),u(s.prototype,"handleMouseUp",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseUp"),s.prototype),u(s.prototype,"handleMouseDown",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleMouseDown"),s.prototype),u(s.prototype,"handleTouchStart",[m.default],Object.getOwnPropertyDescriptor(s.prototype,"handleTouchStart"),s.prototype),s);t.default=y,e.exports=t.default},function(e,t,n){"use strict";function r(e,t){var n=e.maxValue,r=e.minValue,a=e[t];return(0,o.isNumber)(a)||(0,o.isObject)(a)&&(0,o.isNumber)(a.min)&&(0,o.isNumber)(a.max)?(0,o.isNumber)(a)&&(a<r||a>n)?new Error('"'+t+'" must be in between "minValue" and "maxValue"'):(0,o.isObject)(a)&&(a.min<r||a.min>n||a.max<r||a.max>n)?new Error('"'+t+'" must be in between "minValue" and "maxValue"'):void 0:new Error('"'+t+'" must be a number or a range object')}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r;var o=n(1);e.exports=t.default},function(e,t,n){"use strict";function r(e,t){var n=t.width;return e.x/n||0}function o(e,t,n,o){return t+(n-t)*r(e,o)}function a(e,t){return t?f({},e.value):{min:e.minValue,max:e.value}}function i(e,t,n){return((0,d.clamp)(e,t,n)-t)/(n-t)||0}function u(e,t,n){return{min:i(e.min,t,n),max:i(e.max,t,n)}}function s(e,t,n,r){var o=r.width;return{x:i(e,t,n)*o,y:0}}function l(e,t,n,r){return{min:s(e.min,t,n,r),max:s(e.max,t,n,r)}}function c(e,t){var n=t.width,r=e.touches?e.touches[0]:e,o=r.clientX;return{x:(0,d.clamp)(o-t.left,0,n),y:0}}function p(e,t){return Math.round(e/t)*t}Object.defineProperty(t,"__esModule",{value:!0});var f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.getPercentageFromPosition=r,t.getValueFromPosition=o,t.getValueFromProps=a,t.getPercentageFromValue=i,t.getPercentagesFromValues=u,t.getPositionFromValue=s,t.getPositionsFromValues=l,t.getPositionFromEvent=c,t.getStepValueFromValue=p;var d=n(1)},function(e,t,n){"use strict";function r(e){return e.charAt(0).toUpperCase()+e.slice(1)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e,t,n){return Math.min(Math.max(e,t),n)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e,t){var n=Math.pow(t.x-e.x,2),r=Math.pow(t.y-e.y,2);return Math.sqrt(n+r)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e){return void 0!==e&&null!==e}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e){return"number"==typeof e}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e){return null!==e&&"object"===(void 0===e?"undefined":o(e))}Object.defineProperty(t,"__esModule",{value:!0});var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=r,e.exports=t.default},function(e,t,n){"use strict";function r(e,t){return Math.abs(e-t)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,e.exports=t.default},function(e,t,n){"use strict";(function(t){function r(e,n,r,s,l){if("production"!==t.env.NODE_ENV)for(var c in e)if(e.hasOwnProperty(c)){var p;try{o("function"==typeof e[c],"%s: %s type `%s` is invalid; it must be a function, usually from React.PropTypes.",s||"React class",r,c),p=e[c](n,c,s,r,null,i)}catch(e){p=e}if(a(!p||p instanceof Error,"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",s||"React class",r,c,typeof p),p instanceof Error&&!(p.message in u)){u[p.message]=!0;var f=l?l():"";a(!1,"Failed %s type: %s%s",r,p.message,null!=f?f:"")}}}if("production"!==t.env.NODE_ENV)var o=n(6),a=n(8),i=n(9),u={};e.exports=r}).call(t,n(0))},function(e,t,n){"use strict";var r=n(5),o=n(6);e.exports=function(){function e(){o(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t};return n.checkPropTypes=r,n.PropTypes=n,n}},function(e,t,n){"use strict";(function(t){var r=n(5),o=n(6),a=n(8),i=n(9),u=n(26);e.exports=function(e,n){function s(e){var t=e&&(T&&e[T]||e[k]);if("function"==typeof t)return t}function l(e,t){return e===t?0!==e||1/e==1/t:e!==e&&t!==t}function c(e){this.message=e,this.stack=""}function p(e){function r(r,l,p,f,d,h,m){if(f=f||P,h=h||p,m!==i)if(n)o(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");else if("production"!==t.env.NODE_ENV&&"undefined"!=typeof console){var y=f+":"+p;!u[y]&&s<3&&(a(!1,"You are manually calling a React.PropTypes validation function for the `%s` prop on `%s`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details.",h,f),u[y]=!0,s++)}return null==l[p]?r?new c(null===l[p]?"The "+d+" `"+h+"` is marked as required in `"+f+"`, but its value is `null`.":"The "+d+" `"+h+"` is marked as required in `"+f+"`, but its value is `undefined`."):null:e(l,p,f,d,h)}if("production"!==t.env.NODE_ENV)var u={},s=0;var l=r.bind(null,!1);return l.isRequired=r.bind(null,!0),l}function f(e){function t(t,n,r,o,a,i){var u=t[n];if(O(u)!==e)return new c("Invalid "+o+" `"+a+"` of type `"+D(u)+"` supplied to `"+r+"`, expected `"+e+"`.");return null}return p(t)}function d(e){function t(t,n,r,o,a){if("function"!=typeof e)return new c("Property `"+a+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var u=t[n];if(!Array.isArray(u)){return new c("Invalid "+o+" `"+a+"` of type `"+O(u)+"` supplied to `"+r+"`, expected an array.")}for(var s=0;s<u.length;s++){var l=e(u,s,r,o,a+"["+s+"]",i);if(l instanceof Error)return l}return null}return p(t)}function h(e){function t(t,n,r,o,a){if(!(t[n]instanceof e)){var i=e.name||P;return new c("Invalid "+o+" `"+a+"` of type `"+M(t[n])+"` supplied to `"+r+"`, expected instance of `"+i+"`.")}return null}return p(t)}function m(e){function n(t,n,r,o,a){for(var i=t[n],u=0;u<e.length;u++)if(l(i,e[u]))return null;return new c("Invalid "+o+" `"+a+"` of value `"+i+"` supplied to `"+r+"`, expected one of "+JSON.stringify(e)+".")}return Array.isArray(e)?p(n):("production"!==t.env.NODE_ENV&&a(!1,"Invalid argument supplied to oneOf, expected an instance of array."),r.thatReturnsNull)}function y(e){function t(t,n,r,o,a){if("function"!=typeof e)return new c("Property `"+a+"` of component `"+r+"` has invalid PropType notation inside objectOf.");var u=t[n],s=O(u);if("object"!==s)return new c("Invalid "+o+" `"+a+"` of type `"+s+"` supplied to `"+r+"`, expected an object.");for(var l in u)if(u.hasOwnProperty(l)){var p=e(u,l,r,o,a+"."+l,i);if(p instanceof Error)return p}return null}return p(t)}function v(e){function n(t,n,r,o,a){for(var u=0;u<e.length;u++){if(null==(0,e[u])(t,n,r,o,a,i))return null}return new c("Invalid "+o+" `"+a+"` supplied to `"+r+"`.")}return Array.isArray(e)?p(n):("production"!==t.env.NODE_ENV&&a(!1,"Invalid argument supplied to oneOfType, expected an instance of array."),r.thatReturnsNull)}function b(e){function t(t,n,r,o,a){var u=t[n],s=O(u);if("object"!==s)return new c("Invalid "+o+" `"+a+"` of type `"+s+"` supplied to `"+r+"`, expected `object`.");for(var l in e){var p=e[l];if(p){var f=p(u,l,r,o,a+"."+l,i);if(f)return f}}return null}return p(t)}function g(t){switch(typeof t){case"number":case"string":case"undefined":return!0;case"boolean":return!t;case"object":if(Array.isArray(t))return t.every(g);if(null===t||e(t))return!0;var n=s(t);if(!n)return!1;var r,o=n.call(t);if(n!==t.entries){for(;!(r=o.next()).done;)if(!g(r.value))return!1}else for(;!(r=o.next()).done;){var a=r.value;if(a&&!g(a[1]))return!1}return!0;default:return!1}}function w(e,t){return"symbol"===e||("Symbol"===t["@@toStringTag"]||"function"==typeof Symbol&&t instanceof Symbol)}function O(e){var t=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":w(t,e)?"symbol":t}function D(e){var t=O(e);if("object"===t){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return t}function M(e){return e.constructor&&e.constructor.name?e.constructor.name:P}var T="function"==typeof Symbol&&Symbol.iterator,k="@@iterator",P="<<anonymous>>",_={array:f("array"),bool:f("boolean"),func:f("function"),number:f("number"),object:f("object"),string:f("string"),symbol:f("symbol"),any:function(){return p(r.thatReturnsNull)}(),arrayOf:d,element:function(){function t(t,n,r,o,a){var i=t[n];if(!e(i)){return new c("Invalid "+o+" `"+a+"` of type `"+O(i)+"` supplied to `"+r+"`, expected a single ReactElement.")}return null}return p(t)}(),instanceOf:h,node:function(){function e(e,t,n,r,o){return g(e[t])?null:new c("Invalid "+r+" `"+o+"` supplied to `"+n+"`, expected a ReactNode.")}return p(e)}(),objectOf:y,oneOf:m,oneOfType:v,shape:b};return c.prototype=Error.prototype,_.checkPropTypes=u,_.PropTypes=_,_}}).call(t,n(0))}])});
//# sourceMappingURL=react-input-range.min.js.map