{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 752f787e5d9920ff4c61", "webpack:///./~/process/browser.js", "webpack:///./src/js/utils/index.js", "webpack:///./~/prop-types/index.js", "webpack:///external {\"amd\":\"react\",\"commonjs\":\"react\",\"commonjs2\":\"react\",\"root\":\"React\"}", "webpack:///./~/autobind-decorator/lib/index.js", "webpack:///./~/fbjs/lib/emptyFunction.js", "webpack:///./~/fbjs/lib/invariant.js", "webpack:///./src/js/input-range/label.jsx", "webpack:///./~/fbjs/lib/warning.js", "webpack:///./~/prop-types/lib/ReactPropTypesSecret.js", "webpack:///./src/js/input-range/input-range.jsx", "webpack:///./src/js/index.js", "webpack:///./src/js/input-range/default-class-names.js", "webpack:///./src/js/input-range/key-codes.js", "webpack:///./src/js/input-range/range-prop-type.js", "webpack:///./src/js/input-range/slider.jsx", "webpack:///./src/js/input-range/track.jsx", "webpack:///./src/js/input-range/value-prop-type.js", "webpack:///./src/js/input-range/value-transformer.js", "webpack:///./src/js/utils/captialize.js", "webpack:///./src/js/utils/clamp.js", "webpack:///./src/js/utils/distance-to.js", "webpack:///./src/js/utils/is-defined.js", "webpack:///./src/js/utils/is-number.js", "webpack:///./src/js/utils/is-object.js", "webpack:///./src/js/utils/length.js", "webpack:///./~/prop-types/checkPropTypes.js", "webpack:///./~/prop-types/factoryWithThrowingShims.js", "webpack:///./~/prop-types/factoryWithTypeCheckers.js"], "names": ["default", "Label", "props", "labelValue", "formatLabel", "children", "type", "classNames", "labelContainer", "propTypes", "node", "isRequired", "objectOf", "string", "func", "valueTransformer", "InputRange", "allowSameValues", "bool", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ariaControls", "disabled", "draggableTrack", "maxValue", "minValue", "name", "onChangeStart", "onChange", "onChangeComplete", "step", "number", "value", "startValue", "trackNode", "isSliderDragging", "lastKeyMoved", "removeDocumentMouseUpListener", "removeDocumentTouchEndListener", "inputRange", "disabledInputRange", "getClientRect", "position", "values", "getValueFromProps", "isMultiValue", "positions", "getPositionsFromValues", "getTrackClientRect", "distanceToMin", "min", "distanceToMax", "max", "currentV<PERSON>ues", "is<PERSON>ithinRange", "hasStepDifference", "key", "updatePositions", "getValueFromPosition", "transformedV<PERSON>ues", "getStepValueFromValue", "updateValues", "shouldUpdate", "updateValue", "ownerDocument", "addEventListener", "handleMouseUp", "handleTouchEnd", "removeEventListener", "event", "getPositionFromEvent", "requestAnimationFrame", "updatePosition", "prevEvent", "<PERSON><PERSON><PERSON><PERSON>", "prevPosition", "prevValue", "prevStepValue", "offset", "keyCode", "preventDefault", "decrementValue", "incrementValue", "getKeyByPosition", "handleInteractionStart", "handleInteractionEnd", "addDocumentMouseUpListener", "addDocumentTouchEndListener", "percentages", "getPercentagesFromValues", "keys", "get<PERSON><PERSON><PERSON>", "reverse", "map", "percentage", "slider", "handleSliderDrag", "handleSliderKeyDown", "componentClassName", "getComponentClassName", "handleKeyDown", "handleKeyUp", "handleMouseDown", "handleTouchStart", "handleTrackDrag", "handleTrackMouseDown", "renderSliders", "renderHiddenInputs", "Component", "DEFAULT_CLASS_NAMES", "activeTrack", "max<PERSON><PERSON><PERSON>", "minLabel", "slide<PERSON><PERSON><PERSON><PERSON>", "track", "valueLabel", "DOWN_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "rangePropType", "Error", "Slide<PERSON>", "onSliderDrag", "onSliderKeyDown", "removeDocumentMouseMoveListener", "removeDocumentTouchMoveListener", "perc", "style", "left", "handleMouseMove", "handleTouchMove", "addDocumentMouseMoveListener", "addDocumentTouchMoveListener", "getStyle", "Track", "onTrackDrag", "onTrackMouseDown", "trackDragEvent", "getBoundingClientRect", "width", "clientX", "touches", "trackClientRect", "x", "y", "activeTrackStyle", "getActiveTrackStyle", "valuePropType", "propName", "getPercentageFromPosition", "getPercentageFromValue", "getPositionFromValue", "clientRect", "length", "sizePerc", "valueDiff", "validValue", "valuePerc", "positionValue", "valuePerStep", "Math", "round", "captialize", "char<PERSON>t", "toUpperCase", "slice", "clamp", "distanceTo", "pointA", "pointB", "xDiff", "yDiff", "sqrt", "isDefined", "undefined", "isNumber", "isObject", "numA", "numB", "abs"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA,mDAA2C,cAAc;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;AChEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;;;;;;;;;;;;;;;;;;+CCnL7BA,O;;;;;;;;;0CACAA,O;;;;;;;;;+CACAA,O;;;;;;;;;8CACAA,O;;;;;;;;;6CACAA,O;;;;;;;;;6CACAA,O;;;;;;;;;2CACAA,O;;;;;;;;;;ACNT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;;;;;;;AC7BA,+C;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;;AAEA;AACA,iEAAiE,aAAa;AAC9E;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACtGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,+B;;;;;;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,qDAAqD;AACrD,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA,0BAA0B;AAC1B;AACA;AACA;;AAEA,2B;;;;;;;;;;;;;kBC5CwBC,K;;AAVxB;;;;AACA;;;;;;AAEA;;;;;;;AAOe,SAASA,KAAT,CAAeC,KAAf,EAAsB;AACnC,MAAMC,aAAaD,MAAME,WAAN,GAAoBF,MAAME,WAAN,CAAkBF,MAAMG,QAAxB,EAAkCH,MAAMI,IAAxC,CAApB,GAAoEJ,MAAMG,QAA7F;;AAEA,SACE;AAAA;AAAA,MAAM,WAAWH,MAAMK,UAAN,CAAoBL,MAAMI,IAA1B,WAAjB;AACE;AAAA;AAAA,QAAM,WAAWJ,MAAMK,UAAN,CAAiBC,cAAlC;AACGL;AADH;AADF,GADF;AAOD;;AAED;;;;;;;AAOAF,MAAMQ,SAAN,GAAkB;AAChBJ,YAAU,oBAAUK,IAAV,CAAeC,UADT;AAEhBJ,cAAY,oBAAUK,QAAV,CAAmB,oBAAUC,MAA7B,EAAqCF,UAFjC;AAGhBP,eAAa,oBAAUU,IAHP;AAIhBR,QAAM,oBAAUO,MAAV,CAAiBF;AAJP,CAAlB;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,wFAAwF,aAAa;AACrG;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;;AAEA;AACA,eAAe;AACf;;AAEA;AACA,8FAA8F,eAAe;AAC7G;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA,yB;;;;;;;;ACjEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;;;;;;;;;;;;;;;;;;ACbA;;;;AACA;;;;AACA;;;;AACA;;IAAYI,gB;;AACZ;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;;IAIqBC,U;;;;;;AACnB;;;;;wBAKuB;AACrB,aAAO;AACLC,yBAAiB,oBAAUC,IADtB;AAELC,wBAAgB,oBAAUN,MAFrB;AAGLO,sBAAc,oBAAUP,MAHnB;AAILN,oBAAY,oBAAUK,QAAV,CAAmB,oBAAUC,MAA7B,CAJP;AAKLQ,kBAAU,oBAAUH,IALf;AAMLI,wBAAgB,oBAAUJ,IANrB;AAOLd,qBAAa,oBAAUU,IAPlB;AAQLS,yCARK;AASLC,yCATK;AAULC,cAAM,oBAAUZ,MAVX;AAWLa,uBAAe,oBAAUZ,IAXpB;AAYLa,kBAAU,oBAAUb,IAAV,CAAeH,UAZpB;AAaLiB,0BAAkB,oBAAUd,IAbvB;AAcLe,cAAM,oBAAUC,MAdX;AAeLC;AAfK,OAAP;AAiBD;;AAED;;;;;;;;wBAK0B;AACxB,aAAO;AACLd,yBAAiB,KADZ;AAELV,+CAFK;AAGLc,kBAAU,KAHL;AAILE,kBAAU,EAJL;AAKLC,kBAAU,CALL;AAMLK,cAAM;AAND,OAAP;AAQD;;AAED;;;;;;;;;;;;;;;;;;;;AAiBA,sBAAY3B,KAAZ,EAAmB;AAAA;;AAGjB;;;;AAHiB,wHACXA,KADW;;AAOjB,UAAK8B,UAAL,GAAkB,IAAlB;;AAEA;;;;AAIA,UAAKtB,IAAL,GAAY,IAAZ;;AAEA;;;;AAIA,UAAKuB,SAAL,GAAiB,IAAjB;;AAEA;;;;AAIA,UAAKC,gBAAL,GAAwB,KAAxB;;AAEA;;;;AAIA,UAAKC,YAAL,GAAoB,IAApB;AA/BiB;AAgClB;;AAED;;;;;;;;;2CAKuB;AACrB,WAAKC,6BAAL;AACA,WAAKC,8BAAL;AACD;;AAED;;;;;;;;4CAKwB;AACtB,UAAI,CAAC,KAAKnC,KAAL,CAAWmB,QAAhB,EAA0B;AACxB,eAAO,KAAKnB,KAAL,CAAWK,UAAX,CAAsB+B,UAA7B;AACD;;AAED,aAAO,KAAKpC,KAAL,CAAWK,UAAX,CAAsBgC,kBAA7B;AACD;;AAED;;;;;;;;yCAKqB;AACnB,aAAO,KAAKN,SAAL,CAAeO,aAAf,EAAP;AACD;;AAED;;;;;;;;;qCAMiBC,Q,EAAU;AACzB,UAAMC,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;AACA,UAAMC,YAAY9B,iBAAiB+B,sBAAjB,CAAwCJ,MAAxC,EAAgD,KAAKxC,KAAL,CAAWsB,QAA3D,EAAqE,KAAKtB,KAAL,CAAWqB,QAAhF,EAA0F,KAAKwB,kBAAL,EAA1F,CAAlB;;AAEA,UAAI,KAAKH,YAAL,EAAJ,EAAyB;AACvB,YAAMI,gBAAgB,uBAAWP,QAAX,EAAqBI,UAAUI,GAA/B,CAAtB;AACA,YAAMC,gBAAgB,uBAAWT,QAAX,EAAqBI,UAAUM,GAA/B,CAAtB;;AAEA,YAAIH,gBAAgBE,aAApB,EAAmC;AACjC,iBAAO,KAAP;AACD;AACF;;AAED,aAAO,KAAP;AACD;;AAED;;;;;;;;8BAKU;AACR,UAAI,KAAKN,YAAL,EAAJ,EAAyB;AACvB,eAAO,CAAC,KAAD,EAAQ,KAAR,CAAP;AACD;;AAED,aAAO,CAAC,KAAD,CAAP;AACD;;AAED;;;;;;;;;;sCAOkBF,M,EAAQ;AACxB,UAAMU,gBAAgBrC,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAtB;;AAEA,aAAO,mBAAOF,OAAOO,GAAd,EAAmBG,cAAcH,GAAjC,KAAyC,KAAK/C,KAAL,CAAW2B,IAApD,IACA,mBAAOa,OAAOS,GAAd,EAAmBC,cAAcD,GAAjC,KAAyC,KAAKjD,KAAL,CAAW2B,IAD3D;AAED;;AAED;;;;;;;;mCAKe;AACb,aAAO,qBAAS,KAAK3B,KAAL,CAAW6B,KAApB,CAAP;AACD;;AAED;;;;;;;;;kCAMcW,M,EAAQ;AACpB,UAAI,KAAKE,YAAL,EAAJ,EAAyB;AACvB,eAAOF,OAAOO,GAAP,IAAc,KAAK/C,KAAL,CAAWsB,QAAzB,IACAkB,OAAOS,GAAP,IAAc,KAAKjD,KAAL,CAAWqB,QADzB,IAEA,KAAKrB,KAAL,CAAWe,eAFX,GAGGyB,OAAOO,GAAP,IAAcP,OAAOS,GAHxB,GAIGT,OAAOO,GAAP,GAAaP,OAAOS,GAJ9B;AAKD;;AAED,aAAOT,OAAOS,GAAP,IAAc,KAAKjD,KAAL,CAAWsB,QAAzB,IAAqCkB,OAAOS,GAAP,IAAc,KAAKjD,KAAL,CAAWqB,QAArE;AACD;;AAED;;;;;;;;;iCAMamB,M,EAAQ;AACnB,aAAO,KAAKW,aAAL,CAAmBX,MAAnB,KAA8B,KAAKY,iBAAL,CAAuBZ,MAAvB,CAArC;AACD;;AAED;;;;;;;;;;mCAOea,G,EAAKd,Q,EAAU;AAC5B,UAAMC,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;AACA,UAAMC,YAAY9B,iBAAiB+B,sBAAjB,CAAwCJ,MAAxC,EAAgD,KAAKxC,KAAL,CAAWsB,QAA3D,EAAqE,KAAKtB,KAAL,CAAWqB,QAAhF,EAA0F,KAAKwB,kBAAL,EAA1F,CAAlB;;AAEAF,gBAAUU,GAAV,IAAiBd,QAAjB;AACA,WAAKN,YAAL,GAAoBoB,GAApB;;AAEA,WAAKC,eAAL,CAAqBX,SAArB;AACD;;AAED;;;;;;;;;;;oCAQgBA,S,EAAW;AACzB,UAAMH,SAAS;AACbO,aAAKlC,iBAAiB0C,oBAAjB,CAAsCZ,UAAUI,GAAhD,EAAqD,KAAK/C,KAAL,CAAWsB,QAAhE,EAA0E,KAAKtB,KAAL,CAAWqB,QAArF,EAA+F,KAAKwB,kBAAL,EAA/F,CADQ;AAEbI,aAAKpC,iBAAiB0C,oBAAjB,CAAsCZ,UAAUM,GAAhD,EAAqD,KAAKjD,KAAL,CAAWsB,QAAhE,EAA0E,KAAKtB,KAAL,CAAWqB,QAArF,EAA+F,KAAKwB,kBAAL,EAA/F;AAFQ,OAAf;;AAKA,UAAMW,oBAAoB;AACxBT,aAAKlC,iBAAiB4C,qBAAjB,CAAuCjB,OAAOO,GAA9C,EAAmD,KAAK/C,KAAL,CAAW2B,IAA9D,CADmB;AAExBsB,aAAKpC,iBAAiB4C,qBAAjB,CAAuCjB,OAAOS,GAA9C,EAAmD,KAAKjD,KAAL,CAAW2B,IAA9D;AAFmB,OAA1B;;AAKA,WAAK+B,YAAL,CAAkBF,iBAAlB;AACD;;AAED;;;;;;;;;;gCAOYH,G,EAAKxB,K,EAAO;AACtB,UAAMW,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;;AAEAF,aAAOa,GAAP,IAAcxB,KAAd;;AAEA,WAAK6B,YAAL,CAAkBlB,MAAlB;AACD;;AAED;;;;;;;;;iCAMaA,M,EAAQ;AACnB,UAAI,CAAC,KAAKmB,YAAL,CAAkBnB,MAAlB,CAAL,EAAgC;AAC9B;AACD;;AAED,WAAKxC,KAAL,CAAWyB,QAAX,CAAoB,KAAKiB,YAAL,KAAsBF,MAAtB,GAA+BA,OAAOS,GAA1D;AACD;;AAED;;;;;;;;;mCAMeI,G,EAAK;AAClB,UAAMb,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;AACA,UAAMb,QAAQW,OAAOa,GAAP,IAAc,KAAKrD,KAAL,CAAW2B,IAAvC;;AAEA,WAAKiC,WAAL,CAAiBP,GAAjB,EAAsBxB,KAAtB;AACD;;AAED;;;;;;;;;mCAMewB,G,EAAK;AAClB,UAAMb,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;AACA,UAAMb,QAAQW,OAAOa,GAAP,IAAc,KAAKrD,KAAL,CAAW2B,IAAvC;;AAEA,WAAKiC,WAAL,CAAiBP,GAAjB,EAAsBxB,KAAtB;AACD;;AAED;;;;;;;;iDAK6B;AAC3B,WAAKK,6BAAL;AACA,WAAK1B,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,SAAzC,EAAoD,KAAKC,aAAzD;AACD;;AAED;;;;;;;;kDAK8B;AAC5B,WAAK5B,8BAAL;AACA,WAAK3B,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,UAAzC,EAAqD,KAAKE,cAA1D;AACD;;AAED;;;;;;;;oDAKgC;AAC9B,WAAKxD,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,SAA5C,EAAuD,KAAKF,aAA5D;AACD;;AAED;;;;;;;;qDAKiC;AAC/B,WAAKvD,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,UAA5C,EAAwD,KAAKD,cAA7D;AACD;;AAED;;;;;;;;;;qCAQiBE,K,EAAOb,G,EAAK;AAAA;;AAC3B,UAAI,KAAKrD,KAAL,CAAWmB,QAAf,EAAyB;AACvB;AACD;;AAED,UAAMoB,WAAW1B,iBAAiBsD,oBAAjB,CAAsCD,KAAtC,EAA6C,KAAKrB,kBAAL,EAA7C,CAAjB;AACA,WAAKb,gBAAL,GAAwB,IAAxB;AACAoC,4BAAsB;AAAA,eAAM,OAAKC,cAAL,CAAoBhB,GAApB,EAAyBd,QAAzB,CAAN;AAAA,OAAtB;AACD;;AAED;;;;;;;;;oCAOgB2B,K,EAAOI,S,EAAW;AAChC,UAAI,KAAKtE,KAAL,CAAWmB,QAAX,IAAuB,CAAC,KAAKnB,KAAL,CAAWoB,cAAnC,IAAqD,KAAKY,gBAA9D,EAAgF;AAC9E;AACD;;AAH+B,mBAS5B,KAAKhC,KATuB;AAAA,UAM9BqB,QAN8B,UAM9BA,QAN8B;AAAA,UAO9BC,QAP8B,UAO9BA,QAP8B;AAAA,gCAQ9BO,KAR8B;AAAA,UAQrBoB,GARqB,gBAQrBA,GARqB;AAAA,UAQhBF,GARgB,gBAQhBA,GARgB;;;AAWhC,UAAMR,WAAW1B,iBAAiBsD,oBAAjB,CAAsCD,KAAtC,EAA6C,KAAKrB,kBAAL,EAA7C,CAAjB;AACA,UAAMhB,QAAQhB,iBAAiB0C,oBAAjB,CAAsChB,QAAtC,EAAgDjB,QAAhD,EAA0DD,QAA1D,EAAoE,KAAKwB,kBAAL,EAApE,CAAd;AACA,UAAM0B,YAAY1D,iBAAiB4C,qBAAjB,CAAuC5B,KAAvC,EAA8C,KAAK7B,KAAL,CAAW2B,IAAzD,CAAlB;;AAEA,UAAM6C,eAAe3D,iBAAiBsD,oBAAjB,CAAsCG,SAAtC,EAAiD,KAAKzB,kBAAL,EAAjD,CAArB;AACA,UAAM4B,YAAY5D,iBAAiB0C,oBAAjB,CAAsCiB,YAAtC,EAAoDlD,QAApD,EAA8DD,QAA9D,EAAwE,KAAKwB,kBAAL,EAAxE,CAAlB;AACA,UAAM6B,gBAAgB7D,iBAAiB4C,qBAAjB,CAAuCgB,SAAvC,EAAkD,KAAKzE,KAAL,CAAW2B,IAA7D,CAAtB;;AAEA,UAAMgD,SAASD,gBAAgBH,SAA/B;;AAEA,UAAMf,oBAAoB;AACxBT,aAAKA,MAAM4B,MADa;AAExB1B,aAAKA,MAAM0B;AAFa,OAA1B;;AAKA,WAAKjB,YAAL,CAAkBF,iBAAlB;AACD;;AAED;;;;;;;;;;wCAQoBU,K,EAAOb,G,EAAK;AAC9B,UAAI,KAAKrD,KAAL,CAAWmB,QAAf,EAAyB;AACvB;AACD;;AAED,cAAQ+C,MAAMU,OAAd;AACA;AACA;AACEV,gBAAMW,cAAN;AACA,eAAKC,cAAL,CAAoBzB,GAApB;AACA;;AAEF;AACA;AACEa,gBAAMW,cAAN;AACA,eAAKE,cAAL,CAAoB1B,GAApB;AACA;;AAEF;AACE;AAdF;AAgBD;;AAED;;;;;;;;;;yCAQqBa,K,EAAO3B,Q,EAAU;AACpC,UAAI,KAAKvC,KAAL,CAAWmB,QAAf,EAAyB;AACvB;AACD;;AAHmC,oBAShC,KAAKnB,KAT2B;AAAA,UAMlCqB,QANkC,WAMlCA,QANkC;AAAA,UAOlCC,QAPkC,WAOlCA,QAPkC;AAAA,kCAQlCO,KARkC;AAAA,UAQzBoB,GARyB,iBAQzBA,GARyB;AAAA,UAQpBF,GARoB,iBAQpBA,GARoB;;;AAWpCmB,YAAMW,cAAN;;AAEA,UAAMhD,QAAQhB,iBAAiB0C,oBAAjB,CAAsChB,QAAtC,EAAgDjB,QAAhD,EAA0DD,QAA1D,EAAoE,KAAKwB,kBAAL,EAApE,CAAd;AACA,UAAM0B,YAAY1D,iBAAiB4C,qBAAjB,CAAuC5B,KAAvC,EAA8C,KAAK7B,KAAL,CAAW2B,IAAzD,CAAlB;;AAEA,UAAI,CAAC,KAAK3B,KAAL,CAAWoB,cAAZ,IAA8BmD,YAAYtB,GAA1C,IAAiDsB,YAAYxB,GAAjE,EAAsE;AACpE,aAAKsB,cAAL,CAAoB,KAAKW,gBAAL,CAAsBzC,QAAtB,CAApB,EAAqDA,QAArD;AACD;AACF;;AAED;;;;;;;;6CAMyB;AACvB,UAAI,KAAKvC,KAAL,CAAWwB,aAAf,EAA8B;AAC5B,aAAKxB,KAAL,CAAWwB,aAAX,CAAyB,KAAKxB,KAAL,CAAW6B,KAApC;AACD;;AAED,UAAI,KAAK7B,KAAL,CAAW0B,gBAAX,IAA+B,CAAC,sBAAU,KAAKI,UAAf,CAApC,EAAgE;AAC9D,aAAKA,UAAL,GAAkB,KAAK9B,KAAL,CAAW6B,KAA7B;AACD;AACF;;AAED;;;;;;;;2CAMuB;AACrB,UAAI,KAAKG,gBAAT,EAA2B;AACzB,aAAKA,gBAAL,GAAwB,KAAxB;AACD;;AAED,UAAI,CAAC,KAAKhC,KAAL,CAAW0B,gBAAZ,IAAgC,CAAC,sBAAU,KAAKI,UAAf,CAArC,EAAiE;AAC/D;AACD;;AAED,UAAI,KAAKA,UAAL,KAAoB,KAAK9B,KAAL,CAAW6B,KAAnC,EAA0C;AACxC,aAAK7B,KAAL,CAAW0B,gBAAX,CAA4B,KAAK1B,KAAL,CAAW6B,KAAvC;AACD;;AAED,WAAKC,UAAL,GAAkB,IAAlB;AACD;;AAED;;;;;;;;;kCAOcoC,K,EAAO;AACnB,WAAKe,sBAAL,CAA4Bf,KAA5B;AACD;;AAED;;;;;;;;;gCAOYA,K,EAAO;AACjB,WAAKgB,oBAAL,CAA0BhB,KAA1B;AACD;;AAED;;;;;;;;;oCAOgBA,K,EAAO;AACrB,WAAKe,sBAAL,CAA4Bf,KAA5B;AACA,WAAKiB,0BAAL;AACD;;AAED;;;;;;;;kCAMcjB,K,EAAO;AACnB,WAAKgB,oBAAL,CAA0BhB,KAA1B;AACA,WAAKhC,6BAAL;AACD;;AAED;;;;;;;;;qCAOiBgC,K,EAAO;AACtB,WAAKe,sBAAL,CAA4Bf,KAA5B;AACA,WAAKkB,2BAAL;AACD;;AAED;;;;;;;;mCAMelB,K,EAAO;AACpB,WAAKgB,oBAAL,CAA0BhB,KAA1B;AACA,WAAK/B,8BAAL;AACD;;AAED;;;;;;;;oCAKgB;AAAA;;AACd,UAAMK,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;AACA,UAAM2C,cAAcxE,iBAAiByE,wBAAjB,CAA0C9C,MAA1C,EAAkD,KAAKxC,KAAL,CAAWsB,QAA7D,EAAuE,KAAKtB,KAAL,CAAWqB,QAAlF,CAApB;AACA,UAAMkE,OAAO,KAAKvF,KAAL,CAAWe,eAAX,IACX,KAAKkB,YAAL,KAAsB,KADX,GAET,KAAKuD,OAAL,GAAeC,OAAf,EAFS,GAGT,KAAKD,OAAL,EAHJ;;AAKA,aAAOD,KAAKG,GAAL,CAAS,UAACrC,GAAD,EAAS;AACvB,YAAMxB,QAAQW,OAAOa,GAAP,CAAd;AACA,YAAMsC,aAAaN,YAAYhC,GAAZ,CAAnB;;AAFuB,sBAIM,OAAKrD,KAJX;AAAA,YAIjBqB,QAJiB,WAIjBA,QAJiB;AAAA,YAIPC,QAJO,WAIPA,QAJO;;;AAMvB,YAAI+B,QAAQ,KAAZ,EAAmB;AACjBhC,qBAAWmB,OAAOS,GAAlB;AACD,SAFD,MAEO;AACL3B,qBAAWkB,OAAOO,GAAlB;AACD;;AAED,YAAM6C,SACJ;AACE,0BAAgB,OAAK5F,KAAL,CAAWiB,cAD7B;AAEE,wBAAc,OAAKjB,KAAL,CAAWkB,YAF3B;AAGE,sBAAY,OAAKlB,KAAL,CAAWK,UAHzB;AAIE,uBAAa,OAAKL,KAAL,CAAWE,WAJ1B;AAKE,eAAKmD,GALP;AAME,oBAAUhC,QANZ;AAOE,oBAAUC,QAPZ;AAQE,wBAAc,OAAKuE,gBARrB;AASE,2BAAiB,OAAKC,mBATxB;AAUE,sBAAYH,UAVd;AAWE,gBAAMtC,GAXR;AAYE,iBAAOxB,KAZT,GADF;;AAgBA,eAAO+D,MAAP;AACD,OA7BM,CAAP;AA8BD;;AAED;;;;;;;;yCAKqB;AAAA;;AACnB,UAAI,CAAC,KAAK5F,KAAL,CAAWuB,IAAhB,EAAsB;AACpB,eAAO,EAAP;AACD;;AAED,UAAMmB,eAAe,KAAKA,YAAL,EAArB;AACA,UAAMF,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C0C,YAA/C,CAAf;;AAEA,aAAO,KAAK8C,OAAL,GAAeE,GAAf,CAAmB,UAACrC,GAAD,EAAS;AACjC,YAAMxB,QAAQW,OAAOa,GAAP,CAAd;AACA,YAAM9B,OAAOmB,oBAAkB,OAAK1C,KAAL,CAAWuB,IAA7B,GAAoC,uBAAW8B,GAAX,CAApC,GAAwD,OAAKrD,KAAL,CAAWuB,IAAhF;;AAEA,eACE,yCAAO,KAAK8B,GAAZ,EAAiB,MAAK,QAAtB,EAA+B,MAAM9B,IAArC,EAA2C,OAAOM,KAAlD,GADF;AAGD,OAPM,CAAP;AAQD;;AAED;;;;;;;;6BAKS;AAAA;;AACP,UAAMkE,qBAAqB,KAAKC,qBAAL,EAA3B;AACA,UAAMxD,SAAS3B,iBAAiB4B,iBAAjB,CAAmC,KAAKzC,KAAxC,EAA+C,KAAK0C,YAAL,EAA/C,CAAf;AACA,UAAM2C,cAAcxE,iBAAiByE,wBAAjB,CAA0C9C,MAA1C,EAAkD,KAAKxC,KAAL,CAAWsB,QAA7D,EAAuE,KAAKtB,KAAL,CAAWqB,QAAlF,CAApB;;AAEA,aACE;AAAA;AAAA;AACE,2BAAe,KAAKrB,KAAL,CAAWmB,QAD5B;AAEE,eAAK,aAACX,IAAD,EAAU;AAAE,mBAAKA,IAAL,GAAYA,IAAZ;AAAmB,WAFtC;AAGE,qBAAWuF,kBAHb;AAIE,qBAAW,KAAKE,aAJlB;AAKE,mBAAS,KAAKC,WALhB;AAME,uBAAa,KAAKC,eANpB;AAOE,wBAAc,KAAKC,gBAPrB;AAQE;AAAA;AAAA;AACE,wBAAY,KAAKpG,KAAL,CAAWK,UADzB;AAEE,yBAAa,KAAKL,KAAL,CAAWE,WAF1B;AAGE,kBAAK,KAHP;AAIG,eAAKF,KAAL,CAAWsB;AAJd,SARF;AAeE;AAAA;AAAA;AACE,wBAAY,KAAKtB,KAAL,CAAWK,UADzB;AAEE,4BAAgB,KAAKL,KAAL,CAAWoB,cAF7B;AAGE,iBAAK,aAACW,SAAD,EAAe;AAAE,qBAAKA,SAAL,GAAiBA,SAAjB;AAA6B,aAHrD;AAIE,yBAAasD,WAJf;AAKE,yBAAa,KAAKgB,eALpB;AAME,8BAAkB,KAAKC,oBANzB;AAQG,eAAKC,aAAL;AARH,SAfF;AA0BE;AAAA;AAAA;AACE,wBAAY,KAAKvG,KAAL,CAAWK,UADzB;AAEE,yBAAa,KAAKL,KAAL,CAAWE,WAF1B;AAGE,kBAAK,KAHP;AAIG,eAAKF,KAAL,CAAWqB;AAJd,SA1BF;AAiCG,aAAKmF,kBAAL;AAjCH,OADF;AAqCD;;;;EAtqBqC,gBAAMC,S;kBAAzB3F,U;;;;;;;;;;;;;;ACjBrB;;;;;;AAEA;;;;;;;;;AASA;;;;;;;;;;;;;;AAcA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;ACvCA;;;;;AAKA,IAAM4F,sBAAsB;AAC1BC,eAAa,+CADa;AAE1BtE,sBAAoB,mCAFM;AAG1BD,cAAY,aAHc;AAI1B9B,kBAAgB,8BAJU;AAK1BsG,YAAU,4CALgB;AAM1BC,YAAU,4CANgB;AAO1BjB,UAAQ,qBAPkB;AAQ1BkB,mBAAiB,+BARS;AAS1BC,SAAO,mDATmB;AAU1BC,cAAY;AAVc,CAA5B;;kBAaeN,mB;;;;;;;;;;;;;AClBf;AACO,IAAMO,kCAAa,EAAnB;;AAEP;AACO,IAAMC,kCAAa,EAAnB;;AAEP;AACO,IAAMC,oCAAc,EAApB;;AAEP;AACO,IAAMC,8BAAW,EAAjB,C;;;;;;;;;;;;kBCHiBC,a;;AAPxB;;AAEA;;;;;AAKe,SAASA,aAAT,CAAuBrH,KAAvB,EAA8B;AAAA,MACnCqB,QADmC,GACZrB,KADY,CACnCqB,QADmC;AAAA,MACzBC,QADyB,GACZtB,KADY,CACzBsB,QADyB;;;AAG3C,MAAI,CAAC,qBAASA,QAAT,CAAD,IAAuB,CAAC,qBAASD,QAAT,CAA5B,EAAgD;AAC9C,WAAO,IAAIiG,KAAJ,CAAU,4CAAV,CAAP;AACD;;AAED,MAAIhG,YAAYD,QAAhB,EAA0B;AACxB,WAAO,IAAIiG,KAAJ,CAAU,4CAAV,CAAP;AACD;AACF;;;;;;;;;;;;;;;;;;;ACjBD;;;;AACA;;;;AACA;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;IAGqBC,M;;;;;;AACnB;;;;;;;;;;;;;;;;wBAgBuB;AACrB,aAAO;AACLtG,wBAAgB,oBAAUN,MADrB;AAELO,sBAAc,oBAAUP,MAFnB;AAGLN,oBAAY,oBAAUK,QAAV,CAAmB,oBAAUC,MAA7B,EAAqCF,UAH5C;AAILP,qBAAa,oBAAUU,IAJlB;AAKLS,kBAAU,oBAAUO,MALf;AAMLN,kBAAU,oBAAUM,MANf;AAOL4F,sBAAc,oBAAU5G,IAAV,CAAeH,UAPxB;AAQLgH,yBAAiB,oBAAU7G,IAAV,CAAeH,UAR3B;AASLkF,oBAAY,oBAAU/D,MAAV,CAAiBnB,UATxB;AAULL,cAAM,oBAAUO,MAAV,CAAiBF,UAVlB;AAWLoB,eAAO,oBAAUD,MAAV,CAAiBnB;AAXnB,OAAP;AAaD;;AAED;;;;;;;;;;;;;;;;;AAcA,kBAAYT,KAAZ,EAAmB;AAAA;;AAGjB;;;;AAHiB,gHACXA,KADW;;AAOjB,UAAKQ,IAAL,GAAY,IAAZ;AAPiB;AAQlB;;AAED;;;;;;;;;2CAKuB;AACrB,WAAKkH,+BAAL;AACA,WAAKxF,6BAAL;AACA,WAAKC,8BAAL;AACA,WAAKwF,+BAAL;AACD;;AAED;;;;;;;+BAIW;AACT,UAAMC,OAAO,CAAC,KAAK5H,KAAL,CAAW2F,UAAX,IAAyB,CAA1B,IAA+B,GAA5C;AACA,UAAMkC,QAAQ;AACZtF,kBAAU,UADE;AAEZuF,cAASF,IAAT;AAFY,OAAd;;AAKA,aAAOC,KAAP;AACD;;AAED;;;;;;;;mDAK+B;AAC7B,WAAKH,+BAAL;AACA,WAAKlH,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,WAAzC,EAAsD,KAAKiE,eAA3D;AACD;;AAED;;;;;;;;iDAK6B;AAC3B,WAAK7F,6BAAL;AACA,WAAK1B,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,SAAzC,EAAoD,KAAKC,aAAzD;AACD;;AAED;;;;;;;;mDAK+B;AAC7B,WAAK4D,+BAAL;AACA,WAAKnH,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,WAAzC,EAAsD,KAAKkE,eAA3D;AACD;;AAED;;;;;;;;kDAK8B;AAC5B,WAAK7F,8BAAL;AACA,WAAK3B,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,UAAzC,EAAqD,KAAKE,cAA1D;AACD;;AAED;;;;;;;sDAIkC;AAChC,WAAKxD,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,WAA5C,EAAyD,KAAK8D,eAA9D;AACD;;AAED;;;;;;;oDAIgC;AAC9B,WAAKvH,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,SAA5C,EAAuD,KAAKF,aAA5D;AACD;;AAED;;;;;;;sDAIkC;AAChC,WAAKvD,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,WAA5C,EAAyD,KAAK+D,eAA9D;AACD;;AAED;;;;;;;qDAIiC;AAC/B,WAAKxH,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,UAA5C,EAAwD,KAAKD,cAA7D;AACD;;AAED;;;;;;;sCAKkB;AAChB,WAAKiE,4BAAL;AACA,WAAK9C,0BAAL;AACD;;AAED;;;;;;;oCAKgB;AACd,WAAKuC,+BAAL;AACA,WAAKxF,6BAAL;AACD;;AAED;;;;;;;;oCAMgBgC,K,EAAO;AACrB,WAAKlE,KAAL,CAAWwH,YAAX,CAAwBtD,KAAxB,EAA+B,KAAKlE,KAAL,CAAWI,IAA1C;AACD;;AAED;;;;;;;uCAKmB;AACjB,WAAKgF,2BAAL;AACA,WAAK8C,4BAAL;AACD;;AAED;;;;;;;;oCAMgBhE,K,EAAO;AACrB,WAAKlE,KAAL,CAAWwH,YAAX,CAAwBtD,KAAxB,EAA+B,KAAKlE,KAAL,CAAWI,IAA1C;AACD;;AAED;;;;;;;qCAKiB;AACf,WAAKuH,+BAAL;AACA,WAAKxF,8BAAL;AACD;;AAED;;;;;;;;kCAMc+B,K,EAAO;AACnB,WAAKlE,KAAL,CAAWyH,eAAX,CAA2BvD,KAA3B,EAAkC,KAAKlE,KAAL,CAAWI,IAA7C;AACD;;AAED;;;;;;;6BAIS;AAAA;;AACP,UAAMyH,QAAQ,KAAKM,QAAL,EAAd;;AAEA,aACE;AAAA;AAAA;AACE,qBAAW,KAAKnI,KAAL,CAAWK,UAAX,CAAsByG,eADnC;AAEE,eAAK,aAACtG,IAAD,EAAU;AAAE,mBAAKA,IAAL,GAAYA,IAAZ;AAAmB,WAFtC;AAGE,iBAAOqH,KAHT;AAIE;AAAA;AAAA;AACE,wBAAY,KAAK7H,KAAL,CAAWK,UADzB;AAEE,yBAAa,KAAKL,KAAL,CAAWE,WAF1B;AAGE,kBAAK,OAHP;AAIG,eAAKF,KAAL,CAAW6B;AAJd,SAJF;AAWE;AACE,6BAAiB,KAAK7B,KAAL,CAAWiB,cAD9B;AAEE,2BAAe,KAAKjB,KAAL,CAAWkB,YAF5B;AAGE,2BAAe,KAAKlB,KAAL,CAAWqB,QAH5B;AAIE,2BAAe,KAAKrB,KAAL,CAAWsB,QAJ5B;AAKE,2BAAe,KAAKtB,KAAL,CAAW6B,KAL5B;AAME,qBAAW,KAAK7B,KAAL,CAAWK,UAAX,CAAsBuF,MANnC;AAOE,qBAAU,OAPZ;AAQE,qBAAW,KAAKK,aARlB;AASE,uBAAa,KAAKE,eATpB;AAUE,wBAAc,KAAKC,gBAVrB;AAWE,gBAAK,QAXP;AAYE,oBAAS,GAZX;AAXF,OADF;AA2BD;;;;EAnQiC,gBAAMK,S;kBAArBc,M;;;;;;;;;;;;;;;;;;;ACRrB;;;;AACA;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;IAGqBa,K;;;;;;AACnB;;;;;;;;;;wBAUuB;AACrB,aAAO;AACLjI,kBAAU,oBAAUK,IAAV,CAAeC,UADpB;AAELJ,oBAAY,oBAAUK,QAAV,CAAmB,oBAAUC,MAA7B,EAAqCF,UAF5C;AAGLW,wBAAgB,oBAAUJ,IAHrB;AAILqH,qBAAa,oBAAUzH,IAJlB;AAKL0H,0BAAkB,oBAAU1H,IAAV,CAAeH,UAL5B;AAML4E,qBAAa,oBAAU3E,QAAV,CAAmB,oBAAUkB,MAA7B,EAAqCnB;AAN7C,OAAP;AAQD;;AAED;;;;;;;;;;;AAQA,iBAAYT,KAAZ,EAAmB;AAAA;;AAGjB;;;;AAHiB,8GACXA,KADW;;AAOjB,UAAKQ,IAAL,GAAY,IAAZ;AACA,UAAK+H,cAAL,GAAsB,IAAtB;AARiB;AASlB;;AAED;;;;;;;;oCAIgB;AACd,aAAO,KAAK/H,IAAL,CAAUgI,qBAAV,EAAP;AACD;;AAED;;;;;;;0CAIsB;AACpB,UAAMC,QAAW,CAAC,KAAKzI,KAAL,CAAWqF,WAAX,CAAuBpC,GAAvB,GAA6B,KAAKjD,KAAL,CAAWqF,WAAX,CAAuBtC,GAArD,IAA4D,GAAvE,MAAN;AACA,UAAM+E,OAAU,KAAK9H,KAAL,CAAWqF,WAAX,CAAuBtC,GAAvB,GAA6B,GAAvC,MAAN;;AAEA,aAAO,EAAE+E,UAAF,EAAQW,YAAR,EAAP;AACD;;AAED;;;;;;;;mDAK+B;AAC7B,WAAKf,+BAAL;AACA,WAAKlH,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,WAAzC,EAAsD,KAAKiE,eAA3D;AACD;;AAED;;;;;;;;iDAK6B;AAC3B,WAAK7F,6BAAL;AACA,WAAK1B,IAAL,CAAUqD,aAAV,CAAwBC,gBAAxB,CAAyC,SAAzC,EAAoD,KAAKC,aAAzD;AACD;;AAED;;;;;;;sDAIkC;AAChC,WAAKvD,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,WAA5C,EAAyD,KAAK8D,eAA9D;AACD;;AAED;;;;;;;oDAIgC;AAC9B,WAAKvH,IAAL,CAAUqD,aAAV,CAAwBI,mBAAxB,CAA4C,SAA5C,EAAuD,KAAKF,aAA5D;AACD;;AAED;;;;;;;;oCAMgBG,K,EAAO;AACrB,UAAI,CAAC,KAAKlE,KAAL,CAAWoB,cAAhB,EAAgC;AAC9B;AACD;;AAED,UAAI,KAAKmH,cAAL,KAAwB,IAA5B,EAAkC;AAChC,aAAKvI,KAAL,CAAWqI,WAAX,CAAuBnE,KAAvB,EAA8B,KAAKqE,cAAnC;AACD;;AAED,WAAKA,cAAL,GAAsBrE,KAAtB;AACD;;AAED;;;;;;;oCAKgB;AACd,UAAI,CAAC,KAAKlE,KAAL,CAAWoB,cAAhB,EAAgC;AAC9B;AACD;;AAED,WAAKsG,+BAAL;AACA,WAAKxF,6BAAL;AACA,WAAKqG,cAAL,GAAsB,IAAtB;AACD;;AAED;;;;;;;oCAKgBrE,K,EAAO;AACrB,UAAMwE,UAAUxE,MAAMyE,OAAN,GAAgBzE,MAAMyE,OAAN,CAAc,CAAd,EAAiBD,OAAjC,GAA2CxE,MAAMwE,OAAjE;AACA,UAAME,kBAAkB,KAAKtG,aAAL,EAAxB;AACA,UAAMC,WAAW;AACfsG,WAAGH,UAAUE,gBAAgBd,IADd;AAEfgB,WAAG;AAFY,OAAjB;;AAKA,WAAK9I,KAAL,CAAWsI,gBAAX,CAA4BpE,KAA5B,EAAmC3B,QAAnC;;AAEA,UAAI,KAAKvC,KAAL,CAAWoB,cAAf,EAA+B;AAC7B,aAAK6G,4BAAL;AACA,aAAK9C,0BAAL;AACD;AACF;;AAED;;;;;;;qCAKiBjB,K,EAAO;AACtBA,YAAMW,cAAN;;AAEA,WAAKsB,eAAL,CAAqBjC,KAArB;AACD;;AAED;;;;;;;6BAIS;AAAA;;AACP,UAAM6E,mBAAmB,KAAKC,mBAAL,EAAzB;;AAEA,aACE;AAAA;AAAA;AACE,qBAAW,KAAKhJ,KAAL,CAAWK,UAAX,CAAsB0G,KADnC;AAEE,uBAAa,KAAKZ,eAFpB;AAGE,wBAAc,KAAKC,gBAHrB;AAIE,eAAK,aAAC5F,IAAD,EAAU;AAAE,mBAAKA,IAAL,GAAYA,IAAZ;AAAmB,WAJtC;AAKE;AACE,iBAAOuI,gBADT;AAEE,qBAAW,KAAK/I,KAAL,CAAWK,UAAX,CAAsBsG,WAFnC,GALF;AAQG,aAAK3G,KAAL,CAAWG;AARd,OADF;AAYD;;;;EApLgC,gBAAMsG,S;kBAApB2B,K;;;;;;;;;;;;;kBCAGa,a;;AAPxB;;AAEA;;;;;AAKe,SAASA,aAAT,CAAuBjJ,KAAvB,EAA8BkJ,QAA9B,EAAwC;AAAA,MAC7C7H,QAD6C,GACtBrB,KADsB,CAC7CqB,QAD6C;AAAA,MACnCC,QADmC,GACtBtB,KADsB,CACnCsB,QADmC;;AAErD,MAAMO,QAAQ7B,MAAMkJ,QAAN,CAAd;;AAEA,MAAI,CAAC,qBAASrH,KAAT,CAAD,KAAqB,CAAC,qBAASA,KAAT,CAAD,IAAoB,CAAC,qBAASA,MAAMkB,GAAf,CAArB,IAA4C,CAAC,qBAASlB,MAAMoB,GAAf,CAAlE,CAAJ,EAA4F;AAC1F,WAAO,IAAIqE,KAAJ,OAAc4B,QAAd,0CAAP;AACD;;AAED,MAAI,qBAASrH,KAAT,MAAoBA,QAAQP,QAAR,IAAoBO,QAAQR,QAAhD,CAAJ,EAA+D;AAC7D,WAAO,IAAIiG,KAAJ,OAAc4B,QAAd,oDAAP;AACD;;AAED,MAAI,qBAASrH,KAAT,MAAoBA,MAAMkB,GAAN,GAAYzB,QAAZ,IAAwBO,MAAMkB,GAAN,GAAY1B,QAApC,IAAgDQ,MAAMoB,GAAN,GAAY3B,QAA5D,IAAwEO,MAAMoB,GAAN,GAAY5B,QAAxG,CAAJ,EAAuH;AACrH,WAAO,IAAIiG,KAAJ,OAAc4B,QAAd,oDAAP;AACD;AACF;;;;;;;;;;;;;;;;QCbeC,yB,GAAAA,yB;QAgBA5F,oB,GAAAA,oB;QAcAd,iB,GAAAA,iB;QAmBA2G,sB,GAAAA,sB;QAgBA9D,wB,GAAAA,wB;QAgBA+D,oB,GAAAA,oB;QAoBAzG,sB,GAAAA,sB;QAcAuB,oB,GAAAA,oB;QAiBAV,qB,GAAAA,qB;;AA7IhB;;AAEA;;;;;;;AAOO,SAAS0F,yBAAT,CAAmC5G,QAAnC,EAA6C+G,UAA7C,EAAyD;AAC9D,MAAMC,SAASD,WAAWb,KAA1B;AACA,MAAMe,WAAWjH,SAASsG,CAAT,GAAaU,MAA9B;;AAEA,SAAOC,YAAY,CAAnB;AACD;;AAED;;;;;;;;;AASO,SAASjG,oBAAT,CAA8BhB,QAA9B,EAAwCjB,QAAxC,EAAkDD,QAAlD,EAA4DiI,UAA5D,EAAwE;AAC7E,MAAME,WAAWL,0BAA0B5G,QAA1B,EAAoC+G,UAApC,CAAjB;AACA,MAAMG,YAAYpI,WAAWC,QAA7B;;AAEA,SAAOA,WAAYmI,YAAYD,QAA/B;AACD;;AAED;;;;;;;AAOO,SAAS/G,iBAAT,CAA2BzC,KAA3B,EAAkC0C,YAAlC,EAAgD;AACrD,MAAIA,YAAJ,EAAkB;AAChB,wBAAY1C,MAAM6B,KAAlB;AACD;;AAED,SAAO;AACLkB,SAAK/C,MAAMsB,QADN;AAEL2B,SAAKjD,MAAM6B;AAFN,GAAP;AAID;;AAED;;;;;;;;AAQO,SAASuH,sBAAT,CAAgCvH,KAAhC,EAAuCP,QAAvC,EAAiDD,QAAjD,EAA2D;AAChE,MAAMqI,aAAa,kBAAM7H,KAAN,EAAaP,QAAb,EAAuBD,QAAvB,CAAnB;AACA,MAAMoI,YAAYpI,WAAWC,QAA7B;AACA,MAAMqI,YAAY,CAACD,aAAapI,QAAd,IAA0BmI,SAA5C;;AAEA,SAAOE,aAAa,CAApB;AACD;;AAED;;;;;;;;AAQO,SAASrE,wBAAT,CAAkC9C,MAAlC,EAA0ClB,QAA1C,EAAoDD,QAApD,EAA8D;AACnE,SAAO;AACL0B,SAAKqG,uBAAuB5G,OAAOO,GAA9B,EAAmCzB,QAAnC,EAA6CD,QAA7C,CADA;AAEL4B,SAAKmG,uBAAuB5G,OAAOS,GAA9B,EAAmC3B,QAAnC,EAA6CD,QAA7C;AAFA,GAAP;AAID;;AAED;;;;;;;;;AASO,SAASgI,oBAAT,CAA8BxH,KAA9B,EAAqCP,QAArC,EAA+CD,QAA/C,EAAyDiI,UAAzD,EAAqE;AAC1E,MAAMC,SAASD,WAAWb,KAA1B;AACA,MAAMkB,YAAYP,uBAAuBvH,KAAvB,EAA8BP,QAA9B,EAAwCD,QAAxC,CAAlB;AACA,MAAMuI,gBAAgBD,YAAYJ,MAAlC;;AAEA,SAAO;AACLV,OAAGe,aADE;AAELd,OAAG;AAFE,GAAP;AAID;;AAED;;;;;;;;;AASO,SAASlG,sBAAT,CAAgCJ,MAAhC,EAAwClB,QAAxC,EAAkDD,QAAlD,EAA4DiI,UAA5D,EAAwE;AAC7E,SAAO;AACLvG,SAAKsG,qBAAqB7G,OAAOO,GAA5B,EAAiCzB,QAAjC,EAA2CD,QAA3C,EAAqDiI,UAArD,CADA;AAELrG,SAAKoG,qBAAqB7G,OAAOS,GAA5B,EAAiC3B,QAAjC,EAA2CD,QAA3C,EAAqDiI,UAArD;AAFA,GAAP;AAID;;AAED;;;;;;;AAOO,SAASnF,oBAAT,CAA8BD,KAA9B,EAAqCoF,UAArC,EAAiD;AACtD,MAAMC,SAASD,WAAWb,KAA1B;;AADsD,aAElCvE,MAAMyE,OAAN,GAAgBzE,MAAMyE,OAAN,CAAc,CAAd,CAAhB,GAAmCzE,KAFD;AAAA,MAE9CwE,OAF8C,QAE9CA,OAF8C;;AAItD,SAAO;AACLG,OAAG,kBAAMH,UAAUY,WAAWxB,IAA3B,EAAiC,CAAjC,EAAoCyB,MAApC,CADE;AAELT,OAAG;AAFE,GAAP;AAID;;AAED;;;;;;;AAOO,SAASrF,qBAAT,CAA+B5B,KAA/B,EAAsCgI,YAAtC,EAAoD;AACzD,SAAOC,KAAKC,KAAL,CAAWlI,QAAQgI,YAAnB,IAAmCA,YAA1C;AACD,C;;;;;;;;;;;;kBCzIuBG,U;AANxB;;;;;;AAMe,SAASA,UAAT,CAAoBrJ,MAApB,EAA4B;AACzC,SAAOA,OAAOsJ,MAAP,CAAc,CAAd,EAAiBC,WAAjB,KAAiCvJ,OAAOwJ,KAAP,CAAa,CAAb,CAAxC;AACD;;;;;;;;;;;;;kBCAuBC,K;AARxB;;;;;;;;AAQe,SAASA,KAAT,CAAevI,KAAf,EAAsBkB,GAAtB,EAA2BE,GAA3B,EAAgC;AAC7C,SAAO6G,KAAK/G,GAAL,CAAS+G,KAAK7G,GAAL,CAASpB,KAAT,EAAgBkB,GAAhB,CAAT,EAA+BE,GAA/B,CAAP;AACD;;;;;;;;;;;;;kBCHuBoH,U;AAPxB;;;;;;;AAOe,SAASA,UAAT,CAAoBC,MAApB,EAA4BC,MAA5B,EAAoC;AACjD,MAAMC,iBAASD,OAAO1B,CAAP,GAAWyB,OAAOzB,CAA3B,EAAiC,CAAjC,CAAN;AACA,MAAM4B,iBAASF,OAAOzB,CAAP,GAAWwB,OAAOxB,CAA3B,EAAiC,CAAjC,CAAN;;AAEA,SAAOgB,KAAKY,IAAL,CAAUF,QAAQC,KAAlB,CAAP;AACD;;;;;;;;;;;;;kBCNuBE,S;AANxB;;;;;;AAMe,SAASA,SAAT,CAAmB9I,KAAnB,EAA0B;AACvC,SAAOA,UAAU+I,SAAV,IAAuB/I,UAAU,IAAxC;AACD;;;;;;;;;;;;;kBCFuBgJ,Q;AANxB;;;;;;AAMe,SAASA,QAAT,CAAkBhJ,KAAlB,EAAyB;AACtC,SAAO,OAAOA,KAAP,KAAiB,QAAxB;AACD;;;;;;;;;;;;;;;;kBCFuBiJ,Q;AANxB;;;;;;AAMe,SAASA,QAAT,CAAkBjJ,KAAlB,EAAyB;AACtC,SAAOA,UAAU,IAAV,IAAkB,QAAOA,KAAP,yCAAOA,KAAP,OAAiB,QAA1C;AACD;;;;;;;;;;;;;kBCDuB0H,M;AAPxB;;;;;;;AAOe,SAASA,MAAT,CAAgBwB,IAAhB,EAAsBC,IAAtB,EAA4B;AACzC,SAAOlB,KAAKmB,GAAL,CAASF,OAAOC,IAAhB,CAAP;AACD;;;;;;;;ACTD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,UAAU;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gGAAgG;AAChG;AACA,SAAS;AACT;AACA;AACA,gGAAgG;AAChG;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AC5DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;;;;;;ACrDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,6BAA6B;AAC7B,QAAQ;AACR;AACA;AACA;AACA;AACA,+BAA+B,KAAK;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,4BAA4B;AAC5B,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,sBAAsB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB,2BAA2B;AAChD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB,gCAAgC;AACrD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA", "file": "react-input-range.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"InputRange\"] = factory(require(\"react\"));\n\telse\n\t\troot[\"InputRange\"] = factory(root[\"React\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_3__) {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// identity function for calling harmony imports with the correct context\n \t__webpack_require__.i = function(value) { return value; };\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 11);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 752f787e5d9920ff4c61", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/process/browser.js\n// module id = 0\n// module chunks = 0 1", "export { default as captialize } from './captialize';\nexport { default as clamp } from './clamp';\nexport { default as distanceTo } from './distance-to';\nexport { default as isDefined } from './is-defined';\nexport { default as isNumber } from './is-number';\nexport { default as isObject } from './is-object';\nexport { default as length } from './length';\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/index.js", "/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var REACT_ELEMENT_TYPE = (typeof Symbol === 'function' &&\n    Symbol.for &&\n    Symbol.for('react.element')) ||\n    0xeac7;\n\n  var isValidElement = function(object) {\n    return typeof object === 'object' &&\n      object !== null &&\n      object.$$typeof === REACT_ELEMENT_TYPE;\n  };\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(isValidElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/prop-types/index.js\n// module id = 2\n// module chunks = 0 1", "module.exports = __WEBPACK_EXTERNAL_MODULE_3__;\n\n\n//////////////////\n// WEBPACK FOOTER\n// external {\"amd\":\"react\",\"commonjs\":\"react\",\"commonjs2\":\"react\",\"root\":\"React\"}\n// module id = 3\n// module chunks = 0 1", "/**\n * @copyright 2015, <PERSON><PERSON> <<EMAIL>>\n *\n * The decorator may be used on classes or methods\n * ```\n * @autobind\n * class FullBound {}\n *\n * class PartBound {\n *   @autobind\n *   method () {}\n * }\n * ```\n */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports['default'] = autobind;\n\nfunction autobind() {\n  for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  if (args.length === 1) {\n    return boundClass.apply(undefined, args);\n  } else {\n    return boundMethod.apply(undefined, args);\n  }\n}\n\n/**\n * Use boundMethod to bind all methods on the target.prototype\n */\nfunction boundClass(target) {\n  // (Using reflect to get all keys including symbols)\n  var keys = undefined;\n  // Use Reflect if exists\n  if (typeof Reflect !== 'undefined' && typeof Reflect.ownKeys === 'function') {\n    keys = Reflect.ownKeys(target.prototype);\n  } else {\n    keys = Object.getOwnPropertyNames(target.prototype);\n    // use symbols if support is provided\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      keys = keys.concat(Object.getOwnPropertySymbols(target.prototype));\n    }\n  }\n\n  keys.forEach(function (key) {\n    // Ignore special case target method\n    if (key === 'constructor') {\n      return;\n    }\n\n    var descriptor = Object.getOwnPropertyDescriptor(target.prototype, key);\n\n    // Only methods need binding\n    if (typeof descriptor.value === 'function') {\n      Object.defineProperty(target.prototype, key, boundMethod(target, key, descriptor));\n    }\n  });\n  return target;\n}\n\n/**\n * Return a descriptor removing the value and returning a getter\n * The getter will return a .bind version of the function\n * and memoize the result against a symbol on the instance\n */\nfunction boundMethod(target, key, descriptor) {\n  var fn = descriptor.value;\n\n  if (typeof fn !== 'function') {\n    throw new Error('@autobind decorator can only be applied to methods not: ' + typeof fn);\n  }\n\n  // In IE11 calling Object.defineProperty has a side-effect of evaluating the\n  // getter for the property which is being replaced. This causes infinite\n  // recursion and an \"Out of stack space\" error.\n  var definingProperty = false;\n\n  return {\n    configurable: true,\n    get: function get() {\n      if (definingProperty || this === target.prototype || this.hasOwnProperty(key)) {\n        return fn;\n      }\n\n      var boundFn = fn.bind(this);\n      definingProperty = true;\n      Object.defineProperty(this, key, {\n        value: boundFn,\n        configurable: true,\n        writable: true\n      });\n      definingProperty = false;\n      return boundFn;\n    }\n  };\n}\nmodule.exports = exports['default'];\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/autobind-decorator/lib/index.js\n// module id = 4\n// module chunks = 0 1", "\"use strict\";\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * \n */\n\nfunction makeEmptyFunction(arg) {\n  return function () {\n    return arg;\n  };\n}\n\n/**\n * This function accepts and discards inputs; it has no side effects. This is\n * primarily useful idiomatically for overridable function endpoints which\n * always need to be callable, since J<PERSON> lacks a null-call idiom ala Cocoa.\n */\nvar emptyFunction = function emptyFunction() {};\n\nemptyFunction.thatReturns = makeEmptyFunction;\nemptyFunction.thatReturnsFalse = makeEmptyFunction(false);\nemptyFunction.thatReturnsTrue = makeEmptyFunction(true);\nemptyFunction.thatReturnsNull = makeEmptyFunction(null);\nemptyFunction.thatReturnsThis = function () {\n  return this;\n};\nemptyFunction.thatReturnsArgument = function (arg) {\n  return arg;\n};\n\nmodule.exports = emptyFunction;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/fbjs/lib/emptyFunction.js\n// module id = 5\n// module chunks = 0 1", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar validateFormat = function validateFormat(format) {};\n\nif (process.env.NODE_ENV !== 'production') {\n  validateFormat = function validateFormat(format) {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  };\n}\n\nfunction invariant(condition, format, a, b, c, d, e, f) {\n  validateFormat(format);\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(format.replace(/%s/g, function () {\n        return args[argIndex++];\n      }));\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n}\n\nmodule.exports = invariant;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/fbjs/lib/invariant.js\n// module id = 6\n// module chunks = 0 1", "import React from 'react';\nimport PropTypes from 'prop-types';\n\n/**\n * @ignore\n * @param {Object} props\n * @param {InputRangeClassNames} props.classNames\n * @param {Function} props.formatLabel\n * @param {string} props.type\n */\nexport default function Label(props) {\n  const labelValue = props.formatLabel ? props.formatLabel(props.children, props.type) : props.children;\n\n  return (\n    <span className={props.classNames[`${props.type}Label`]}>\n      <span className={props.classNames.labelContainer}>\n        {labelValue}\n      </span>\n    </span>\n  );\n}\n\n/**\n * @type {Object}\n * @property {Function} children\n * @property {Function} classNames\n * @property {Function} formatLabel\n * @property {Function} type\n */\nLabel.propTypes = {\n  children: PropTypes.node.isRequired,\n  classNames: PropTypes.objectOf(PropTypes.string).isRequired,\n  formatLabel: PropTypes.func,\n  type: PropTypes.string.isRequired,\n};\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/label.jsx", "/**\n * Copyright 2014-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n */\n\n'use strict';\n\nvar emptyFunction = require('./emptyFunction');\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar warning = emptyFunction;\n\nif (process.env.NODE_ENV !== 'production') {\n  (function () {\n    var printWarning = function printWarning(format) {\n      for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      var argIndex = 0;\n      var message = 'Warning: ' + format.replace(/%s/g, function () {\n        return args[argIndex++];\n      });\n      if (typeof console !== 'undefined') {\n        console.error(message);\n      }\n      try {\n        // --- Welcome to debugging React ---\n        // This error was thrown as a convenience so that you can use this stack\n        // to find the callsite that caused this warning to fire.\n        throw new Error(message);\n      } catch (x) {}\n    };\n\n    warning = function warning(condition, format) {\n      if (format === undefined) {\n        throw new Error('`warning(condition, format, ...args)` requires a warning ' + 'message argument');\n      }\n\n      if (format.indexOf('Failed Composite propType: ') === 0) {\n        return; // Ignore CompositeComponent proptype check.\n      }\n\n      if (!condition) {\n        for (var _len2 = arguments.length, args = Array(_len2 > 2 ? _len2 - 2 : 0), _key2 = 2; _key2 < _len2; _key2++) {\n          args[_key2 - 2] = arguments[_key2];\n        }\n\n        printWarning.apply(undefined, [format].concat(args));\n      }\n    };\n  })();\n}\n\nmodule.exports = warning;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/fbjs/lib/warning.js\n// module id = 8\n// module chunks = 0 1", "/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/prop-types/lib/ReactPropTypesSecret.js\n// module id = 9\n// module chunks = 0 1", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport autobind from 'autobind-decorator';\nimport * as valueTransformer from './value-transformer';\nimport DEFAULT_CLASS_NAMES from './default-class-names';\nimport Label from './label';\nimport rangePropType from './range-prop-type';\nimport valuePropType from './value-prop-type';\nimport Slider from './slider';\nimport Track from './track';\nimport { captialize, distanceTo, isDefined, isObject, length } from '../utils';\nimport { DOWN_ARROW, LEFT_ARROW, RIGHT_ARROW, UP_ARROW } from './key-codes';\n\n/**\n * A React component that allows users to input numeric values within a range\n * by dragging its sliders.\n */\nexport default class InputRange extends React.Component {\n  /**\n   * @ignore\n   * @override\n   * @return {Object}\n   */\n  static get propTypes() {\n    return {\n      allowSameValues: PropTypes.bool,\n      ariaLabelledby: PropTypes.string,\n      ariaControls: PropTypes.string,\n      classNames: PropTypes.objectOf(PropTypes.string),\n      disabled: PropTypes.bool,\n      draggableTrack: PropTypes.bool,\n      formatLabel: PropTypes.func,\n      maxValue: rangePropType,\n      minValue: rangePropType,\n      name: PropTypes.string,\n      onChangeStart: PropTypes.func,\n      onChange: PropTypes.func.isRequired,\n      onChangeComplete: PropTypes.func,\n      step: PropTypes.number,\n      value: valuePropType,\n    };\n  }\n\n  /**\n   * @ignore\n   * @override\n   * @return {Object}\n   */\n  static get defaultProps() {\n    return {\n      allowSameValues: false,\n      classNames: DEFAULT_CLASS_NAMES,\n      disabled: false,\n      maxValue: 10,\n      minValue: 0,\n      step: 1,\n    };\n  }\n\n  /**\n   * @param {Object} props\n   * @param {boolean} [props.allowSameValues]\n   * @param {string} [props.ariaLabelledby]\n   * @param {string} [props.ariaControls]\n   * @param {InputRangeClassNames} [props.classNames]\n   * @param {boolean} [props.disabled = false]\n   * @param {Function} [props.formatLabel]\n   * @param {number|Range} [props.maxValue = 10]\n   * @param {number|Range} [props.minValue = 0]\n   * @param {string} [props.name]\n   * @param {string} props.onChange\n   * @param {Function} [props.onChangeComplete]\n   * @param {Function} [props.onChangeStart]\n   * @param {number} [props.step = 1]\n   * @param {number|Range} props.value\n   */\n  constructor(props) {\n    super(props);\n\n    /**\n     * @private\n     * @type {?number}\n     */\n    this.startValue = null;\n\n    /**\n     * @private\n     * @type {?Component}\n     */\n    this.node = null;\n\n    /**\n     * @private\n     * @type {?Component}\n     */\n    this.trackNode = null;\n\n    /**\n     * @private\n     * @type {bool}\n     */\n    this.isSliderDragging = false;\n\n    /**\n     * @private\n     * @type {?string}\n     */\n    this.lastKeyMoved = null;\n  }\n\n  /**\n   * @ignore\n   * @override\n   * @return {void}\n   */\n  componentWillUnmount() {\n    this.removeDocumentMouseUpListener();\n    this.removeDocumentTouchEndListener();\n  }\n\n  /**\n   * Return the CSS class name of the component\n   * @private\n   * @return {string}\n   */\n  getComponentClassName() {\n    if (!this.props.disabled) {\n      return this.props.classNames.inputRange;\n    }\n\n    return this.props.classNames.disabledInputRange;\n  }\n\n  /**\n   * Return the bounding rect of the track\n   * @private\n   * @return {ClientRect}\n   */\n  getTrackClientRect() {\n    return this.trackNode.getClientRect();\n  }\n\n  /**\n   * Return the slider key closest to a point\n   * @private\n   * @param {Point} position\n   * @return {string}\n   */\n  getKeyByPosition(position) {\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n    const positions = valueTransformer.getPositionsFromValues(values, this.props.minValue, this.props.maxValue, this.getTrackClientRect());\n\n    if (this.isMultiValue()) {\n      const distanceToMin = distanceTo(position, positions.min);\n      const distanceToMax = distanceTo(position, positions.max);\n\n      if (distanceToMin < distanceToMax) {\n        return 'min';\n      }\n    }\n\n    return 'max';\n  }\n\n  /**\n   * Return all the slider keys\n   * @private\n   * @return {string[]}\n   */\n  getKeys() {\n    if (this.isMultiValue()) {\n      return ['min', 'max'];\n    }\n\n    return ['max'];\n  }\n\n  /**\n   * Return true if the difference between the new and the current value is\n   * greater or equal to the step amount of the component\n   * @private\n   * @param {Range} values\n   * @return {boolean}\n   */\n  hasStepDifference(values) {\n    const currentValues = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n\n    return length(values.min, currentValues.min) >= this.props.step ||\n           length(values.max, currentValues.max) >= this.props.step;\n  }\n\n  /**\n   * Return true if the component accepts a min and max value\n   * @private\n   * @return {boolean}\n   */\n  isMultiValue() {\n    return isObject(this.props.value);\n  }\n\n  /**\n   * Return true if the range is within the max and min value of the component\n   * @private\n   * @param {Range} values\n   * @return {boolean}\n   */\n  isWithinRange(values) {\n    if (this.isMultiValue()) {\n      return values.min >= this.props.minValue &&\n             values.max <= this.props.maxValue &&\n             this.props.allowSameValues\n              ? values.min <= values.max\n              : values.min < values.max;\n    }\n\n    return values.max >= this.props.minValue && values.max <= this.props.maxValue;\n  }\n\n  /**\n   * Return true if the new value should trigger a render\n   * @private\n   * @param {Range} values\n   * @return {boolean}\n   */\n  shouldUpdate(values) {\n    return this.isWithinRange(values) && this.hasStepDifference(values);\n  }\n\n  /**\n   * Update the position of a slider\n   * @private\n   * @param {string} key\n   * @param {Point} position\n   * @return {void}\n   */\n  updatePosition(key, position) {\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n    const positions = valueTransformer.getPositionsFromValues(values, this.props.minValue, this.props.maxValue, this.getTrackClientRect());\n\n    positions[key] = position;\n    this.lastKeyMoved = key;\n\n    this.updatePositions(positions);\n  }\n\n  /**\n   * Update the positions of multiple sliders\n   * @private\n   * @param {Object} positions\n   * @param {Point} positions.min\n   * @param {Point} positions.max\n   * @return {void}\n   */\n  updatePositions(positions) {\n    const values = {\n      min: valueTransformer.getValueFromPosition(positions.min, this.props.minValue, this.props.maxValue, this.getTrackClientRect()),\n      max: valueTransformer.getValueFromPosition(positions.max, this.props.minValue, this.props.maxValue, this.getTrackClientRect()),\n    };\n\n    const transformedValues = {\n      min: valueTransformer.getStepValueFromValue(values.min, this.props.step),\n      max: valueTransformer.getStepValueFromValue(values.max, this.props.step),\n    };\n\n    this.updateValues(transformedValues);\n  }\n\n  /**\n   * Update the value of a slider\n   * @private\n   * @param {string} key\n   * @param {number} value\n   * @return {void}\n   */\n  updateValue(key, value) {\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n\n    values[key] = value;\n\n    this.updateValues(values);\n  }\n\n  /**\n   * Update the values of multiple sliders\n   * @private\n   * @param {Range|number} values\n   * @return {void}\n   */\n  updateValues(values) {\n    if (!this.shouldUpdate(values)) {\n      return;\n    }\n\n    this.props.onChange(this.isMultiValue() ? values : values.max);\n  }\n\n  /**\n   * Increment the value of a slider by key name\n   * @private\n   * @param {string} key\n   * @return {void}\n   */\n  incrementValue(key) {\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n    const value = values[key] + this.props.step;\n\n    this.updateValue(key, value);\n  }\n\n  /**\n   * Decrement the value of a slider by key name\n   * @private\n   * @param {string} key\n   * @return {void}\n   */\n  decrementValue(key) {\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n    const value = values[key] - this.props.step;\n\n    this.updateValue(key, value);\n  }\n\n  /**\n   * Listen to mouseup event\n   * @private\n   * @return {void}\n   */\n  addDocumentMouseUpListener() {\n    this.removeDocumentMouseUpListener();\n    this.node.ownerDocument.addEventListener('mouseup', this.handleMouseUp);\n  }\n\n  /**\n   * Listen to touchend event\n   * @private\n   * @return {void}\n   */\n  addDocumentTouchEndListener() {\n    this.removeDocumentTouchEndListener();\n    this.node.ownerDocument.addEventListener('touchend', this.handleTouchEnd);\n  }\n\n  /**\n   * Stop listening to mouseup event\n   * @private\n   * @return {void}\n   */\n  removeDocumentMouseUpListener() {\n    this.node.ownerDocument.removeEventListener('mouseup', this.handleMouseUp);\n  }\n\n  /**\n   * Stop listening to touchend event\n   * @private\n   * @return {void}\n   */\n  removeDocumentTouchEndListener() {\n    this.node.ownerDocument.removeEventListener('touchend', this.handleTouchEnd);\n  }\n\n  /**\n   * Handle any \"mousemove\" event received by the slider\n   * @private\n   * @param {SyntheticEvent} event\n   * @param {string} key\n   * @return {void}\n   */\n  @autobind\n  handleSliderDrag(event, key) {\n    if (this.props.disabled) {\n      return;\n    }\n\n    const position = valueTransformer.getPositionFromEvent(event, this.getTrackClientRect());\n    this.isSliderDragging = true;\n    requestAnimationFrame(() => this.updatePosition(key, position));\n  }\n\n  /**\n   * Handle any \"mousemove\" event received by the track\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleTrackDrag(event, prevEvent) {\n    if (this.props.disabled || !this.props.draggableTrack || this.isSliderDragging) {\n      return;\n    }\n\n    const {\n      maxValue,\n      minValue,\n      value: { max, min },\n    } = this.props;\n\n    const position = valueTransformer.getPositionFromEvent(event, this.getTrackClientRect());\n    const value = valueTransformer.getValueFromPosition(position, minValue, maxValue, this.getTrackClientRect());\n    const stepValue = valueTransformer.getStepValueFromValue(value, this.props.step);\n\n    const prevPosition = valueTransformer.getPositionFromEvent(prevEvent, this.getTrackClientRect());\n    const prevValue = valueTransformer.getValueFromPosition(prevPosition, minValue, maxValue, this.getTrackClientRect());\n    const prevStepValue = valueTransformer.getStepValueFromValue(prevValue, this.props.step);\n\n    const offset = prevStepValue - stepValue;\n\n    const transformedValues = {\n      min: min - offset,\n      max: max - offset,\n    };\n\n    this.updateValues(transformedValues);\n  }\n\n  /**\n   * Handle any \"keydown\" event received by the slider\n   * @private\n   * @param {SyntheticEvent} event\n   * @param {string} key\n   * @return {void}\n   */\n  @autobind\n  handleSliderKeyDown(event, key) {\n    if (this.props.disabled) {\n      return;\n    }\n\n    switch (event.keyCode) {\n    case LEFT_ARROW:\n    case DOWN_ARROW:\n      event.preventDefault();\n      this.decrementValue(key);\n      break;\n\n    case RIGHT_ARROW:\n    case UP_ARROW:\n      event.preventDefault();\n      this.incrementValue(key);\n      break;\n\n    default:\n      break;\n    }\n  }\n\n  /**\n   * Handle any \"mousedown\" event received by the track\n   * @private\n   * @param {SyntheticEvent} event\n   * @param {Point} position\n   * @return {void}\n   */\n  @autobind\n  handleTrackMouseDown(event, position) {\n    if (this.props.disabled) {\n      return;\n    }\n\n    const {\n      maxValue,\n      minValue,\n      value: { max, min },\n    } = this.props;\n\n    event.preventDefault();\n\n    const value = valueTransformer.getValueFromPosition(position, minValue, maxValue, this.getTrackClientRect());\n    const stepValue = valueTransformer.getStepValueFromValue(value, this.props.step);\n\n    if (!this.props.draggableTrack || stepValue > max || stepValue < min) {\n      this.updatePosition(this.getKeyByPosition(position), position);\n    }\n  }\n\n  /**\n   * Handle the start of any mouse/touch event\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleInteractionStart() {\n    if (this.props.onChangeStart) {\n      this.props.onChangeStart(this.props.value);\n    }\n\n    if (this.props.onChangeComplete && !isDefined(this.startValue)) {\n      this.startValue = this.props.value;\n    }\n  }\n\n  /**\n   * Handle the end of any mouse/touch event\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleInteractionEnd() {\n    if (this.isSliderDragging) {\n      this.isSliderDragging = false;\n    }\n\n    if (!this.props.onChangeComplete || !isDefined(this.startValue)) {\n      return;\n    }\n\n    if (this.startValue !== this.props.value) {\n      this.props.onChangeComplete(this.props.value);\n    }\n\n    this.startValue = null;\n  }\n\n  /**\n   * Handle any \"keydown\" event received by the component\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleKeyDown(event) {\n    this.handleInteractionStart(event);\n  }\n\n  /**\n   * Handle any \"keyup\" event received by the component\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleKeyUp(event) {\n    this.handleInteractionEnd(event);\n  }\n\n  /**\n   * Handle any \"mousedown\" event received by the component\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleMouseDown(event) {\n    this.handleInteractionStart(event);\n    this.addDocumentMouseUpListener();\n  }\n\n  /**\n   * Handle any \"mouseup\" event received by the component\n   * @private\n   * @param {SyntheticEvent} event\n   */\n  @autobind\n  handleMouseUp(event) {\n    this.handleInteractionEnd(event);\n    this.removeDocumentMouseUpListener();\n  }\n\n  /**\n   * Handle any \"touchstart\" event received by the component\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleTouchStart(event) {\n    this.handleInteractionStart(event);\n    this.addDocumentTouchEndListener();\n  }\n\n  /**\n   * Handle any \"touchend\" event received by the component\n   * @private\n   * @param {SyntheticEvent} event\n   */\n  @autobind\n  handleTouchEnd(event) {\n    this.handleInteractionEnd(event);\n    this.removeDocumentTouchEndListener();\n  }\n\n  /**\n   * Return JSX of sliders\n   * @private\n   * @return {JSX.Element}\n   */\n  renderSliders() {\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n    const percentages = valueTransformer.getPercentagesFromValues(values, this.props.minValue, this.props.maxValue);\n    const keys = this.props.allowSameValues &&\n      this.lastKeyMoved === 'min'\n      ? this.getKeys().reverse()\n      : this.getKeys();\n\n    return keys.map((key) => {\n      const value = values[key];\n      const percentage = percentages[key];\n\n      let { maxValue, minValue } = this.props;\n\n      if (key === 'min') {\n        maxValue = values.max;\n      } else {\n        minValue = values.min;\n      }\n\n      const slider = (\n        <Slider\n          ariaLabelledby={this.props.ariaLabelledby}\n          ariaControls={this.props.ariaControls}\n          classNames={this.props.classNames}\n          formatLabel={this.props.formatLabel}\n          key={key}\n          maxValue={maxValue}\n          minValue={minValue}\n          onSliderDrag={this.handleSliderDrag}\n          onSliderKeyDown={this.handleSliderKeyDown}\n          percentage={percentage}\n          type={key}\n          value={value} />\n      );\n\n      return slider;\n    });\n  }\n\n  /**\n   * Return JSX of hidden inputs\n   * @private\n   * @return {JSX.Element}\n   */\n  renderHiddenInputs() {\n    if (!this.props.name) {\n      return [];\n    }\n\n    const isMultiValue = this.isMultiValue();\n    const values = valueTransformer.getValueFromProps(this.props, isMultiValue);\n\n    return this.getKeys().map((key) => {\n      const value = values[key];\n      const name = isMultiValue ? `${this.props.name}${captialize(key)}` : this.props.name;\n\n      return (\n        <input key={key} type=\"hidden\" name={name} value={value} />\n      );\n    });\n  }\n\n  /**\n   * @ignore\n   * @override\n   * @return {JSX.Element}\n   */\n  render() {\n    const componentClassName = this.getComponentClassName();\n    const values = valueTransformer.getValueFromProps(this.props, this.isMultiValue());\n    const percentages = valueTransformer.getPercentagesFromValues(values, this.props.minValue, this.props.maxValue);\n\n    return (\n      <div\n        aria-disabled={this.props.disabled}\n        ref={(node) => { this.node = node; }}\n        className={componentClassName}\n        onKeyDown={this.handleKeyDown}\n        onKeyUp={this.handleKeyUp}\n        onMouseDown={this.handleMouseDown}\n        onTouchStart={this.handleTouchStart}>\n        <Label\n          classNames={this.props.classNames}\n          formatLabel={this.props.formatLabel}\n          type=\"min\">\n          {this.props.minValue}\n        </Label>\n\n        <Track\n          classNames={this.props.classNames}\n          draggableTrack={this.props.draggableTrack}\n          ref={(trackNode) => { this.trackNode = trackNode; }}\n          percentages={percentages}\n          onTrackDrag={this.handleTrackDrag}\n          onTrackMouseDown={this.handleTrackMouseDown}>\n\n          {this.renderSliders()}\n        </Track>\n\n        <Label\n          classNames={this.props.classNames}\n          formatLabel={this.props.formatLabel}\n          type=\"max\">\n          {this.props.maxValue}\n        </Label>\n\n        {this.renderHiddenInputs()}\n      </div>\n    );\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/input-range.jsx", "import InputRange from './input-range/input-range';\n\n/**\n * @ignore\n * @typedef {Object} ClientRect\n * @property {number} height\n * @property {number} left\n * @property {number} top\n * @property {number} width\n */\n\n/**\n * @typedef {Object} InputRangeClassNames\n * @property {string} activeTrack\n * @property {string} disabledInputRange\n * @property {string} inputRange\n * @property {string} labelContainer\n * @property {string} maxLabel\n * @property {string} minLabel\n * @property {string} slider\n * @property {string} sliderContainer\n * @property {string} track\n * @property {string} valueLabel\n */\n\n/**\n * @typedef {Function} LabelFormatter\n * @param {number} value\n * @param {string} type\n * @return {string}\n */\n\n/**\n * @ignore\n * @typedef {Object} Point\n * @property {number} x\n * @property {number} y\n */\n\n/**\n * @typedef {Object} Range\n * @property {number} min - Min value\n * @property {number} max - Max value\n */\n\nexport default InputRange;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/index.js", "/**\n * Default CSS class names\n * @ignore\n * @type {InputRangeClassNames}\n */\nconst DEFAULT_CLASS_NAMES = {\n  activeTrack: 'input-range__track input-range__track--active',\n  disabledInputRange: 'input-range input-range--disabled',\n  inputRange: 'input-range',\n  labelContainer: 'input-range__label-container',\n  max<PERSON>abel: 'input-range__label input-range__label--max',\n  minLabel: 'input-range__label input-range__label--min',\n  slider: 'input-range__slider',\n  sliderContainer: 'input-range__slider-container',\n  track: 'input-range__track input-range__track--background',\n  valueLabel: 'input-range__label input-range__label--value',\n};\n\nexport default DEFAULT_CLASS_NAMES;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/default-class-names.js", "/** @ignore */\nexport const DOWN_ARROW = 40;\n\n/** @ignore */\nexport const LEFT_ARROW = 37;\n\n/** @ignore */\nexport const RIGHT_ARROW = 39;\n\n/** @ignore */\nexport const UP_ARROW = 38;\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/key-codes.js", "import { isNumber } from '../utils';\n\n/**\n * @ignore\n * @param {Object} props - React component props\n * @return {?Error} Return Error if validation fails\n */\nexport default function rangePropType(props) {\n  const { maxValue, minValue } = props;\n\n  if (!isNumber(minValue) || !isNumber(maxValue)) {\n    return new Error('\"minValue\" and \"maxValue\" must be a number');\n  }\n\n  if (minValue >= maxValue) {\n    return new Error('\"minValue\" must be smaller than \"maxValue\"');\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/range-prop-type.js", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport autobind from 'autobind-decorator';\nimport Label from './label';\n\n/**\n * @ignore\n */\nexport default class Slider extends React.Component {\n  /**\n   * Accepted propTypes of Slider\n   * @override\n   * @return {Object}\n   * @property {Function} ariaLabelledby\n   * @property {Function} ariaControls\n   * @property {Function} className\n   * @property {Function} formatLabel\n   * @property {Function} maxValue\n   * @property {Function} minValue\n   * @property {Function} onSliderDrag\n   * @property {Function} onSliderKeyDown\n   * @property {Function} percentage\n   * @property {Function} type\n   * @property {Function} value\n   */\n  static get propTypes() {\n    return {\n      ariaLabelledby: PropTypes.string,\n      ariaControls: PropTypes.string,\n      classNames: PropTypes.objectOf(PropTypes.string).isRequired,\n      formatLabel: PropTypes.func,\n      maxValue: PropTypes.number,\n      minValue: PropTypes.number,\n      onSliderDrag: PropTypes.func.isRequired,\n      onSliderKeyDown: PropTypes.func.isRequired,\n      percentage: PropTypes.number.isRequired,\n      type: PropTypes.string.isRequired,\n      value: PropTypes.number.isRequired,\n    };\n  }\n\n  /**\n   * @param {Object} props\n   * @param {string} [props.ariaLabelledby]\n   * @param {string} [props.ariaControls]\n   * @param {InputRangeClassNames} props.classNames\n   * @param {Function} [props.formatLabel]\n   * @param {number} [props.maxValue]\n   * @param {number} [props.minValue]\n   * @param {Function} props.onSliderKeyDown\n   * @param {Function} props.onSliderDrag\n   * @param {number} props.percentage\n   * @param {number} props.type\n   * @param {number} props.value\n   */\n  constructor(props) {\n    super(props);\n\n    /**\n     * @private\n     * @type {?Component}\n     */\n    this.node = null;\n  }\n\n  /**\n   * @ignore\n   * @override\n   * @return {void}\n   */\n  componentWillUnmount() {\n    this.removeDocumentMouseMoveListener();\n    this.removeDocumentMouseUpListener();\n    this.removeDocumentTouchEndListener();\n    this.removeDocumentTouchMoveListener();\n  }\n\n  /**\n   * @private\n   * @return {Object}\n   */\n  getStyle() {\n    const perc = (this.props.percentage || 0) * 100;\n    const style = {\n      position: 'absolute',\n      left: `${perc}%`,\n    };\n\n    return style;\n  }\n\n  /**\n   * Listen to mousemove event\n   * @private\n   * @return {void}\n   */\n  addDocumentMouseMoveListener() {\n    this.removeDocumentMouseMoveListener();\n    this.node.ownerDocument.addEventListener('mousemove', this.handleMouseMove);\n  }\n\n  /**\n   * Listen to mouseup event\n   * @private\n   * @return {void}\n   */\n  addDocumentMouseUpListener() {\n    this.removeDocumentMouseUpListener();\n    this.node.ownerDocument.addEventListener('mouseup', this.handleMouseUp);\n  }\n\n  /**\n   * Listen to touchmove event\n   * @private\n   * @return {void}\n   */\n  addDocumentTouchMoveListener() {\n    this.removeDocumentTouchMoveListener();\n    this.node.ownerDocument.addEventListener('touchmove', this.handleTouchMove);\n  }\n\n  /**\n   * Listen to touchend event\n   * @private\n   * @return {void}\n   */\n  addDocumentTouchEndListener() {\n    this.removeDocumentTouchEndListener();\n    this.node.ownerDocument.addEventListener('touchend', this.handleTouchEnd);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  removeDocumentMouseMoveListener() {\n    this.node.ownerDocument.removeEventListener('mousemove', this.handleMouseMove);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  removeDocumentMouseUpListener() {\n    this.node.ownerDocument.removeEventListener('mouseup', this.handleMouseUp);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  removeDocumentTouchMoveListener() {\n    this.node.ownerDocument.removeEventListener('touchmove', this.handleTouchMove);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  removeDocumentTouchEndListener() {\n    this.node.ownerDocument.removeEventListener('touchend', this.handleTouchEnd);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleMouseDown() {\n    this.addDocumentMouseMoveListener();\n    this.addDocumentMouseUpListener();\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleMouseUp() {\n    this.removeDocumentMouseMoveListener();\n    this.removeDocumentMouseUpListener();\n  }\n\n  /**\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleMouseMove(event) {\n    this.props.onSliderDrag(event, this.props.type);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleTouchStart() {\n    this.addDocumentTouchEndListener();\n    this.addDocumentTouchMoveListener();\n  }\n\n  /**\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleTouchMove(event) {\n    this.props.onSliderDrag(event, this.props.type);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleTouchEnd() {\n    this.removeDocumentTouchMoveListener();\n    this.removeDocumentTouchEndListener();\n  }\n\n  /**\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleKeyDown(event) {\n    this.props.onSliderKeyDown(event, this.props.type);\n  }\n\n  /**\n   * @override\n   * @return {JSX.Element}\n   */\n  render() {\n    const style = this.getStyle();\n\n    return (\n      <span\n        className={this.props.classNames.sliderContainer}\n        ref={(node) => { this.node = node; }}\n        style={style}>\n        <Label\n          classNames={this.props.classNames}\n          formatLabel={this.props.formatLabel}\n          type=\"value\">\n          {this.props.value}\n        </Label>\n\n        <div\n          aria-labelledby={this.props.ariaLabelledby}\n          aria-controls={this.props.ariaControls}\n          aria-valuemax={this.props.maxValue}\n          aria-valuemin={this.props.minValue}\n          aria-valuenow={this.props.value}\n          className={this.props.classNames.slider}\n          draggable=\"false\"\n          onKeyDown={this.handleKeyDown}\n          onMouseDown={this.handleMouseDown}\n          onTouchStart={this.handleTouchStart}\n          role=\"slider\"\n          tabIndex=\"0\" />\n      </span>\n    );\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/slider.jsx", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport autobind from 'autobind-decorator';\n\n/**\n * @ignore\n */\nexport default class Track extends React.Component {\n  /**\n   * @override\n   * @return {Object}\n   * @property {Function} children\n   * @property {Function} classNames\n   * @property {Boolean} draggableTrack\n   * @property {Function} onTrackDrag\n   * @property {Function} onTrackMouseDown\n   * @property {Function} percentages\n   */\n  static get propTypes() {\n    return {\n      children: PropTypes.node.isRequired,\n      classNames: PropTypes.objectOf(PropTypes.string).isRequired,\n      draggableTrack: PropTypes.bool,\n      onTrackDrag: PropTypes.func,\n      onTrackMouseDown: PropTypes.func.isRequired,\n      percentages: PropTypes.objectOf(PropTypes.number).isRequired,\n    };\n  }\n\n  /**\n   * @param {Object} props\n   * @param {InputRangeClassNames} props.classNames\n   * @param {Boolean} props.draggableTrack\n   * @param {Function} props.onTrackDrag\n   * @param {Function} props.onTrackMouseDown\n   * @param {number} props.percentages\n   */\n  constructor(props) {\n    super(props);\n\n    /**\n     * @private\n     * @type {?Component}\n     */\n    this.node = null;\n    this.trackDragEvent = null;\n  }\n\n  /**\n   * @private\n   * @return {ClientRect}\n   */\n  getClientRect() {\n    return this.node.getBoundingClientRect();\n  }\n\n  /**\n   * @private\n   * @return {Object} CSS styles\n   */\n  getActiveTrackStyle() {\n    const width = `${(this.props.percentages.max - this.props.percentages.min) * 100}%`;\n    const left = `${this.props.percentages.min * 100}%`;\n\n    return { left, width };\n  }\n\n  /**\n   * Listen to mousemove event\n   * @private\n   * @return {void}\n   */\n  addDocumentMouseMoveListener() {\n    this.removeDocumentMouseMoveListener();\n    this.node.ownerDocument.addEventListener('mousemove', this.handleMouseMove);\n  }\n\n  /**\n   * Listen to mouseup event\n   * @private\n   * @return {void}\n   */\n  addDocumentMouseUpListener() {\n    this.removeDocumentMouseUpListener();\n    this.node.ownerDocument.addEventListener('mouseup', this.handleMouseUp);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  removeDocumentMouseMoveListener() {\n    this.node.ownerDocument.removeEventListener('mousemove', this.handleMouseMove);\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  removeDocumentMouseUpListener() {\n    this.node.ownerDocument.removeEventListener('mouseup', this.handleMouseUp);\n  }\n\n  /**\n   * @private\n   * @param {SyntheticEvent} event\n   * @return {void}\n   */\n  @autobind\n  handleMouseMove(event) {\n    if (!this.props.draggableTrack) {\n      return;\n    }\n\n    if (this.trackDragEvent !== null) {\n      this.props.onTrackDrag(event, this.trackDragEvent);\n    }\n\n    this.trackDragEvent = event;\n  }\n\n  /**\n   * @private\n   * @return {void}\n   */\n  @autobind\n  handleMouseUp() {\n    if (!this.props.draggableTrack) {\n      return;\n    }\n\n    this.removeDocumentMouseMoveListener();\n    this.removeDocumentMouseUpListener();\n    this.trackDragEvent = null;\n  }\n\n  /**\n   * @private\n   * @param {SyntheticEvent} event - User event\n   */\n  @autobind\n  handleMouseDown(event) {\n    const clientX = event.touches ? event.touches[0].clientX : event.clientX;\n    const trackClientRect = this.getClientRect();\n    const position = {\n      x: clientX - trackClientRect.left,\n      y: 0,\n    };\n\n    this.props.onTrackMouseDown(event, position);\n\n    if (this.props.draggableTrack) {\n      this.addDocumentMouseMoveListener();\n      this.addDocumentMouseUpListener();\n    }\n  }\n\n  /**\n   * @private\n   * @param {SyntheticEvent} event - User event\n   */\n  @autobind\n  handleTouchStart(event) {\n    event.preventDefault();\n\n    this.handleMouseDown(event);\n  }\n\n  /**\n   * @override\n   * @return {JSX.Element}\n   */\n  render() {\n    const activeTrackStyle = this.getActiveTrackStyle();\n\n    return (\n      <div\n        className={this.props.classNames.track}\n        onMouseDown={this.handleMouseDown}\n        onTouchStart={this.handleTouchStart}\n        ref={(node) => { this.node = node; }}>\n        <div\n          style={activeTrackStyle}\n          className={this.props.classNames.activeTrack} />\n        {this.props.children}\n      </div>\n    );\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/track.jsx", "import { isNumber, isObject } from '../utils';\n\n/**\n * @ignore\n * @param {Object} props\n * @return {?Error} Return Error if validation fails\n */\nexport default function valuePropType(props, propName) {\n  const { maxValue, minValue } = props;\n  const value = props[propName];\n\n  if (!isNumber(value) && (!isObject(value) || !isNumber(value.min) || !isNumber(value.max))) {\n    return new Error(`\"${propName}\" must be a number or a range object`);\n  }\n\n  if (isNumber(value) && (value < minValue || value > maxValue)) {\n    return new Error(`\"${propName}\" must be in between \"minValue\" and \"maxValue\"`);\n  }\n\n  if (isObject(value) && (value.min < minValue || value.min > maxValue || value.max < minValue || value.max > maxValue)) {\n    return new Error(`\"${propName}\" must be in between \"minValue\" and \"maxValue\"`);\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/value-prop-type.js", "import { clamp } from '../utils';\n\n/**\n * Convert a point into a percentage value\n * @ignore\n * @param {Point} position\n * @param {ClientRect} clientRect\n * @return {number} Percentage value\n */\nexport function getPercentageFromPosition(position, clientRect) {\n  const length = clientRect.width;\n  const sizePerc = position.x / length;\n\n  return sizePerc || 0;\n}\n\n/**\n * Convert a point into a model value\n * @ignore\n * @param {Point} position\n * @param {number} minValue\n * @param {number} maxValue\n * @param {ClientRect} clientRect\n * @return {number}\n */\nexport function getValueFromPosition(position, minValue, maxValue, clientRect) {\n  const sizePerc = getPercentageFromPosition(position, clientRect);\n  const valueDiff = maxValue - minValue;\n\n  return minValue + (valueDiff * sizePerc);\n}\n\n/**\n * Convert props into a range value\n * @ignore\n * @param {Object} props\n * @param {boolean} isMultiValue\n * @return {Range}\n */\nexport function getValueFromProps(props, isMultiValue) {\n  if (isMultiValue) {\n    return { ...props.value };\n  }\n\n  return {\n    min: props.minValue,\n    max: props.value,\n  };\n}\n\n/**\n * Convert a model value into a percentage value\n * @ignore\n * @param {number} value\n * @param {number} minValue\n * @param {number} maxValue\n * @return {number}\n */\nexport function getPercentageFromValue(value, minValue, maxValue) {\n  const validValue = clamp(value, minValue, maxValue);\n  const valueDiff = maxValue - minValue;\n  const valuePerc = (validValue - minValue) / valueDiff;\n\n  return valuePerc || 0;\n}\n\n/**\n * Convert model values into percentage values\n * @ignore\n * @param {Range} values\n * @param {number} minValue\n * @param {number} maxValue\n * @return {Range}\n */\nexport function getPercentagesFromValues(values, minValue, maxValue) {\n  return {\n    min: getPercentageFromValue(values.min, minValue, maxValue),\n    max: getPercentageFromValue(values.max, minValue, maxValue),\n  };\n}\n\n/**\n * Convert a value into a point\n * @ignore\n * @param {number} value\n * @param {number} minValue\n * @param {number} maxValue\n * @param {ClientRect} clientRect\n * @return {Point} Position\n */\nexport function getPositionFromValue(value, minValue, maxValue, clientRect) {\n  const length = clientRect.width;\n  const valuePerc = getPercentageFromValue(value, minValue, maxValue);\n  const positionValue = valuePerc * length;\n\n  return {\n    x: positionValue,\n    y: 0,\n  };\n}\n\n/**\n * Convert a range of values into points\n * @ignore\n * @param {Range} values\n * @param {number} minValue\n * @param {number} maxValue\n * @param {ClientRect} clientRect\n * @return {Range}\n */\nexport function getPositionsFromValues(values, minValue, maxValue, clientRect) {\n  return {\n    min: getPositionFromValue(values.min, minValue, maxValue, clientRect),\n    max: getPositionFromValue(values.max, minValue, maxValue, clientRect),\n  };\n}\n\n/**\n * Convert an event into a point\n * @ignore\n * @param {Event} event\n * @param {ClientRect} clientRect\n * @return {Point}\n */\nexport function getPositionFromEvent(event, clientRect) {\n  const length = clientRect.width;\n  const { clientX } = event.touches ? event.touches[0] : event;\n\n  return {\n    x: clamp(clientX - clientRect.left, 0, length),\n    y: 0,\n  };\n}\n\n/**\n * Convert a value into a step value\n * @ignore\n * @param {number} value\n * @param {number} valuePerStep\n * @return {number}\n */\nexport function getStepValueFromValue(value, valuePerStep) {\n  return Math.round(value / valuePerStep) * valuePerStep;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/input-range/value-transformer.js", "/**\n * Captialize a string\n * @ignore\n * @param {string} string\n * @return {string}\n */\nexport default function captialize(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/captialize.js", "/**\n * Clamp a value between a min and max value\n * @ignore\n * @param {number} value\n * @param {number} min\n * @param {number} max\n * @return {number}\n */\nexport default function clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/clamp.js", "/**\n * Calculate the distance between pointA and pointB\n * @ignore\n * @param {Point} pointA\n * @param {Point} pointB\n * @return {number} Distance\n */\nexport default function distanceTo(pointA, pointB) {\n  const xDiff = (pointB.x - pointA.x) ** 2;\n  const yDiff = (pointB.y - pointA.y) ** 2;\n\n  return Math.sqrt(xDiff + yDiff);\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/distance-to.js", "/**\n * Check if a value is defined\n * @ignore\n * @param {*} value\n * @return {boolean}\n */\nexport default function isDefined(value) {\n  return value !== undefined && value !== null;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/is-defined.js", "/**\n * Check if a value is a number\n * @ignore\n * @param {*} value\n * @return {boolean}\n */\nexport default function isNumber(value) {\n  return typeof value === 'number';\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/is-number.js", "/**\n * Check if a value is an object\n * @ignore\n * @param {*} value\n * @return {boolean}\n */\nexport default function isObject(value) {\n  return value !== null && typeof value === 'object';\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/is-object.js", "/**\n * Calculate the absolute difference between two numbers\n * @ignore\n * @param {number} numA\n * @param {number} numB\n * @return {number}\n */\nexport default function length(numA, numB) {\n  return Math.abs(numA - numB);\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/js/utils/length.js", "/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== 'production') {\n  var invariant = require('fbjs/lib/invariant');\n  var warning = require('fbjs/lib/warning');\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (typeSpecs.hasOwnProperty(typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          invariant(typeof typeSpecs[typeSpecName] === 'function', '%s: %s type `%s` is invalid; it must be a function, usually from ' + 'React.PropTypes.', componentName || 'React class', location, typeSpecName);\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        warning(!error || error instanceof Error, '%s: type specification of %s `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error);\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          warning(false, 'Failed %s type: %s%s', location, error.message, stack != null ? stack : '');\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = checkPropTypes;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/prop-types/checkPropTypes.js\n// module id = 26\n// module chunks = 0 1", "/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n'use strict';\n\nvar emptyFunction = require('fbjs/lib/emptyFunction');\nvar invariant = require('fbjs/lib/invariant');\n\nmodule.exports = function() {\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  function shim() {\n    invariant(\n      false,\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  var ReactPropTypes = {\n    array: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim\n  };\n\n  ReactPropTypes.checkPropTypes = emptyFunction;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/prop-types/factoryWithThrowingShims.js\n// module id = 27\n// module chunks = 0 1", "/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n'use strict';\n\nvar emptyFunction = require('fbjs/lib/emptyFunction');\nvar invariant = require('fbjs/lib/invariant');\nvar warning = require('fbjs/lib/warning');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar checkPropTypes = require('./checkPropTypes');\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message) {\n    this.message = message;\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          invariant(\n            false,\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            warning(\n              false,\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `%s` prop on `%s`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.',\n              propFullName,\n              componentName\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunction.thatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      process.env.NODE_ENV !== 'production' ? warning(false, 'Invalid argument supplied to oneOf, expected an instance of array.') : void 0;\n      return emptyFunction.thatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues);\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + propValue + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (propValue.hasOwnProperty(key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? warning(false, 'Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunction.thatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        if (checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret) == null) {\n          return null;\n        }\n      }\n\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (!checker) {\n          continue;\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/prop-types/factoryWithTypeCheckers.js\n// module id = 28\n// module chunks = 0 1"], "sourceRoot": ""}