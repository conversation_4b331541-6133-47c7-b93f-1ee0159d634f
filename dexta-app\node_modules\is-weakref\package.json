{"name": "is-weakref", "version": "1.0.2", "description": "Is this value a JS WeakRef? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakref.git"}, "keywords": ["weakref", "weak", "ref", "finalization", "finalization registry"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakref/issues"}, "homepage": "https://github.com/inspect-js/is-weakref#readme", "devDependencies": {"@ljharb/eslint-config": "^20.0.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^8.4.1", "for-each": "^0.3.3", "nyc": "^10.3.2", "object-inspect": "^1.11.1", "safe-publish-latest": "^2.0.0", "tape": "^5.3.2"}, "dependencies": {"call-bind": "^1.0.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}