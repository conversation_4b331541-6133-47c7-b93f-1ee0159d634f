{"version": 3, "file": "PhoneInput.js", "names": ["React", "ReactHookFormInput", "PhoneInput_", "metadata", "metadataType", "createPhoneInput", "defaultMetadata", "PhoneInput", "ref", "rest", "forwardRef", "propTypes"], "sources": ["../../source/react-hook-form/PhoneInput.js"], "sourcesContent": ["import React from 'react'\r\n\r\nimport ReactHookFormInput from './ReactHookFormInput.js'\r\nimport PhoneInput_ from '../PhoneInputBrowser.js'\r\n\r\nimport { metadata as metadataType } from '../PropTypes.js'\r\n\r\nexport function createPhoneInput(defaultMetadata) {\r\n  let PhoneInput = ({\r\n    metadata = defaultMetadata,\r\n    ...rest\r\n  }, ref) => {\r\n    return (\r\n      <ReactHookFormInput\r\n        {...rest}\r\n        ref={ref}\r\n        metadata={metadata}\r\n        Component={PhoneInput_}\r\n      />\r\n    )\r\n  }\r\n\r\n  PhoneInput = React.forwardRef(PhoneInput)\r\n\r\n  PhoneInput.propTypes = {\r\n    metadata: metadataType\r\n  }\r\n\r\n  return PhoneInput\r\n}\r\n\r\nexport default createPhoneInput()"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,WAAP,MAAwB,yBAAxB;AAEA,SAASC,QAAQ,IAAIC,YAArB,QAAyC,iBAAzC;AAEA,OAAO,SAASC,gBAAT,CAA0BC,eAA1B,EAA2C;EAChD,IAAIC,UAAU,GAAG,0BAGdC,GAHc,EAGN;IAAA,yBAFTL,QAES;IAAA,IAFTA,QAES,8BAFEG,eAEF;IAAA,IADNG,IACM;;IACT,oBACE,oBAAC,kBAAD,eACMA,IADN;MAEE,GAAG,EAAED,GAFP;MAGE,QAAQ,EAAEL,QAHZ;MAIE,SAAS,EAAED;IAJb,GADF;EAQD,CAZD;;EAcAK,UAAU,gBAAGP,KAAK,CAACU,UAAN,CAAiBH,UAAjB,CAAb;EAEAA,UAAU,CAACI,SAAX,GAAuB;IACrBR,QAAQ,EAAEC;EADW,CAAvB;EAIA,OAAOG,UAAP;AACD;AAED,eAAeF,gBAAgB,EAA/B"}