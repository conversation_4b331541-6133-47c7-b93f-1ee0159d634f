{"name": "is-weakmap", "version": "2.0.1", "description": "Is this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "node test", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\"", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakmap.git"}, "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakmap/issues"}, "homepage": "https://github.com/inspect-js/is-weakmap#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.7.2", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.12.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}}