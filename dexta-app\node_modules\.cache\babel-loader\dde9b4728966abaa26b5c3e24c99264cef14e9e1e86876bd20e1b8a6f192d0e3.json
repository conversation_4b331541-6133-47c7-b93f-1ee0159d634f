{"ast": null, "code": "var _jsxFileName = \"D:\\\\CodeFreaks\\\\dexta-app\\\\src\\\\Components\\\\Excel\\\\Excelmain.js\",\n  _s = $RefreshSig$();\nimport { createUniver, defaultTheme, LocaleType, merge } from \"@univerjs/presets\";\nimport { UniverSheetsCorePreset } from \"@univerjs/presets/preset-sheets-core\";\nimport UniverPresetSheetsCoreEnUS from \"@univerjs/presets/preset-sheets-core/locales/en-US\";\nimport \"./style.css\";\nimport \"@univerjs/presets/lib/styles/preset-sheets-core.css\";\nimport { BooleanNumber, SheetTypes } from \"@univerjs/core\";\nimport { LocaleType as CoreLocaleType } from \"@univerjs/core\";\nimport { useCallback, useEffect, useRef, useState, useImperativeHandle, forwardRef } from \"react\";\nimport { ToastContainer, Zoom } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { Currencies } from \"../Modals/data\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction debounce(func, delay) {\n  let timeoutId;\n  return function (...args) {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => func.apply(this, args), delay);\n  };\n}\n\n// Helper function to detect currency symbol in a string\nconst detectCurrencySymbol = value => {\n  if (typeof value !== \"string\") return null;\n  for (const currency of Currencies) {\n    if (value.includes(currency.sign)) {\n      return currency.sign;\n    }\n  }\n  return null;\n};\n\n// Helper function to get currency format pattern\nconst getCurrencyFormatPattern = currencySymbol => {\n  if (!currencySymbol) return null;\n\n  // Handle special cases for multi-character currency symbols\n  if (currencySymbol === \"SAR\") {\n    return '\"SAR\"#,##0';\n  }\n\n  // For single character symbols, wrap in quotes\n  return `\"${currencySymbol}\"#,##0`;\n};\n\n// Helper function to clean currency symbols from string values\nconst cleanCurrencyValue = value => {\n  if (typeof value !== \"string\") return value;\n  let cleaned = value;\n  // Remove all known currency symbols\n  for (const currency of Currencies) {\n    cleaned = cleaned.replace(new RegExp(currency.sign.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"g\"), \"\");\n  }\n\n  // Remove commas and trim\n  cleaned = cleaned.replace(/,/g, \"\").trim();\n  return cleaned;\n};\nexport const letterToColumn = letter => {\n  let col = 0;\n  for (let i = 0; i < letter.length; i++) {\n    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);\n  }\n  return col - 1;\n};\nexport const columnToLetter = col => {\n  let letter = \"\";\n  while (col >= 0) {\n    letter = String.fromCharCode(col % 26 + 65) + letter;\n    col = Math.floor(col / 26) - 1;\n  }\n  return letter;\n};\nexport const cellRefToIndices = cellRef => {\n  const match = cellRef.match(/([A-Z]+)(\\d+)/);\n  if (!match) return null;\n  const [, colLetter, rowNumber] = match;\n  const rowIndex = parseInt(rowNumber, 10) - 1;\n  const colIndex = letterToColumn(colLetter);\n  return {\n    rowIndex,\n    colIndex\n  };\n};\nexport const transformApiToMatrix = apiData => {\n  const cellData = {};\n  let maxRow = 0;\n  let maxCol = 0;\n  if (apiData !== null && apiData !== void 0 && apiData.excelData) {\n    Object === null || Object === void 0 ? void 0 : Object.entries(apiData.excelData).forEach(([cell, value]) => {\n      const indices = cellRefToIndices(cell);\n      if (!indices) return;\n      const {\n        rowIndex,\n        colIndex\n      } = indices;\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\n      if (typeof value === \"string\" && value.startsWith(\"=\")) {\n        cellData[rowIndex][colIndex] = {\n          f: value,\n          s: {\n            ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n          }\n        };\n      } else {\n        cellData[rowIndex][colIndex] = {\n          v: value,\n          s: {\n            ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n          }\n        };\n      }\n\n      maxRow = Math.max(maxRow, rowIndex);\n      maxCol = Math.max(maxCol, colIndex);\n    });\n  }\n  if (Array !== null && Array !== void 0 && Array.isArray(apiData === null || apiData === void 0 ? void 0 : apiData.maskedCells)) {\n    apiData.maskedCells.forEach(cellRef => {\n      const indices = cellRefToIndices(cellRef);\n      if (!indices) return;\n      const {\n        rowIndex,\n        colIndex\n      } = indices;\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\n      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};\n      cellData[rowIndex][colIndex].s = {\n        ...(cellData[rowIndex][colIndex].s || {}),\n        bg: {\n          rgb: \"#FFFF00\"\n        },\n        ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n      };\n\n      if (apiData !== null && apiData !== void 0 && apiData.highlightAllMaskedCells) {\n        cellData[rowIndex][colIndex].v = \"\";\n      }\n    });\n  }\n  return {\n    id: `workbook-${Date.now()}`,\n    name: \"universheet\",\n    sheetOrder: [\"sheet-01\"],\n    sheets: {\n      \"sheet-01\": {\n        type: SheetTypes.GRID,\n        id: \"sheet-01\",\n        name: \"Sheet 1\",\n        cellData,\n        rowCount: Math.max(maxRow + 1, 20),\n        columnCount: Math.max(maxCol + 1, 20),\n        defaultRowHeight: 28,\n        defaultColumnWidth: 93\n      }\n    }\n  };\n};\nexport const transformMatrixToApi = (cellData, setExcelCellMatrix, userInteractedCells = new Set(), maskedCells = [], existingCellMatrix = null) => {\n  console.log(cellData, \"cell data\");\n  const structuredMatrix = JSON.parse(JSON.stringify(existingCellMatrix || {}));\n  const apiExcelData = {};\n\n  // Log what cells are currently tracked as user-interacted\n  console.log(\"User-interacted cells before processing:\", Array.from(userInteractedCells));\n  if (!cellData) return {\n    cellMatrix: structuredMatrix,\n    excelData: apiExcelData\n  };\n  Object.entries(cellData).forEach(([rowIndex, row]) => {\n    Object.entries(row).forEach(([colIndex, cell]) => {\n      if (!structuredMatrix[rowIndex]) structuredMatrix[rowIndex] = {};\n      structuredMatrix[rowIndex][colIndex] = {\n        ...cell\n      };\n      const colLetter = columnToLetter(parseInt(colIndex));\n      const rowNumber = parseInt(rowIndex) + 1;\n      const ref = `${colLetter}${rowNumber}`;\n      const isMaskedCell = maskedCells.includes(ref);\n      if (isMaskedCell) {\n        console.log(\"ahhhahaha\");\n        if (userInteractedCells.has(ref)) {\n          const isZeroWithoutFormula = (cell.v === 0 || cell.v === \"0\") && !cell.f && (!cell.m || cell.m === \"\");\n          if (isZeroWithoutFormula) {\n            console.log(`⛔ Skipping ${ref}: zero with no formula or display value`);\n            return;\n          }\n          const isNumber = typeof cell.v === \"number\" || !isNaN(cell.v) && cell.t === 2;\n          const finalValue = isNumber ? Number(cell.v) : String(cell.v);\n          apiExcelData[ref] = finalValue;\n          console.log(`✅ Including masked cell ${ref}:`, finalValue, `formula:`, cell.f || \"none\");\n        }\n      } else {\n        if ((cell === null || cell === void 0 ? void 0 : cell.v) !== undefined && cell.v !== null && cell.v !== \"\" || (cell === null || cell === void 0 ? void 0 : cell.v) === 0 && cell !== null && cell !== void 0 && cell.f && userInteractedCells.has(ref)) {\n          // For non-masked cells, only include zeros if they have formulas\n          const isNumber = typeof cell.v === \"number\" || !isNaN(cell.v) && cell.t === 2;\n          apiExcelData[ref] = isNumber ? Number(cell.v) : String(cell.v);\n        }\n      }\n    });\n  });\n\n  // Final safety check - remove any zeros in masked cells that somehow made it through\n  const finalApiData = {};\n  Object.entries(apiExcelData).forEach(([cellRef, value]) => {\n    const isMaskedCell = maskedCells.includes(cellRef);\n    if (isMaskedCell && value === 0) {\n      console.log(`FINAL CHECK: Removing zero from masked cell ${cellRef} before API submission`);\n      // Don't include this cell\n    } else {\n      finalApiData[cellRef] = value;\n    }\n  });\n  for (const row in structuredMatrix) {\n    for (const col in structuredMatrix[row]) {\n      const cell = structuredMatrix[row][col];\n      const colLetter = columnToLetter(Number(col));\n      const rowNumber = Number(row) + 1;\n      const ref = `${colLetter}${rowNumber}`;\n      const isMasked = maskedCells.includes(ref);\n      const isUserInteracted = userInteractedCells.has(ref);\n      const isZeroCell = (cell.v === 0 || cell.v === \"0\") && !cell.f && (!cell.m || cell.m === \"\") && !isUserInteracted;\n      if (isZeroCell) {\n        delete structuredMatrix[row][col];\n      }\n    }\n    if (Object.keys(structuredMatrix[row]).length === 0) {\n      delete structuredMatrix[row];\n    }\n  }\n  if (typeof setExcelCellMatrix === \"function\") {\n    setExcelCellMatrix(structuredMatrix);\n  }\n  console.log(finalApiData, \"final api data\");\n  return {\n    cellMatrix: structuredMatrix,\n    excelData: finalApiData\n  };\n};\nexport const transformApiMatrixToSheetData = (cellMatrix, maskedCells = [], highlightAllMaskedCells = false) => {\n  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));\n  Object.entries(cellData).forEach(([rowIndex, row]) => {\n    Object.entries(row).forEach(([colIndex, cell]) => {\n      if (!cell.s) cell.s = {};\n      delete cell.s.ht;\n      delete cell.s.vt;\n      delete cell.s.tb;\n      cell.s.ht = 1;\n    });\n  });\n  if (Array.isArray(maskedCells)) {\n    maskedCells.forEach(cellRef => {\n      const indices = cellRefToIndices(cellRef);\n      if (!indices) return;\n      const {\n        rowIndex,\n        colIndex\n      } = indices;\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\n      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};\n      const style = {\n        bg: {\n          rgb: \"#FFFF00\"\n        },\n        ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n      };\n\n      cellData[rowIndex][colIndex].s = style;\n      if (!highlightAllMaskedCells) {\n        cellData[rowIndex][colIndex].v = \"\";\n        if (cellData[rowIndex][colIndex].f) delete cellData[rowIndex][colIndex].f;\n      }\n    });\n  }\n  return {\n    id: `workbook-${Date.now()}`,\n    name: \"universheet\",\n    sheetOrder: [\"sheet-01\"],\n    sheets: {\n      \"sheet-01\": {\n        type: SheetTypes.GRID,\n        id: \"sheet-01\",\n        name: \"Sheet 1\",\n        cellData,\n        rowCount: 20,\n        columnCount: 20,\n        defaultRowHeight: 28,\n        defaultColumnWidth: 93\n      }\n    }\n  };\n};\nexport const transformMatrixWithResponseOverlay = (cellMatrix, responseSubmitted, maskedCells = [], userInteractedCellsRef = null) => {\n  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));\n  Object.entries(cellData).forEach(([rowIndex, row]) => {\n    Object.entries(row).forEach(([colIndex, cell]) => {\n      if (!cell.s) cell.s = {};\n      delete cell.s.ht;\n      delete cell.s.vt;\n      delete cell.s.tb;\n      cell.s.ht = 1;\n    });\n  });\n  const isMatrixFormat = responseSubmitted && typeof responseSubmitted === \"object\" && Object.keys(responseSubmitted).some(key => !isNaN(key));\n  if (Array.isArray(maskedCells)) {\n    maskedCells.forEach(cellRef => {\n      const indices = cellRefToIndices(cellRef);\n      if (!indices) return;\n      const {\n        rowIndex,\n        colIndex\n      } = indices;\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\n      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};\n      const style = {\n        bg: {\n          rgb: \"#FFFF00\"\n        },\n        ht: 1\n      };\n      cellData[rowIndex][colIndex].s = style;\n      let submittedValue = null;\n      if (responseSubmitted) {\n        if (isMatrixFormat) {\n          var _responseSubmitted$ro;\n          const submittedCell = (_responseSubmitted$ro = responseSubmitted[rowIndex]) === null || _responseSubmitted$ro === void 0 ? void 0 : _responseSubmitted$ro[colIndex];\n          if (submittedCell) {\n            var _cellData$rowIndex$co;\n            // Track this cell as user-interacted since it has submitted data\n            if (userInteractedCellsRef !== null && userInteractedCellsRef !== void 0 && userInteractedCellsRef.current) {\n              userInteractedCellsRef.current.add(cellRef);\n            }\n            cellData[rowIndex][colIndex] = {\n              ...cellData[rowIndex][colIndex],\n              ...submittedCell,\n              s: {\n                ...cellData[rowIndex][colIndex].s,\n                ...submittedCell.s,\n                bg: {\n                  rgb: \"#FFFF00\"\n                },\n                ht: 1\n              }\n            };\n            if ((_cellData$rowIndex$co = cellData[rowIndex][colIndex].s) !== null && _cellData$rowIndex$co !== void 0 && _cellData$rowIndex$co.n) {\n              let pattern = cellData[rowIndex][colIndex].s.n;\n              if (typeof pattern === \"string\") {\n                pattern = pattern.replace(/\\\\\"/g, '\"');\n                cellData[rowIndex][colIndex].s.n = {\n                  pattern: pattern\n                };\n              } else if (pattern.pattern) {\n                let cleanPattern = pattern.pattern.replace(/\\\\\"/g, '\"');\n                cellData[rowIndex][colIndex].s.n = {\n                  pattern: cleanPattern\n                };\n              }\n              if (submittedCell.m && submittedCell.m !== cellData[rowIndex][colIndex].v) {\n                cellData[rowIndex][colIndex].m = submittedCell.m;\n              }\n            }\n            return;\n          }\n        } else {\n          submittedValue = responseSubmitted[cellRef];\n          // Track this cell as user-interacted if it has submitted data\n          if (submittedValue !== undefined && submittedValue !== null && submittedValue !== \"\" && userInteractedCellsRef !== null && userInteractedCellsRef !== void 0 && userInteractedCellsRef.current) {\n            userInteractedCellsRef.current.add(cellRef);\n          }\n        }\n      }\n      if (submittedValue !== undefined && submittedValue !== null && submittedValue !== \"\") {\n        let cellType = 1;\n        let finalValue = submittedValue;\n        if (typeof submittedValue === \"string\" && submittedValue.startsWith(\"=\")) {\n          cellData[rowIndex][colIndex].f = submittedValue;\n        } else {\n          if (typeof submittedValue === \"number\") {\n            finalValue = submittedValue;\n            cellType = 2;\n          } else if (typeof submittedValue === \"string\") {\n            const stringValue = submittedValue.trim();\n            if (!isNaN(stringValue) && !isNaN(parseFloat(stringValue)) && stringValue !== \"\") {\n              finalValue = Number(stringValue);\n              cellType = 2;\n            } else {\n              finalValue = submittedValue;\n              cellType = 1;\n            }\n          }\n          cellData[rowIndex][colIndex].v = finalValue;\n          cellData[rowIndex][colIndex].t = cellType;\n          if (cellType === 2) {\n            var _cellMatrix$rowIndex, _originalCell$s;\n            if (!cellData[rowIndex][colIndex].s) {\n              cellData[rowIndex][colIndex].s = {};\n            }\n\n            // Check if there's already a pattern in the original cell data\n            const originalCell = cellMatrix === null || cellMatrix === void 0 ? void 0 : (_cellMatrix$rowIndex = cellMatrix[rowIndex]) === null || _cellMatrix$rowIndex === void 0 ? void 0 : _cellMatrix$rowIndex[colIndex];\n            if (originalCell !== null && originalCell !== void 0 && (_originalCell$s = originalCell.s) !== null && _originalCell$s !== void 0 && _originalCell$s.n) {\n              // Clean up the existing pattern - remove escape slashes\n              let pattern = originalCell.s.n;\n              if (typeof pattern === \"string\") {\n                pattern = pattern.replace(/\\\\\"/g, '\"');\n                cellData[rowIndex][colIndex].s.n = {\n                  pattern: pattern\n                };\n              } else if (pattern.pattern) {\n                let cleanPattern = pattern.pattern.replace(/\\\\\"/g, '\"');\n                cellData[rowIndex][colIndex].s.n = {\n                  pattern: cleanPattern\n                };\n              }\n            } else {\n              // Fallback to default currency pattern - detect from value or use USD as default\n              const cellValue = cellData[rowIndex][colIndex].v;\n              const detectedSymbol = detectCurrencySymbol(String(cellValue || \"\"));\n              const formatPattern = getCurrencyFormatPattern(detectedSymbol || \"$\");\n              cellData[rowIndex][colIndex].s.n = {\n                pattern: formatPattern\n              };\n            }\n          }\n        }\n      } else {\n        cellData[rowIndex][colIndex].v = \"\";\n        cellData[rowIndex][colIndex].t = 1;\n        if (cellData[rowIndex][colIndex].f) {\n          delete cellData[rowIndex][colIndex].f;\n        }\n      }\n    });\n  }\n  console.log(cellData, \"my god data\");\n  return {\n    id: `workbook-${Date.now()}`,\n    name: \"universheet\",\n    sheetOrder: [\"sheet-01\"],\n    sheets: {\n      \"sheet-01\": {\n        type: SheetTypes.GRID,\n        id: \"sheet-01\",\n        name: \"Sheet 1\",\n        cellData,\n        rowCount: 20,\n        columnCount: 20,\n        defaultRowHeight: 28,\n        defaultColumnWidth: 93\n      }\n    }\n  };\n};\nconst ExcelSheets = /*#__PURE__*/_s( /*#__PURE__*/forwardRef(_c = _s(({\n  cellsData,\n  maskedCells,\n  SetExcelApiData,\n  SetCellsFilled,\n  cellsFilled,\n  apiData,\n  excelID,\n  responseSubmitted,\n  highlightAllMaskedCells = false,\n  setExcelCellMatrix = null\n}, ref) => {\n  _s();\n  const [workbookData, setWorkbookData] = useState(null);\n  const univerCreatedRef = useRef(false);\n  const univerAPIRef = useRef(null);\n  const workbookDataRef = useRef(null);\n  const pendingChangesRef = useRef(new Map()); // Track pending changes\n  const debouncedHandleRef = useRef(null);\n  const userInteractedCellsRef = useRef(new Set()); // Track cells user actually interacted with\n\n  // Expose method to force sync all data immediately\n  useImperativeHandle(ref, () => ({\n    syncData: () => {\n      // Cancel any pending debounced calls\n      if (debouncedHandleRef.current) {\n        var _debouncedHandleRef$c, _debouncedHandleRef$c2;\n        (_debouncedHandleRef$c = (_debouncedHandleRef$c2 = debouncedHandleRef.current).cancel) === null || _debouncedHandleRef$c === void 0 ? void 0 : _debouncedHandleRef$c.call(_debouncedHandleRef$c2);\n      }\n      // Process any pending changes immediately\n      handleDataChangeImmediate();\n    }\n  }));\n\n  // Helper function to check if all masked cells are filled in responseSubmitted\n  const areAllMaskedCellsFilled = useCallback(() => {\n    if (!responseSubmitted || !maskedCells || maskedCells.length === 0) {\n      return false;\n    }\n    return maskedCells.every(cellRef => {\n      const value = responseSubmitted[cellRef];\n      return value !== undefined && value !== null && value !== \"\";\n    });\n  }, [responseSubmitted, maskedCells]);\n  useEffect(() => {\n    let matrix;\n    if (apiData !== null && apiData !== void 0 && apiData.cellMatrix && !responseSubmitted) {\n      matrix = transformApiMatrixToSheetData(apiData.cellMatrix, maskedCells, highlightAllMaskedCells);\n    } else if (responseSubmitted) {\n      if (apiData !== null && apiData !== void 0 && apiData.cellMatrix) {\n        var _apiData$responseSubm;\n        const responseData = apiData === null || apiData === void 0 ? void 0 : (_apiData$responseSubm = apiData.responseSubmitted) === null || _apiData$responseSubm === void 0 ? void 0 : _apiData$responseSubm.cellMatrixResponse;\n        if (responseData) {\n          matrix = transformMatrixWithResponseOverlay(apiData.cellMatrix, responseData, maskedCells, userInteractedCellsRef);\n        } else {\n          matrix = transformMatrixWithResponseOverlay(apiData.cellMatrix, responseSubmitted, maskedCells, userInteractedCellsRef);\n        }\n      } else {\n        matrix = transformApiToMatrix({\n          excelData: responseSubmitted,\n          maskedCells,\n          highlightAllMaskedCells\n        });\n      }\n    } else {\n      matrix = transformApiToMatrix({\n        excelData: apiData === null || apiData === void 0 ? void 0 : apiData.excelData,\n        maskedCells,\n        highlightAllMaskedCells\n      });\n    }\n    // Only proceed if we have a valid matrix\n    if (matrix) {\n      var _matrix, _matrix$sheets, _matrix$sheets$sheet;\n      const initialWorkbook = {\n        ...matrix,\n        locale: CoreLocaleType.EN_US,\n        appVersion: \"3.0.0-alpha\"\n      };\n      setWorkbookData(initialWorkbook);\n      workbookDataRef.current = initialWorkbook;\n      univerCreatedRef.current = false;\n      pendingChangesRef.current.clear();\n\n      // If we have a response submitted, update the API data before clearing user interactions\n      if (responseSubmitted && (_matrix = matrix) !== null && _matrix !== void 0 && (_matrix$sheets = _matrix.sheets) !== null && _matrix$sheets !== void 0 && (_matrix$sheets$sheet = _matrix$sheets[\"sheet-01\"]) !== null && _matrix$sheets$sheet !== void 0 && _matrix$sheets$sheet.cellData) {\n        var _apiData$responseSubm2;\n        SetExcelApiData(transformMatrixToApi(matrix.sheets[\"sheet-01\"].cellData, setExcelCellMatrix, userInteractedCellsRef.current, maskedCells, (apiData === null || apiData === void 0 ? void 0 : (_apiData$responseSubm2 = apiData.responseSubmitted) === null || _apiData$responseSubm2 === void 0 ? void 0 : _apiData$responseSubm2.cellMatrixResponse) || null));\n      }\n\n      // Only clear user interactions if we don't have a response submitted\n      if (!responseSubmitted) {\n        userInteractedCellsRef.current.clear();\n      }\n    }\n  }, [apiData, maskedCells, highlightAllMaskedCells, responseSubmitted, areAllMaskedCellsFilled]);\n\n  // Immediate data change handler (no debounce)\n  const handleDataChangeImmediate = useCallback(() => {\n    var _refWorkbook$sheets, _refWorkbook$sheets$s, _apiData$responseSubm3;\n    const univerAPI = univerAPIRef.current;\n    const book = univerAPI === null || univerAPI === void 0 ? void 0 : univerAPI.getActiveWorkbook();\n    const sheet = book === null || book === void 0 ? void 0 : book.getActiveSheet();\n    if (!sheet) return;\n    const refWorkbook = workbookDataRef.current;\n    const existingCellData = (refWorkbook === null || refWorkbook === void 0 ? void 0 : (_refWorkbook$sheets = refWorkbook.sheets) === null || _refWorkbook$sheets === void 0 ? void 0 : (_refWorkbook$sheets$s = _refWorkbook$sheets[\"sheet-01\"]) === null || _refWorkbook$sheets$s === void 0 ? void 0 : _refWorkbook$sheets$s.cellData) || {};\n    const updatedCellData = {};\n    const sheetData = sheet.getSheet().getSnapshot();\n    const maxRow = sheetData.rowCount - 1;\n    const maxCol = sheetData.columnCount - 1;\n\n    // Process all cells including pending changes\n    for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\n      for (let colNum = 0; colNum <= maxCol; colNum++) {\n        var _existingCellData$row;\n        const cellKey = `${rowNum}-${colNum}`;\n        const range = sheet.getRange(rowNum, colNum);\n        const formula = range.getFormula() || \"\";\n        let calculatedValue = range.getValue();\n\n        // For formulas, also get the display value to ensure we have the calculated result\n        let displayValue = calculatedValue;\n        if (formula && typeof range.getDisplayValue === \"function\") {\n          displayValue = range.getDisplayValue();\n          // If display value is different and not empty, use it as the calculated value\n          if (displayValue !== null && displayValue !== undefined && displayValue !== calculatedValue) {\n            calculatedValue = displayValue;\n          }\n        }\n\n        // Don't convert 0 to empty string - 0 is a valid value\n        if (calculatedValue === null || calculatedValue === undefined) {\n          calculatedValue = \"\";\n        }\n\n        // Check if there's a pending change for this cell\n        if (pendingChangesRef.current.has(cellKey)) {\n          calculatedValue = pendingChangesRef.current.get(cellKey);\n        }\n        const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;\n        const existingCell = existingCellData === null || existingCellData === void 0 ? void 0 : (_existingCellData$row = existingCellData[rowNum]) === null || _existingCellData$row === void 0 ? void 0 : _existingCellData$row[colNum];\n        const isMaskedCell = maskedCells.includes(cellRef);\n        const wasUserInteracted = userInteractedCellsRef.current.has(cellRef);\n        const isBlankLike = calculatedValue === \"\" || calculatedValue === null || typeof calculatedValue === \"number\" && calculatedValue === 0 && !formula;\n        const hasData = isMaskedCell ? wasUserInteracted && !isBlankLike : wasUserInteracted && !isBlankLike || formula || pendingChangesRef.current.has(cellKey);\n        if (hasData) {\n          if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};\n\n          // Determine proper type for the cell value\n          let cellType = 1; // Default to text\n          let finalValue = calculatedValue;\n\n          // For formulas, ensure we use the calculated numeric value if possible\n          if (formula && calculatedValue !== \"\" && typeof calculatedValue !== \"undefined\") {\n            // Try to convert to number if it's a numeric result\n            if (typeof calculatedValue === \"string\") {\n              const cleaned = calculatedValue.replace(/[,%$]/g, \"\");\n              if (!isNaN(cleaned) && !isNaN(parseFloat(cleaned))) {\n                finalValue = Number(cleaned);\n                cellType = 2; // Number type\n              } else {\n                finalValue = calculatedValue;\n                cellType = 1; // Text type\n              }\n            } else if (typeof calculatedValue === \"number\") {\n              finalValue = calculatedValue;\n              cellType = 2; // Number type\n            }\n          } else if (!formula && calculatedValue !== \"\" && typeof calculatedValue !== \"undefined\") {\n            // For non-formula cells, check if the value is a number\n            const stringValue = String(calculatedValue).trim();\n            if (!isNaN(stringValue) && !isNaN(parseFloat(stringValue))) {\n              finalValue = Number(stringValue);\n              cellType = 2; // Number type\n            }\n          }\n\n          const newCellData = {\n            v: finalValue,\n            t: cellType,\n            s: {\n              ...((existingCell === null || existingCell === void 0 ? void 0 : existingCell.s) || {}),\n              ht: 1 // Ensure left alignment (1 = left, 2 = center, 3 = right)\n            }\n          };\n\n          if (formula) {\n            newCellData.f = formula;\n          }\n          if (isMaskedCell) {\n            newCellData.s = {\n              ...newCellData.s,\n              bg: {\n                rgb: \"#FFFF00\"\n              },\n              ht: 1 // Ensure left alignment for masked cells (1 = left, 2 = center, 3 = right)\n            };\n\n            if (highlightAllMaskedCells) {\n              newCellData.v = \"\";\n            }\n          }\n          updatedCellData[rowNum][colNum] = newCellData;\n        }\n      }\n    }\n\n    // Clear pending changes as they've been processed\n    pendingChangesRef.current.clear();\n    workbookDataRef.current = {\n      ...refWorkbook,\n      sheets: {\n        ...refWorkbook.sheets,\n        [\"sheet-01\"]: {\n          ...refWorkbook.sheets[\"sheet-01\"],\n          cellData: updatedCellData\n        }\n      }\n    };\n    SetExcelApiData(transformMatrixToApi(updatedCellData, setExcelCellMatrix, userInteractedCellsRef.current, maskedCells, (apiData === null || apiData === void 0 ? void 0 : (_apiData$responseSubm3 = apiData.responseSubmitted) === null || _apiData$responseSubm3 === void 0 ? void 0 : _apiData$responseSubm3.cellMatrixResponse) || null));\n    if (highlightAllMaskedCells) {\n      SetCellsFilled(false);\n    } else {\n      let anyFilled = false;\n      for (const cellRef of maskedCells) {\n        var _updatedCellData$rowI;\n        const {\n          rowIndex,\n          colIndex\n        } = cellRefToIndices(cellRef);\n        const cell = updatedCellData === null || updatedCellData === void 0 ? void 0 : (_updatedCellData$rowI = updatedCellData[rowIndex]) === null || _updatedCellData$rowI === void 0 ? void 0 : _updatedCellData$rowI[colIndex];\n        if ((cell === null || cell === void 0 ? void 0 : cell.v) !== undefined && cell.v !== null && cell.v !== \"\") {\n          anyFilled = true;\n          break;\n        }\n      }\n      SetCellsFilled(anyFilled);\n    }\n  }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);\n\n  // Enhanced data change handler that preserves formatting during paste operations\n  const handleFormattedDataChange = useCallback(() => {\n    try {\n      var _refWorkbook$sheets2, _refWorkbook$sheets2$, _apiData$responseSubm4;\n      const univerAPI = univerAPIRef.current;\n      const book = univerAPI === null || univerAPI === void 0 ? void 0 : univerAPI.getActiveWorkbook();\n      const sheet = book === null || book === void 0 ? void 0 : book.getActiveSheet();\n      if (!sheet || typeof sheet.getRange !== \"function\") return;\n      const refWorkbook = workbookDataRef.current;\n      const existingCellData = (refWorkbook === null || refWorkbook === void 0 ? void 0 : (_refWorkbook$sheets2 = refWorkbook.sheets) === null || _refWorkbook$sheets2 === void 0 ? void 0 : (_refWorkbook$sheets2$ = _refWorkbook$sheets2[\"sheet-01\"]) === null || _refWorkbook$sheets2$ === void 0 ? void 0 : _refWorkbook$sheets2$.cellData) || {};\n      const updatedCellData = {};\n      const sheetData = sheet.getSheet().getSnapshot();\n      const maxRow = sheetData.rowCount - 1;\n      const maxCol = sheetData.columnCount - 1;\n      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\n        for (let colNum = 0; colNum <= maxCol; colNum++) {\n          var _range$getDisplayValu, _range$getDisplayValu2, _range$getStyle;\n          const cellKey = `${rowNum}-${colNum}`;\n          const range = sheet.getRange(rowNum, colNum);\n          if (!range || typeof range.getFormula !== \"function\") continue;\n          const formula = range.getFormula();\n          const rawValue = range.getValue();\n          const displayValue = (_range$getDisplayValu = (_range$getDisplayValu2 = range.getDisplayValue) === null || _range$getDisplayValu2 === void 0 ? void 0 : _range$getDisplayValu2.call(range)) !== null && _range$getDisplayValu !== void 0 ? _range$getDisplayValu : rawValue;\n          const styleFromSheet = ((_range$getStyle = range.getStyle) === null || _range$getStyle === void 0 ? void 0 : _range$getStyle.call(range)) || {};\n          if (formula || rawValue !== null || maskedCells.includes(`${columnToLetter(colNum)}${rowNum + 1}`)) {\n            var _existingCellData$row2, _existingCellData$row3;\n            if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};\n            const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;\n            const isMasked = maskedCells.includes(cellRef);\n            let format = styleFromSheet.n;\n            if (!format && typeof displayValue === \"string\") {\n              const detectedSymbol = detectCurrencySymbol(displayValue);\n              if (detectedSymbol) {\n                format = getCurrencyFormatPattern(detectedSymbol);\n              } else if (displayValue.includes(\"%\")) {\n                format = \"0.00%\";\n              }\n            }\n            let finalValue = rawValue;\n            if (typeof rawValue === \"string\") {\n              const cleaned = cleanCurrencyValue(rawValue).replace(/%/g, \"\");\n              finalValue = isNaN(cleaned) ? rawValue : Number(cleaned);\n            }\n            const patchedCell = {\n              v: finalValue,\n              m: displayValue,\n              t: typeof finalValue === \"number\" ? 2 : 1,\n              s: {\n                ...((existingCellData === null || existingCellData === void 0 ? void 0 : (_existingCellData$row2 = existingCellData[rowNum]) === null || _existingCellData$row2 === void 0 ? void 0 : (_existingCellData$row3 = _existingCellData$row2[colNum]) === null || _existingCellData$row3 === void 0 ? void 0 : _existingCellData$row3.s) || {}),\n                ...(format ? {\n                  n: format\n                } : {}),\n                ht: 1,\n                ...(isMasked ? {\n                  bg: {\n                    rgb: \"#FFFF00\"\n                  }\n                } : {})\n              }\n            };\n            if (formula) patchedCell.f = formula;\n            updatedCellData[rowNum][colNum] = patchedCell;\n          }\n        }\n      }\n      workbookDataRef.current = {\n        ...refWorkbook,\n        sheets: {\n          ...refWorkbook.sheets,\n          [\"sheet-01\"]: {\n            ...refWorkbook.sheets[\"sheet-01\"],\n            cellData: updatedCellData\n          }\n        }\n      };\n      SetExcelApiData(transformMatrixToApi(updatedCellData, setExcelCellMatrix, userInteractedCellsRef.current, maskedCells, (apiData === null || apiData === void 0 ? void 0 : (_apiData$responseSubm4 = apiData.responseSubmitted) === null || _apiData$responseSubm4 === void 0 ? void 0 : _apiData$responseSubm4.cellMatrixResponse) || null));\n      if (highlightAllMaskedCells) {\n        SetCellsFilled(false);\n      } else {\n        let anyFilled = false;\n        for (const cellRef of maskedCells) {\n          var _updatedCellData$rowI2;\n          const {\n            rowIndex,\n            colIndex\n          } = cellRefToIndices(cellRef);\n          const cell = updatedCellData === null || updatedCellData === void 0 ? void 0 : (_updatedCellData$rowI2 = updatedCellData[rowIndex]) === null || _updatedCellData$rowI2 === void 0 ? void 0 : _updatedCellData$rowI2[colIndex];\n          if ((cell === null || cell === void 0 ? void 0 : cell.v) !== undefined && cell.v !== null && cell.v !== \"\") {\n            anyFilled = true;\n            break;\n          }\n        }\n        SetCellsFilled(anyFilled);\n      }\n    } catch (error) {\n      console.error(\"Error in handleFormattedDataChange:\", error);\n    }\n  }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);\n\n  // Debounced version for regular updates\n  const handleDataChange = useCallback(() => {\n    handleDataChangeImmediate();\n  }, [handleDataChangeImmediate]);\n  useEffect(() => {\n    var _apiData$responseSubm5;\n    if (!workbookData || univerCreatedRef.current) return;\n    const {\n      univerAPI\n    } = createUniver({\n      locale: LocaleType.EN_US,\n      locales: {\n        [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS)\n      },\n      theme: defaultTheme,\n      presets: [UniverSheetsCorePreset()]\n    });\n    univerAPIRef.current = univerAPI;\n    univerAPI.createWorkbook(workbookData);\n    const initialCellData = workbookData.sheets[\"sheet-01\"].cellData;\n    SetExcelApiData(transformMatrixToApi(initialCellData, setExcelCellMatrix, userInteractedCellsRef.current, maskedCells, (apiData === null || apiData === void 0 ? void 0 : (_apiData$responseSubm5 = apiData.responseSubmitted) === null || _apiData$responseSubm5 === void 0 ? void 0 : _apiData$responseSubm5.cellMatrixResponse) || null));\n    if (highlightAllMaskedCells) {\n      SetCellsFilled(false);\n    } else {\n      let anyMaskedFilled = false;\n      for (const cellRef of maskedCells) {\n        var _initialCellData$rowI;\n        const {\n          rowIndex,\n          colIndex\n        } = cellRefToIndices(cellRef);\n        const cell = initialCellData === null || initialCellData === void 0 ? void 0 : (_initialCellData$rowI = initialCellData[rowIndex]) === null || _initialCellData$rowI === void 0 ? void 0 : _initialCellData$rowI[colIndex];\n        if ((cell === null || cell === void 0 ? void 0 : cell.v) !== undefined && cell.v !== null && cell.v !== \"\") {\n          anyMaskedFilled = true;\n          break;\n        }\n      }\n      SetCellsFilled(anyMaskedFilled);\n    }\n    const restrictPermissionOnCells = async () => {\n      const book = univerAPI === null || univerAPI === void 0 ? void 0 : univerAPI.getActiveWorkbook();\n      const sheet = book.getActiveSheet();\n      const bookId = book.getId();\n      const sheetId = sheet.getSheetId();\n      const permission = book.getPermission();\n      const allCells = [];\n      for (let row = 1; row <= 100; row++) {\n        for (let col = 0; col < 26; col++) {\n          allCells.push(`${String.fromCharCode(65 + col)}${row}`);\n        }\n      }\n      const cellsToLock = allCells.filter(cell => !maskedCells.includes(cell));\n      const ranges = cellsToLock.map(cell => sheet.getRange(cell));\n      const {\n        permissionId\n      } = await permission.addRangeBaseProtection(bookId, sheetId, ranges);\n      const editPoint = permission.permissionPointsDefinition.RangeProtectionPermissionEditPoint;\n      permission.rangeRuleChangedAfterAuth$.subscribe(id => {\n        if (id === permissionId) {\n          permission.setRangeProtectionPermissionPoint(bookId, sheetId, permissionId, editPoint, false);\n        }\n      });\n    };\n    restrictPermissionOnCells();\n\n    // Create debounced version with longer delay and store reference\n    const debouncedHandleDataChange = debounce(handleDataChange, 100);\n    debouncedHandleRef.current = debouncedHandleDataChange;\n    univerAPI.addEvent(univerAPI.Event.SheetEditChanging, params => {\n      var _params$value, _params$value$_data, _params$value$_data$b, _refWorkbook$sheets3, _refWorkbook$sheets3$;\n      const newValue = params === null || params === void 0 ? void 0 : (_params$value = params.value) === null || _params$value === void 0 ? void 0 : (_params$value$_data = _params$value._data) === null || _params$value$_data === void 0 ? void 0 : (_params$value$_data$b = _params$value$_data.body) === null || _params$value$_data$b === void 0 ? void 0 : _params$value$_data$b.dataStream;\n      const row = params === null || params === void 0 ? void 0 : params.row;\n      const column = params === null || params === void 0 ? void 0 : params.column;\n      if (newValue === undefined || row === undefined || column === undefined) return;\n      const trimmedValue = String(newValue).trim();\n      const cellKey = `${row}-${column}`;\n      const cellRef = `${columnToLetter(column)}${row + 1}`;\n\n      // Track that user interacted with this cell\n      userInteractedCellsRef.current.add(cellRef);\n\n      // Store pending change - but store the properly typed value\n      let typedValue = trimmedValue;\n      let cellType = 1; // Default to text\n\n      // Check if the value is a number\n      if (trimmedValue !== \"\" && !isNaN(trimmedValue) && !isNaN(parseFloat(trimmedValue))) {\n        typedValue = Number(trimmedValue);\n        cellType = 2; // Number type\n      }\n\n      pendingChangesRef.current.set(cellKey, typedValue);\n      const refWorkbook = workbookDataRef.current;\n      const cellData = (refWorkbook === null || refWorkbook === void 0 ? void 0 : (_refWorkbook$sheets3 = refWorkbook.sheets) === null || _refWorkbook$sheets3 === void 0 ? void 0 : (_refWorkbook$sheets3$ = _refWorkbook$sheets3[\"sheet-01\"]) === null || _refWorkbook$sheets3$ === void 0 ? void 0 : _refWorkbook$sheets3$.cellData) || {};\n      const updatedCellData = {\n        ...cellData\n      };\n      if (!updatedCellData[row]) updatedCellData[row] = {};\n      const existingCell = updatedCellData[row][column] || {};\n      const isMaskedCell = maskedCells.some(cellRef => {\n        const {\n          rowIndex,\n          colIndex\n        } = cellRefToIndices(cellRef);\n        return rowIndex === row && colIndex === column;\n      });\n      if (isMaskedCell) {\n        if (highlightAllMaskedCells) {\n          updatedCellData[row][column] = {\n            ...existingCell,\n            v: \"\",\n            t: 1,\n            s: {\n              bg: {\n                rgb: \"#FFFF00\"\n              },\n              ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n            }\n          };\n        } else {\n          updatedCellData[row][column] = {\n            ...existingCell,\n            v: typedValue,\n            t: cellType,\n            s: {\n              bg: {\n                rgb: \"#FFFF00\"\n              },\n              ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n            }\n          };\n        }\n      } else {\n        updatedCellData[row][column] = {\n          ...existingCell,\n          v: typedValue,\n          t: cellType,\n          s: {\n            ...((existingCell === null || existingCell === void 0 ? void 0 : existingCell.s) || {}),\n            ht: 1 // Left align text (1 = left, 2 = center, 3 = right)\n          }\n        };\n      }\n\n      workbookDataRef.current = {\n        ...refWorkbook,\n        sheets: {\n          ...refWorkbook.sheets,\n          [\"sheet-01\"]: {\n            ...refWorkbook.sheets[\"sheet-01\"],\n            cellData: updatedCellData\n          }\n        }\n      };\n      debouncedHandleDataChange();\n    });\n\n    // Use the enhanced data change handler for better formatting preservation\n    const debouncedFormattedDataChange = debounce(handleFormattedDataChange, 100);\n\n    // Aggressive formula formatting handler - triggers on ANY value change\n    const handleFormulaFormatting = () => {\n      const book = univerAPI === null || univerAPI === void 0 ? void 0 : univerAPI.getActiveWorkbook();\n      const sheet = book === null || book === void 0 ? void 0 : book.getActiveSheet();\n      if (!sheet) return;\n\n      // Check all cells for formulas that need formatting\n      const sheetData = sheet.getSheet().getSnapshot();\n      const maxRow = Math.min(sheetData.rowCount - 1, 100); // Limit to reasonable range\n      const maxCol = Math.min(sheetData.columnCount - 1, 26);\n      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\n        for (let colNum = 0; colNum <= maxCol; colNum++) {\n          const range = sheet.getRange(rowNum, colNum);\n          const formula = range.getFormula();\n          const calculatedValue = range.getValue();\n          if (formula && formula.startsWith(\"=\") && typeof calculatedValue === \"number\") {\n            const cellRefs = formula.match(/[A-Z]+\\d+/g);\n            if (cellRefs) {\n              let shouldApplyCurrencyFormat = false;\n              for (const cellRef of cellRefs) {\n                const refIndices = cellRefToIndices(cellRef);\n                if (refIndices) {\n                  const refRange = sheet.getRange(refIndices.rowIndex, refIndices.colIndex);\n                  const refDisplayValue = refRange.getDisplayValue();\n                  if (refDisplayValue && typeof refDisplayValue === \"string\" && detectCurrencySymbol(refDisplayValue)) {\n                    shouldApplyCurrencyFormat = detectCurrencySymbol(refDisplayValue);\n                    break;\n                  }\n                }\n              }\n              if (shouldApplyCurrencyFormat) {\n                const currentDisplayValue = range.getDisplayValue();\n                // Only apply if not already formatted with the detected currency\n                if (!currentDisplayValue || !detectCurrencySymbol(currentDisplayValue)) {\n                  try {\n                    const formatPattern = getCurrencyFormatPattern(shouldApplyCurrencyFormat);\n                    range.setNumberFormat(formatPattern);\n                  } catch (e) {}\n                }\n              }\n            }\n          }\n        }\n      }\n    };\n    const captureAllFormulaResults = () => {\n      const univerAPI = univerAPIRef.current;\n      const book = univerAPI === null || univerAPI === void 0 ? void 0 : univerAPI.getActiveWorkbook();\n      const sheet = book === null || book === void 0 ? void 0 : book.getActiveSheet();\n      if (sheet) {\n        const sheetData = sheet.getSheet().getSnapshot();\n        const maxRow = sheetData.rowCount - 1;\n        const maxCol = sheetData.columnCount - 1;\n        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\n          for (let colNum = 0; colNum <= maxCol; colNum++) {\n            const range = sheet.getRange(rowNum, colNum);\n            if (range) {\n              const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;\n              const isMaskedCell = maskedCells.includes(cellRef);\n\n              // For masked cells, be very strict about what counts as user interaction\n              if (isMaskedCell) {\n                let shouldTrack = false;\n\n                // Check for formula first - formulas are always meaningful\n                if (typeof range.getFormula === \"function\") {\n                  const formula = range.getFormula();\n                  if (formula) {\n                    shouldTrack = true;\n                  }\n                }\n\n                // For values without formulas, be very restrictive\n                if (!shouldTrack && typeof range.getValue === \"function\") {\n                  const value = range.getValue();\n                  // ONLY track if:\n                  // 1. Value is not zero (zeros are likely from drag)\n                  // 2. Value is not empty/null/undefined\n                  // 3. Cell was already tracked (preserve existing tracking)\n                  if (value !== null && value !== undefined && value !== \"\" && value !== 0 && !userInteractedCellsRef.current.has(cellRef)) {\n                    shouldTrack = true;\n                  }\n                }\n\n                // Only add to tracking if we're sure it's meaningful\n                if (shouldTrack) {\n                  userInteractedCellsRef.current.add(cellRef);\n                }\n              }\n            }\n          }\n        }\n      }\n\n      // Clean up any cells that were marked as user-interacted but only have zeros\n      // This is an aggressive cleanup for drag-generated zeros\n      const cellsToRemove = [];\n      userInteractedCellsRef.current.forEach(cellRef => {\n        const indices = cellRefToIndices(cellRef);\n        if (indices) {\n          const {\n            rowIndex,\n            colIndex\n          } = indices;\n          const range = sheet.getRange(rowIndex, colIndex);\n          if (range) {\n            const value = range.getValue();\n            const formula = range.getFormula();\n            const isMaskedCell = maskedCells.includes(cellRef);\n\n            // Remove from tracking if it's a masked cell with zero and no formula\n            if (isMaskedCell && value === 0 && !formula) {\n              cellsToRemove.push(cellRef);\n            }\n          }\n        }\n      });\n\n      // Remove the problematic cells from tracking\n      cellsToRemove.forEach(cellRef => {\n        console.log(`Removing cell ${cellRef} from user interaction tracking - had zero without formula`);\n        userInteractedCellsRef.current.delete(cellRef);\n      });\n      if (cellsToRemove.length > 0) {\n        console.log(`Cleaned up ${cellsToRemove.length} cells with drag-generated zeros`);\n      }\n\n      // Force immediate data capture - no delays, no complications\n      handleDataChangeImmediate();\n\n      // Also trigger the formatted version as backup\n      setTimeout(() => {\n        handleFormattedDataChange();\n      }, 100);\n    };\n\n    // Trigger formula formatting on multiple events\n    const debouncedFormulaFormatting = debounce(handleFormulaFormatting, 200);\n\n    // Listen to SheetValueChanged for immediate formatting\n    univerAPI.addEvent(univerAPI.Event.SheetValueChanged, () => {\n      debouncedFormulaFormatting();\n      debouncedFormattedDataChange();\n    });\n\n    // AGGRESSIVE DRAG DETECTION - Capture data on ANY mouse activity\n    const containerElement = document.getElementById(excelID);\n    if (containerElement) {\n      let isDragging = false;\n      let dragTimer = null;\n      let isTyping = false;\n      const handleMouseDown = e => {\n        if (e.target.closest(\".univer-container\")) {\n          isDragging = false;\n          isTyping = false;\n          // Clear any existing timer\n          if (dragTimer) {\n            clearTimeout(dragTimer);\n            dragTimer = null;\n          }\n        }\n      };\n\n      // Track actual typing/input events\n      const handleKeyDown = e => {\n        if (e.target.closest(\".univer-container\")) {\n          isTyping = true;\n          // Reset typing flag after a short delay\n          setTimeout(() => {\n            isTyping = false;\n          }, 1000);\n        }\n      };\n      const handleMouseMove = e => {\n        if (e.buttons === 1) {\n          isDragging = true;\n        }\n      };\n      const handleMouseUp = () => {\n        if (isDragging) {\n          setTimeout(() => {\n            captureAllFormulaResults();\n          }, 100);\n          setTimeout(() => {\n            captureAllFormulaResults();\n          }, 300);\n          setTimeout(() => {\n            captureAllFormulaResults();\n          }, 600);\n          isDragging = false;\n        }\n      };\n      containerElement.addEventListener(\"mousedown\", handleMouseDown);\n      containerElement.addEventListener(\"mousemove\", handleMouseMove);\n      containerElement.addEventListener(\"mouseup\", handleMouseUp);\n      containerElement.addEventListener(\"keydown\", handleKeyDown);\n\n      // Store references for cleanup\n      containerElement._dragHandlers = {\n        mousedown: handleMouseDown,\n        mousemove: handleMouseMove,\n        mouseup: handleMouseUp,\n        keydown: handleKeyDown\n      };\n      const handlePaste = () => {\n        setTimeout(() => {\n          debouncedFormattedDataChange();\n        }, 100);\n      };\n      containerElement.addEventListener(\"paste\", handlePaste);\n\n      // Store all references for cleanup\n      containerElement._pasteHandler = handlePaste;\n    }\n\n    // Also try to listen for any available fill/drag events\n    try {\n      // Try different possible event names for drag operations\n      const possibleEvents = [\"SheetFillSeries\", \"SheetAutofill\", \"SheetDragFill\", \"SheetCellsChanged\"];\n      possibleEvents.forEach(eventName => {\n        if (univerAPI.Event[eventName]) {\n          univerAPI.addEvent(univerAPI.Event[eventName], params => {\n            setTimeout(() => {\n              debouncedFormulaFormatting();\n              debouncedFormattedDataChange();\n            }, 300);\n          });\n        }\n      });\n    } catch (e) {}\n\n    // Listen for paste events if available\n    try {\n      if (univerAPI.Event.ClipboardPasted) {\n        univerAPI.addEvent(univerAPI.Event.ClipboardPasted, debouncedFormattedDataChange);\n      }\n    } catch (e) {}\n    univerCreatedRef.current = true;\n    return () => {\n      var _debouncedHandleRef$c3, _univerAPIRef$current, _univerAPIRef$current2;\n      // Cancel any pending debounced calls\n      if ((_debouncedHandleRef$c3 = debouncedHandleRef.current) !== null && _debouncedHandleRef$c3 !== void 0 && _debouncedHandleRef$c3.cancel) {\n        debouncedHandleRef.current.cancel();\n      }\n\n      // Clean up DOM event listeners\n      const containerElement = document.getElementById(excelID);\n      if (containerElement) {\n        // Clean up paste handler\n        if (containerElement._pasteHandler) {\n          containerElement.removeEventListener(\"paste\", containerElement._pasteHandler);\n          delete containerElement._pasteHandler;\n        }\n\n        // Clean up drag handlers\n        if (containerElement._dragHandlers) {\n          containerElement.removeEventListener(\"mousedown\", containerElement._dragHandlers.mousedown);\n          containerElement.removeEventListener(\"mousemove\", containerElement._dragHandlers.mousemove);\n          containerElement.removeEventListener(\"mouseup\", containerElement._dragHandlers.mouseup);\n          containerElement.removeEventListener(\"keydown\", containerElement._dragHandlers.keydown);\n          delete containerElement._dragHandlers;\n        }\n      }\n      (_univerAPIRef$current = univerAPIRef.current) === null || _univerAPIRef$current === void 0 ? void 0 : (_univerAPIRef$current2 = _univerAPIRef$current.dispose) === null || _univerAPIRef$current2 === void 0 ? void 0 : _univerAPIRef$current2.call(_univerAPIRef$current);\n      univerAPIRef.current = null;\n      univerCreatedRef.current = false;\n      pendingChangesRef.current.clear();\n      userInteractedCellsRef.current.clear();\n    };\n  }, [workbookData, excelID, SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells, handleDataChange, handleFormattedDataChange]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"univer-container\",\n      id: excelID\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1480,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      transition: Zoom\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1481,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1479,\n    columnNumber: 7\n  }, this);\n}, \"6fm0/X8roPfG+Y1mDZdkEqgoNIE=\")), \"6fm0/X8roPfG+Y1mDZdkEqgoNIE=\");\n_c2 = ExcelSheets;\nExcelSheets.displayName = \"ExcelSheets\";\nexport default ExcelSheets;\nvar _c, _c2;\n$RefreshReg$(_c, \"ExcelSheets$forwardRef\");\n$RefreshReg$(_c2, \"ExcelSheets\");", "map": {"version": 3, "names": ["createUniver", "defaultTheme", "LocaleType", "merge", "UniverSheetsCorePreset", "UniverPresetSheetsCoreEnUS", "BooleanNumber", "SheetTypes", "CoreLocaleType", "useCallback", "useEffect", "useRef", "useState", "useImperativeHandle", "forwardRef", "ToastContainer", "Zoom", "Currencies", "jsxDEV", "_jsxDEV", "debounce", "func", "delay", "timeoutId", "args", "clearTimeout", "setTimeout", "apply", "detectCurrencySymbol", "value", "currency", "includes", "sign", "getCurrencyFormatPattern", "currencySymbol", "cleanCurrencyValue", "cleaned", "replace", "RegExp", "trim", "letterToColumn", "letter", "col", "i", "length", "charCodeAt", "columnToLetter", "String", "fromCharCode", "Math", "floor", "cellRefToIndices", "cellRef", "match", "colLetter", "rowNumber", "rowIndex", "parseInt", "colIndex", "transformApiToMatrix", "apiData", "cellData", "maxRow", "maxCol", "excelData", "Object", "entries", "for<PERSON>ach", "cell", "indices", "startsWith", "f", "s", "ht", "v", "max", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "bg", "rgb", "highlightAllMaskedCells", "id", "Date", "now", "name", "sheetOrder", "sheets", "type", "GRID", "rowCount", "columnCount", "defaultRowHeight", "defaultColumnWidth", "transformMatrixToApi", "setExcelCellMatrix", "userInteractedCells", "Set", "existingCellMatrix", "console", "log", "structuredMatrix", "JSON", "parse", "stringify", "apiExcelData", "from", "cellMatrix", "row", "ref", "isMaskedCell", "has", "isZeroWithoutFormula", "m", "isNumber", "isNaN", "t", "finalValue", "Number", "undefined", "finalApiData", "isMasked", "isUserInteracted", "isZeroCell", "keys", "transformApiMatrixToSheetData", "vt", "tb", "style", "transformMatrixWithResponseOverlay", "responseSubmitted", "userInteractedCellsRef", "isMatrixFormat", "some", "key", "submittedValue", "_responseSubmitted$ro", "submittedCell", "_cellData$rowIndex$co", "current", "add", "n", "pattern", "cleanPattern", "cellType", "stringValue", "parseFloat", "_cellMatrix$rowIndex", "_originalCell$s", "originalCell", "cellValue", "detectedSymbol", "formatPattern", "ExcelSheets", "_s", "_c", "cellsData", "SetExcelApiData", "SetCellsFilled", "cellsFilled", "excelID", "workbookData", "setWorkbookData", "univerCreatedRef", "univerAPIRef", "workbookDataRef", "pendingChangesRef", "Map", "debouncedHandleRef", "syncData", "_debouncedHandleRef$c", "_debouncedHandleRef$c2", "cancel", "call", "handleDataChangeImmediate", "areAllMaskedCellsFilled", "every", "matrix", "_apiData$responseSubm", "responseData", "cellMatrixResponse", "_matrix", "_matrix$sheets", "_matrix$sheets$sheet", "initialWorkbook", "locale", "EN_US", "appVersion", "clear", "_apiData$responseSubm2", "_refWorkbook$sheets", "_refWorkbook$sheets$s", "_apiData$responseSubm3", "univerAPI", "book", "getActiveWorkbook", "sheet", "getActiveSheet", "refWorkbook", "existingCellData", "updatedCellData", "sheetData", "getSheet", "getSnapshot", "row<PERSON>um", "colNum", "_existingCellData$row", "cellKey", "range", "getRange", "formula", "getFormula", "calculatedValue", "getValue", "displayValue", "getDisplayValue", "get", "existingCell", "wasUserInteracted", "isBlankLike", "hasData", "newCellData", "anyFilled", "_updatedCellData$rowI", "handleFormattedDataChange", "_refWorkbook$sheets2", "_refWorkbook$sheets2$", "_apiData$responseSubm4", "_range$getDisplayValu", "_range$getDisplayValu2", "_range$getStyle", "rawValue", "styleFromSheet", "getStyle", "_existingCellData$row2", "_existingCellData$row3", "format", "patchedCell", "_updatedCellData$rowI2", "error", "handleDataChange", "_apiData$responseSubm5", "locales", "theme", "presets", "createWorkbook", "initialCellData", "anyMaskedFilled", "_initialCellData$rowI", "restrictPermissionOnCells", "bookId", "getId", "sheetId", "getSheetId", "permission", "getPermission", "allCells", "push", "cellsToLock", "filter", "ranges", "map", "permissionId", "addRangeBaseProtection", "editPoint", "permissionPointsDefinition", "RangeProtectionPermissionEditPoint", "rangeRuleChangedAfterAuth$", "subscribe", "setRangeProtectionPermissionPoint", "debouncedHandleDataChange", "addEvent", "Event", "SheetEditChanging", "params", "_params$value", "_params$value$_data", "_params$value$_data$b", "_refWorkbook$sheets3", "_refWorkbook$sheets3$", "newValue", "_data", "body", "dataStream", "column", "trimmedValue", "typedValue", "set", "debouncedFormattedDataChange", "handleFormulaFormatting", "min", "cellRefs", "shouldApplyCurrencyFormat", "refIndices", "refRange", "refDisplayValue", "currentDisplayValue", "setNumberFormat", "e", "captureAllFormulaResults", "shouldTrack", "cellsToRemove", "delete", "debouncedFormulaFormatting", "Sheet<PERSON><PERSON>ueChanged", "containerElement", "document", "getElementById", "isDragging", "dragTimer", "isTyping", "handleMouseDown", "target", "closest", "handleKeyDown", "handleMouseMove", "buttons", "handleMouseUp", "addEventListener", "_dragHandlers", "mousedown", "mousemove", "mouseup", "keydown", "handlePaste", "_paste<PERSON><PERSON><PERSON>", "possibleEvents", "eventName", "ClipboardPasted", "_debouncedHandleRef$c3", "_univerAPIRef$current", "_univerAPIRef$current2", "removeEventListener", "dispose", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "_c2", "displayName", "$RefreshReg$"], "sources": ["D:/CodeFreaks/dexta-app/src/Components/Excel/Excelmain.js"], "sourcesContent": ["import {\r\n  createUniver,\r\n  defaultTheme,\r\n  LocaleType,\r\n  merge,\r\n} from \"@univerjs/presets\";\r\nimport { UniverSheetsCorePreset } from \"@univerjs/presets/preset-sheets-core\";\r\nimport UniverPresetSheetsCoreEnUS from \"@univerjs/presets/preset-sheets-core/locales/en-US\";\r\n\r\nimport \"./style.css\";\r\nimport \"@univerjs/presets/lib/styles/preset-sheets-core.css\";\r\n\r\nimport { BooleanNumber, SheetTypes } from \"@univerjs/core\";\r\nimport { LocaleType as CoreLocaleType } from \"@univerjs/core\";\r\nimport {\r\n  useCallback,\r\n  useEffect,\r\n  useRef,\r\n  useState,\r\n  useImperativeHandle,\r\n  forwardRef,\r\n} from \"react\";\r\nimport { ToastContainer, Zoom } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport { Currencies } from \"../Modals/data\";\r\n\r\nfunction debounce(func, delay) {\r\n  let timeoutId;\r\n  return function (...args) {\r\n    clearTimeout(timeoutId);\r\n    timeoutId = setTimeout(() => func.apply(this, args), delay);\r\n  };\r\n}\r\n\r\n// Helper function to detect currency symbol in a string\r\nconst detectCurrencySymbol = (value) => {\r\n  if (typeof value !== \"string\") return null;\r\n\r\n  for (const currency of Currencies) {\r\n    if (value.includes(currency.sign)) {\r\n      return currency.sign;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\n// Helper function to get currency format pattern\r\nconst getCurrencyFormatPattern = (currencySymbol) => {\r\n  if (!currencySymbol) return null;\r\n\r\n  // Handle special cases for multi-character currency symbols\r\n  if (currencySymbol === \"SAR\") {\r\n    return '\"SAR\"#,##0';\r\n  }\r\n\r\n  // For single character symbols, wrap in quotes\r\n  return `\"${currencySymbol}\"#,##0`;\r\n};\r\n\r\n// Helper function to clean currency symbols from string values\r\nconst cleanCurrencyValue = (value) => {\r\n  if (typeof value !== \"string\") return value;\r\n\r\n  let cleaned = value;\r\n  // Remove all known currency symbols\r\n  for (const currency of Currencies) {\r\n    cleaned = cleaned.replace(\r\n      new RegExp(currency.sign.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"g\"),\r\n      \"\"\r\n    );\r\n  }\r\n\r\n  // Remove commas and trim\r\n  cleaned = cleaned.replace(/,/g, \"\").trim();\r\n\r\n  return cleaned;\r\n};\r\n\r\nexport const letterToColumn = (letter) => {\r\n  let col = 0;\r\n  for (let i = 0; i < letter.length; i++) {\r\n    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);\r\n  }\r\n  return col - 1;\r\n};\r\n\r\nexport const columnToLetter = (col) => {\r\n  let letter = \"\";\r\n  while (col >= 0) {\r\n    letter = String.fromCharCode((col % 26) + 65) + letter;\r\n    col = Math.floor(col / 26) - 1;\r\n  }\r\n  return letter;\r\n};\r\n\r\nexport const cellRefToIndices = (cellRef) => {\r\n  const match = cellRef.match(/([A-Z]+)(\\d+)/);\r\n  if (!match) return null;\r\n  const [, colLetter, rowNumber] = match;\r\n  const rowIndex = parseInt(rowNumber, 10) - 1;\r\n  const colIndex = letterToColumn(colLetter);\r\n  return { rowIndex, colIndex };\r\n};\r\n\r\nexport const transformApiToMatrix = (apiData) => {\r\n  const cellData = {};\r\n  let maxRow = 0;\r\n  let maxCol = 0;\r\n\r\n  if (apiData?.excelData) {\r\n    Object?.entries(apiData.excelData).forEach(([cell, value]) => {\r\n      const indices = cellRefToIndices(cell);\r\n      if (!indices) return;\r\n      const { rowIndex, colIndex } = indices;\r\n\r\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\r\n\r\n      if (typeof value === \"string\" && value.startsWith(\"=\")) {\r\n        cellData[rowIndex][colIndex] = {\r\n          f: value,\r\n          s: {\r\n            ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n          },\r\n        };\r\n      } else {\r\n        cellData[rowIndex][colIndex] = {\r\n          v: value,\r\n          s: {\r\n            ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n          },\r\n        };\r\n      }\r\n\r\n      maxRow = Math.max(maxRow, rowIndex);\r\n      maxCol = Math.max(maxCol, colIndex);\r\n    });\r\n  }\r\n\r\n  if (Array?.isArray(apiData?.maskedCells)) {\r\n    apiData.maskedCells.forEach((cellRef) => {\r\n      const indices = cellRefToIndices(cellRef);\r\n      if (!indices) return;\r\n      const { rowIndex, colIndex } = indices;\r\n\r\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\r\n      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};\r\n\r\n      cellData[rowIndex][colIndex].s = {\r\n        ...(cellData[rowIndex][colIndex].s || {}),\r\n        bg: { rgb: \"#FFFF00\" },\r\n        ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n      };\r\n\r\n      if (apiData?.highlightAllMaskedCells) {\r\n        cellData[rowIndex][colIndex].v = \"\";\r\n      }\r\n    });\r\n  }\r\n\r\n  return {\r\n    id: `workbook-${Date.now()}`,\r\n    name: \"universheet\",\r\n    sheetOrder: [\"sheet-01\"],\r\n    sheets: {\r\n      \"sheet-01\": {\r\n        type: SheetTypes.GRID,\r\n        id: \"sheet-01\",\r\n        name: \"Sheet 1\",\r\n        cellData,\r\n        rowCount: Math.max(maxRow + 1, 20),\r\n        columnCount: Math.max(maxCol + 1, 20),\r\n        defaultRowHeight: 28,\r\n        defaultColumnWidth: 93,\r\n      },\r\n    },\r\n  };\r\n};\r\n\r\nexport const transformMatrixToApi = (\r\n  cellData,\r\n  setExcelCellMatrix,\r\n  userInteractedCells = new Set(),\r\n  maskedCells = [],\r\n  existingCellMatrix = null\r\n) => {\r\n\r\n  console.log(cellData, \"cell data\")\r\n  const structuredMatrix = JSON.parse(JSON.stringify(existingCellMatrix || {}));\r\n  const apiExcelData = {};\r\n\r\n  // Log what cells are currently tracked as user-interacted\r\n  console.log(\r\n    \"User-interacted cells before processing:\",\r\n    Array.from(userInteractedCells)\r\n  );\r\n\r\n  if (!cellData)\r\n    return { cellMatrix: structuredMatrix, excelData: apiExcelData };\r\n\r\n  Object.entries(cellData).forEach(([rowIndex, row]) => {\r\n    Object.entries(row).forEach(([colIndex, cell]) => {\r\n      if (!structuredMatrix[rowIndex]) structuredMatrix[rowIndex] = {};\r\n      structuredMatrix[rowIndex][colIndex] = { ...cell };\r\n\r\n      const colLetter = columnToLetter(parseInt(colIndex));\r\n      const rowNumber = parseInt(rowIndex) + 1;\r\n      const ref = `${colLetter}${rowNumber}`;\r\n      const isMaskedCell = maskedCells.includes(ref);\r\n\r\n      if (isMaskedCell) {\r\n        console.log(\"ahhhahaha\");\r\n        if (userInteractedCells.has(ref)) {\r\n          const isZeroWithoutFormula =\r\n            (cell.v === 0 || cell.v === \"0\") &&\r\n            !cell.f &&\r\n            (!cell.m || cell.m === \"\");\r\n\r\n          if (isZeroWithoutFormula) {\r\n            console.log(\r\n              `⛔ Skipping ${ref}: zero with no formula or display value`\r\n            );\r\n            return;\r\n          }\r\n\r\n          const isNumber =\r\n            typeof cell.v === \"number\" || (!isNaN(cell.v) && cell.t === 2);\r\n          const finalValue = isNumber ? Number(cell.v) : String(cell.v);\r\n          apiExcelData[ref] = finalValue;\r\n          console.log(\r\n            `✅ Including masked cell ${ref}:`,\r\n            finalValue,\r\n            `formula:`,\r\n            cell.f || \"none\"\r\n          );\r\n        }\r\n      } else {\r\n        if (\r\n          (cell?.v !== undefined && cell.v !== null && cell.v !== \"\") ||\r\n          (cell?.v === 0 && cell?.f && userInteractedCells.has(ref))\r\n        ) {\r\n          // For non-masked cells, only include zeros if they have formulas\r\n          const isNumber =\r\n            typeof cell.v === \"number\" || (!isNaN(cell.v) && cell.t === 2);\r\n          apiExcelData[ref] = isNumber ? Number(cell.v) : String(cell.v);\r\n        }\r\n      }\r\n    });\r\n  });\r\n\r\n  // Final safety check - remove any zeros in masked cells that somehow made it through\r\n  const finalApiData = {};\r\n  Object.entries(apiExcelData).forEach(([cellRef, value]) => {\r\n    const isMaskedCell = maskedCells.includes(cellRef);\r\n\r\n    if (isMaskedCell && value === 0) {\r\n      console.log(\r\n        `FINAL CHECK: Removing zero from masked cell ${cellRef} before API submission`\r\n      );\r\n      // Don't include this cell\r\n    } else {\r\n      finalApiData[cellRef] = value;\r\n    }\r\n  });\r\n\r\n  for (const row in structuredMatrix) {\r\n    for (const col in structuredMatrix[row]) {\r\n      const cell = structuredMatrix[row][col];\r\n      const colLetter = columnToLetter(Number(col));\r\n      const rowNumber = Number(row) + 1;\r\n      const ref = `${colLetter}${rowNumber}`;\r\n      const isMasked = maskedCells.includes(ref);\r\n      const isUserInteracted = userInteractedCells.has(ref);\r\n\r\n      const isZeroCell =\r\n        (cell.v === 0 || cell.v === \"0\") &&\r\n        !cell.f &&\r\n        (!cell.m || cell.m === \"\") &&\r\n        !isUserInteracted;\r\n\r\n      if (isZeroCell) {\r\n        delete structuredMatrix[row][col];\r\n      }\r\n    }\r\n    if (Object.keys(structuredMatrix[row]).length === 0) {\r\n      delete structuredMatrix[row];\r\n    }\r\n  }\r\n\r\n  if (typeof setExcelCellMatrix === \"function\") {\r\n    setExcelCellMatrix(structuredMatrix);\r\n  }\r\n\r\n  console.log(finalApiData, \"final api data\")\r\n\r\n  return { cellMatrix: structuredMatrix, excelData: finalApiData };\r\n};\r\n\r\nexport const transformApiMatrixToSheetData = (\r\n  cellMatrix,\r\n  maskedCells = [],\r\n  highlightAllMaskedCells = false\r\n) => {\r\n  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));\r\n\r\n  Object.entries(cellData).forEach(([rowIndex, row]) => {\r\n    Object.entries(row).forEach(([colIndex, cell]) => {\r\n      if (!cell.s) cell.s = {};\r\n      delete cell.s.ht;\r\n      delete cell.s.vt;\r\n      delete cell.s.tb;\r\n      cell.s.ht = 1;\r\n    });\r\n  });\r\n  if (Array.isArray(maskedCells)) {\r\n    maskedCells.forEach((cellRef) => {\r\n      const indices = cellRefToIndices(cellRef);\r\n      if (!indices) return;\r\n      const { rowIndex, colIndex } = indices;\r\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\r\n      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};\r\n\r\n      const style = {\r\n        bg: { rgb: \"#FFFF00\" },\r\n        ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n      };\r\n\r\n      cellData[rowIndex][colIndex].s = style;\r\n      if (!highlightAllMaskedCells) {\r\n        cellData[rowIndex][colIndex].v = \"\";\r\n        if (cellData[rowIndex][colIndex].f)\r\n          delete cellData[rowIndex][colIndex].f;\r\n      }\r\n    });\r\n  }\r\n\r\n  return {\r\n    id: `workbook-${Date.now()}`,\r\n    name: \"universheet\",\r\n    sheetOrder: [\"sheet-01\"],\r\n    sheets: {\r\n      \"sheet-01\": {\r\n        type: SheetTypes.GRID,\r\n        id: \"sheet-01\",\r\n        name: \"Sheet 1\",\r\n        cellData,\r\n        rowCount: 20,\r\n        columnCount: 20,\r\n        defaultRowHeight: 28,\r\n        defaultColumnWidth: 93,\r\n      },\r\n    },\r\n  };\r\n};\r\n\r\nexport const transformMatrixWithResponseOverlay = (\r\n  cellMatrix,\r\n  responseSubmitted,\r\n  maskedCells = [],\r\n  userInteractedCellsRef = null\r\n) => {\r\n  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));\r\n\r\n  Object.entries(cellData).forEach(([rowIndex, row]) => {\r\n    Object.entries(row).forEach(([colIndex, cell]) => {\r\n      if (!cell.s) cell.s = {};\r\n      delete cell.s.ht;\r\n      delete cell.s.vt;\r\n      delete cell.s.tb;\r\n      cell.s.ht = 1;\r\n    });\r\n  });\r\n\r\n  const isMatrixFormat =\r\n    responseSubmitted &&\r\n    typeof responseSubmitted === \"object\" &&\r\n    Object.keys(responseSubmitted).some((key) => !isNaN(key));\r\n\r\n  if (Array.isArray(maskedCells)) {\r\n    maskedCells.forEach((cellRef) => {\r\n      const indices = cellRefToIndices(cellRef);\r\n      if (!indices) return;\r\n      const { rowIndex, colIndex } = indices;\r\n      if (!cellData[rowIndex]) cellData[rowIndex] = {};\r\n      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};\r\n\r\n      const style = {\r\n        bg: { rgb: \"#FFFF00\" },\r\n        ht: 1,\r\n      };\r\n\r\n      cellData[rowIndex][colIndex].s = style;\r\n\r\n      let submittedValue = null;\r\n\r\n      if (responseSubmitted) {\r\n        if (isMatrixFormat) {\r\n          const submittedCell = responseSubmitted[rowIndex]?.[colIndex];\r\n          if (submittedCell) {\r\n            // Track this cell as user-interacted since it has submitted data\r\n            if (userInteractedCellsRef?.current) {\r\n              userInteractedCellsRef.current.add(cellRef);\r\n            }\r\n\r\n            cellData[rowIndex][colIndex] = {\r\n              ...cellData[rowIndex][colIndex],\r\n              ...submittedCell,\r\n              s: {\r\n                ...cellData[rowIndex][colIndex].s,\r\n                ...submittedCell.s,\r\n                bg: { rgb: \"#FFFF00\" },\r\n                ht: 1,\r\n              },\r\n            };\r\n\r\n            if (cellData[rowIndex][colIndex].s?.n) {\r\n              let pattern = cellData[rowIndex][colIndex].s.n;\r\n              if (typeof pattern === \"string\") {\r\n                pattern = pattern.replace(/\\\\\"/g, '\"');\r\n                cellData[rowIndex][colIndex].s.n = { pattern: pattern };\r\n              } else if (pattern.pattern) {\r\n                let cleanPattern = pattern.pattern.replace(/\\\\\"/g, '\"');\r\n                cellData[rowIndex][colIndex].s.n = { pattern: cleanPattern };\r\n              }\r\n\r\n              if (\r\n                submittedCell.m &&\r\n                submittedCell.m !== cellData[rowIndex][colIndex].v\r\n              ) {\r\n                cellData[rowIndex][colIndex].m = submittedCell.m;\r\n              }\r\n            }\r\n\r\n            return;\r\n          }\r\n        } else {\r\n          submittedValue = responseSubmitted[cellRef];\r\n          // Track this cell as user-interacted if it has submitted data\r\n          if (\r\n            submittedValue !== undefined &&\r\n            submittedValue !== null &&\r\n            submittedValue !== \"\" &&\r\n            userInteractedCellsRef?.current\r\n          ) {\r\n            userInteractedCellsRef.current.add(cellRef);\r\n          }\r\n        }\r\n      }\r\n\r\n      if (\r\n        submittedValue !== undefined &&\r\n        submittedValue !== null &&\r\n        submittedValue !== \"\"\r\n      ) {\r\n        let cellType = 1;\r\n        let finalValue = submittedValue;\r\n\r\n        if (\r\n          typeof submittedValue === \"string\" &&\r\n          submittedValue.startsWith(\"=\")\r\n        ) {\r\n          cellData[rowIndex][colIndex].f = submittedValue;\r\n        } else {\r\n          if (typeof submittedValue === \"number\") {\r\n            finalValue = submittedValue;\r\n            cellType = 2;\r\n          } else if (typeof submittedValue === \"string\") {\r\n            const stringValue = submittedValue.trim();\r\n            if (\r\n              !isNaN(stringValue) &&\r\n              !isNaN(parseFloat(stringValue)) &&\r\n              stringValue !== \"\"\r\n            ) {\r\n              finalValue = Number(stringValue);\r\n              cellType = 2;\r\n            } else {\r\n              finalValue = submittedValue;\r\n              cellType = 1;\r\n            }\r\n          }\r\n\r\n          cellData[rowIndex][colIndex].v = finalValue;\r\n          cellData[rowIndex][colIndex].t = cellType;\r\n\r\n          if (cellType === 2) {\r\n            if (!cellData[rowIndex][colIndex].s) {\r\n              cellData[rowIndex][colIndex].s = {};\r\n            }\r\n\r\n            // Check if there's already a pattern in the original cell data\r\n            const originalCell = cellMatrix?.[rowIndex]?.[colIndex];\r\n            if (originalCell?.s?.n) {\r\n              // Clean up the existing pattern - remove escape slashes\r\n              let pattern = originalCell.s.n;\r\n              if (typeof pattern === \"string\") {\r\n                pattern = pattern.replace(/\\\\\"/g, '\"');\r\n                cellData[rowIndex][colIndex].s.n = { pattern: pattern };\r\n              } else if (pattern.pattern) {\r\n                let cleanPattern = pattern.pattern.replace(/\\\\\"/g, '\"');\r\n                cellData[rowIndex][colIndex].s.n = { pattern: cleanPattern };\r\n              }\r\n            } else {\r\n              // Fallback to default currency pattern - detect from value or use USD as default\r\n              const cellValue = cellData[rowIndex][colIndex].v;\r\n              const detectedSymbol = detectCurrencySymbol(\r\n                String(cellValue || \"\")\r\n              );\r\n              const formatPattern = getCurrencyFormatPattern(\r\n                detectedSymbol || \"$\"\r\n              );\r\n              cellData[rowIndex][colIndex].s.n = { pattern: formatPattern };\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        cellData[rowIndex][colIndex].v = \"\";\r\n        cellData[rowIndex][colIndex].t = 1;\r\n        if (cellData[rowIndex][colIndex].f) {\r\n          delete cellData[rowIndex][colIndex].f;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  console.log(cellData, \"my god data\");\r\n  return {\r\n    id: `workbook-${Date.now()}`,\r\n    name: \"universheet\",\r\n    sheetOrder: [\"sheet-01\"],\r\n    sheets: {\r\n      \"sheet-01\": {\r\n        type: SheetTypes.GRID,\r\n        id: \"sheet-01\",\r\n        name: \"Sheet 1\",\r\n        cellData,\r\n        rowCount: 20,\r\n        columnCount: 20,\r\n        defaultRowHeight: 28,\r\n        defaultColumnWidth: 93,\r\n      },\r\n    },\r\n  };\r\n};\r\nconst ExcelSheets = forwardRef(\r\n  (\r\n    {\r\n      cellsData,\r\n      maskedCells,\r\n      SetExcelApiData,\r\n      SetCellsFilled,\r\n      cellsFilled,\r\n      apiData,\r\n      excelID,\r\n      responseSubmitted,\r\n      highlightAllMaskedCells = false,\r\n      setExcelCellMatrix = null,\r\n    },\r\n    ref\r\n  ) => {\r\n    const [workbookData, setWorkbookData] = useState(null);\r\n    const univerCreatedRef = useRef(false);\r\n    const univerAPIRef = useRef(null);\r\n    const workbookDataRef = useRef(null);\r\n    const pendingChangesRef = useRef(new Map()); // Track pending changes\r\n    const debouncedHandleRef = useRef(null);\r\n    const userInteractedCellsRef = useRef(new Set()); // Track cells user actually interacted with\r\n\r\n    // Expose method to force sync all data immediately\r\n    useImperativeHandle(ref, () => ({\r\n      syncData: () => {\r\n        // Cancel any pending debounced calls\r\n        if (debouncedHandleRef.current) {\r\n          debouncedHandleRef.current.cancel?.();\r\n        }\r\n        // Process any pending changes immediately\r\n        handleDataChangeImmediate();\r\n      },\r\n    }));\r\n\r\n    // Helper function to check if all masked cells are filled in responseSubmitted\r\n    const areAllMaskedCellsFilled = useCallback(() => {\r\n      if (!responseSubmitted || !maskedCells || maskedCells.length === 0) {\r\n        return false;\r\n      }\r\n\r\n      return maskedCells.every((cellRef) => {\r\n        const value = responseSubmitted[cellRef];\r\n        return value !== undefined && value !== null && value !== \"\";\r\n      });\r\n    }, [responseSubmitted, maskedCells]);\r\n\r\n    useEffect(() => {\r\n      let matrix;\r\n\r\n      if (apiData?.cellMatrix && !responseSubmitted) {\r\n        matrix = transformApiMatrixToSheetData(\r\n          apiData.cellMatrix,\r\n          maskedCells,\r\n          highlightAllMaskedCells\r\n        );\r\n      } else if (responseSubmitted) {\r\n        if (apiData?.cellMatrix) {\r\n          const responseData = apiData?.responseSubmitted?.cellMatrixResponse;\r\n\r\n          if (responseData) {\r\n            matrix = transformMatrixWithResponseOverlay(\r\n              apiData.cellMatrix,\r\n              responseData,\r\n              maskedCells,\r\n              userInteractedCellsRef\r\n            );\r\n          } else {\r\n            matrix = transformMatrixWithResponseOverlay(\r\n              apiData.cellMatrix,\r\n              responseSubmitted,\r\n              maskedCells,\r\n              userInteractedCellsRef\r\n            );\r\n          }\r\n        } else {\r\n          matrix = transformApiToMatrix({\r\n            excelData: responseSubmitted,\r\n            maskedCells,\r\n            highlightAllMaskedCells,\r\n          });\r\n        }\r\n      } else {\r\n        matrix = transformApiToMatrix({\r\n          excelData: apiData?.excelData,\r\n          maskedCells,\r\n          highlightAllMaskedCells,\r\n        });\r\n      }\r\n      // Only proceed if we have a valid matrix\r\n      if (matrix) {\r\n        const initialWorkbook = {\r\n          ...matrix,\r\n          locale: CoreLocaleType.EN_US,\r\n          appVersion: \"3.0.0-alpha\",\r\n        };\r\n\r\n        setWorkbookData(initialWorkbook);\r\n        workbookDataRef.current = initialWorkbook;\r\n        univerCreatedRef.current = false;\r\n        pendingChangesRef.current.clear();\r\n\r\n        // If we have a response submitted, update the API data before clearing user interactions\r\n        if (responseSubmitted && matrix?.sheets?.[\"sheet-01\"]?.cellData) {\r\n          SetExcelApiData(\r\n            transformMatrixToApi(\r\n              matrix.sheets[\"sheet-01\"].cellData,\r\n              setExcelCellMatrix,\r\n              userInteractedCellsRef.current,\r\n              maskedCells,\r\n              apiData?.responseSubmitted?.cellMatrixResponse || null\r\n            )\r\n          );\r\n        }\r\n\r\n        // Only clear user interactions if we don't have a response submitted\r\n        if (!responseSubmitted) {\r\n          userInteractedCellsRef.current.clear();\r\n        }\r\n      }\r\n    }, [\r\n      apiData,\r\n      maskedCells,\r\n      highlightAllMaskedCells,\r\n      responseSubmitted,\r\n      areAllMaskedCellsFilled,\r\n    ]);\r\n\r\n    // Immediate data change handler (no debounce)\r\n    const handleDataChangeImmediate = useCallback(() => {\r\n      const univerAPI = univerAPIRef.current;\r\n      const book = univerAPI?.getActiveWorkbook();\r\n      const sheet = book?.getActiveSheet();\r\n      if (!sheet) return;\r\n\r\n      const refWorkbook = workbookDataRef.current;\r\n      const existingCellData =\r\n        refWorkbook?.sheets?.[\"sheet-01\"]?.cellData || {};\r\n      const updatedCellData = {};\r\n\r\n      const sheetData = sheet.getSheet().getSnapshot();\r\n      const maxRow = sheetData.rowCount - 1;\r\n      const maxCol = sheetData.columnCount - 1;\r\n\r\n      // Process all cells including pending changes\r\n      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\r\n        for (let colNum = 0; colNum <= maxCol; colNum++) {\r\n          const cellKey = `${rowNum}-${colNum}`;\r\n          const range = sheet.getRange(rowNum, colNum);\r\n          const formula = range.getFormula() || \"\";\r\n          let calculatedValue = range.getValue();\r\n\r\n          // For formulas, also get the display value to ensure we have the calculated result\r\n          let displayValue = calculatedValue;\r\n          if (formula && typeof range.getDisplayValue === \"function\") {\r\n            displayValue = range.getDisplayValue();\r\n            // If display value is different and not empty, use it as the calculated value\r\n            if (displayValue !== null && displayValue !== undefined && displayValue !== calculatedValue) {\r\n              calculatedValue = displayValue;\r\n            }\r\n          }\r\n\r\n          // Don't convert 0 to empty string - 0 is a valid value\r\n          if (calculatedValue === null || calculatedValue === undefined) {\r\n            calculatedValue = \"\";\r\n          }\r\n\r\n          // Check if there's a pending change for this cell\r\n          if (pendingChangesRef.current.has(cellKey)) {\r\n            calculatedValue = pendingChangesRef.current.get(cellKey);\r\n          }\r\n\r\n          const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;\r\n          const existingCell = existingCellData?.[rowNum]?.[colNum];\r\n          const isMaskedCell = maskedCells.includes(cellRef);\r\n          const wasUserInteracted = userInteractedCellsRef.current.has(cellRef);\r\n\r\n          const isBlankLike =\r\n            calculatedValue === \"\" ||\r\n            calculatedValue === null ||\r\n            (typeof calculatedValue === \"number\" &&\r\n              calculatedValue === 0 &&\r\n              !formula);\r\n\r\n          const hasData = isMaskedCell\r\n            ? wasUserInteracted && !isBlankLike\r\n            : (wasUserInteracted && !isBlankLike) ||\r\n              formula ||\r\n              pendingChangesRef.current.has(cellKey);\r\n\r\n          if (hasData) {\r\n            if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};\r\n\r\n            // Determine proper type for the cell value\r\n            let cellType = 1; // Default to text\r\n            let finalValue = calculatedValue;\r\n\r\n            // For formulas, ensure we use the calculated numeric value if possible\r\n            if (formula && calculatedValue !== \"\" && typeof calculatedValue !== \"undefined\") {\r\n              // Try to convert to number if it's a numeric result\r\n              if (typeof calculatedValue === \"string\") {\r\n                const cleaned = calculatedValue.replace(/[,%$]/g, \"\");\r\n                if (!isNaN(cleaned) && !isNaN(parseFloat(cleaned))) {\r\n                  finalValue = Number(cleaned);\r\n                  cellType = 2; // Number type\r\n                } else {\r\n                  finalValue = calculatedValue;\r\n                  cellType = 1; // Text type\r\n                }\r\n              } else if (typeof calculatedValue === \"number\") {\r\n                finalValue = calculatedValue;\r\n                cellType = 2; // Number type\r\n              }\r\n            } else if (\r\n              !formula &&\r\n              calculatedValue !== \"\" &&\r\n              typeof calculatedValue !== \"undefined\"\r\n            ) {\r\n              // For non-formula cells, check if the value is a number\r\n              const stringValue = String(calculatedValue).trim();\r\n              if (!isNaN(stringValue) && !isNaN(parseFloat(stringValue))) {\r\n                finalValue = Number(stringValue);\r\n                cellType = 2; // Number type\r\n              }\r\n            }\r\n\r\n            const newCellData = {\r\n              v: finalValue,\r\n              t: cellType,\r\n              s: {\r\n                ...(existingCell?.s || {}),\r\n                ht: 1, // Ensure left alignment (1 = left, 2 = center, 3 = right)\r\n              },\r\n            };\r\n            if (formula) {\r\n              newCellData.f = formula;\r\n            }\r\n            if (isMaskedCell) {\r\n              newCellData.s = {\r\n                ...newCellData.s,\r\n                bg: { rgb: \"#FFFF00\" },\r\n                ht: 1, // Ensure left alignment for masked cells (1 = left, 2 = center, 3 = right)\r\n              };\r\n              if (highlightAllMaskedCells) {\r\n                newCellData.v = \"\";\r\n              }\r\n            }\r\n            updatedCellData[rowNum][colNum] = newCellData;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Clear pending changes as they've been processed\r\n      pendingChangesRef.current.clear();\r\n\r\n      workbookDataRef.current = {\r\n        ...refWorkbook,\r\n        sheets: {\r\n          ...refWorkbook.sheets,\r\n          [\"sheet-01\"]: {\r\n            ...refWorkbook.sheets[\"sheet-01\"],\r\n            cellData: updatedCellData,\r\n          },\r\n        },\r\n      };\r\n\r\n      SetExcelApiData(\r\n        transformMatrixToApi(\r\n          updatedCellData,\r\n          setExcelCellMatrix,\r\n          userInteractedCellsRef.current,\r\n          maskedCells,\r\n          apiData?.responseSubmitted?.cellMatrixResponse || null\r\n        )\r\n      );\r\n\r\n      if (highlightAllMaskedCells) {\r\n        SetCellsFilled(false);\r\n      } else {\r\n        let anyFilled = false;\r\n        for (const cellRef of maskedCells) {\r\n          const { rowIndex, colIndex } = cellRefToIndices(cellRef);\r\n          const cell = updatedCellData?.[rowIndex]?.[colIndex];\r\n          if (cell?.v !== undefined && cell.v !== null && cell.v !== \"\") {\r\n            anyFilled = true;\r\n            break;\r\n          }\r\n        }\r\n        SetCellsFilled(anyFilled);\r\n      }\r\n    }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);\r\n\r\n    // Enhanced data change handler that preserves formatting during paste operations\r\n    const handleFormattedDataChange = useCallback(() => {\r\n      try {\r\n        const univerAPI = univerAPIRef.current;\r\n        const book = univerAPI?.getActiveWorkbook();\r\n        const sheet = book?.getActiveSheet();\r\n        if (!sheet || typeof sheet.getRange !== \"function\") return;\r\n\r\n        const refWorkbook = workbookDataRef.current;\r\n        const existingCellData =\r\n          refWorkbook?.sheets?.[\"sheet-01\"]?.cellData || {};\r\n        const updatedCellData = {};\r\n\r\n        const sheetData = sheet.getSheet().getSnapshot();\r\n        const maxRow = sheetData.rowCount - 1;\r\n        const maxCol = sheetData.columnCount - 1;\r\n\r\n        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\r\n          for (let colNum = 0; colNum <= maxCol; colNum++) {\r\n            const cellKey = `${rowNum}-${colNum}`;\r\n            const range = sheet.getRange(rowNum, colNum);\r\n            if (!range || typeof range.getFormula !== \"function\") continue;\r\n\r\n            const formula = range.getFormula();\r\n            const rawValue = range.getValue();\r\n            const displayValue = range.getDisplayValue?.() ?? rawValue;\r\n            const styleFromSheet = range.getStyle?.() || {};\r\n\r\n            if (\r\n              formula ||\r\n              rawValue !== null ||\r\n              maskedCells.includes(`${columnToLetter(colNum)}${rowNum + 1}`)\r\n            ) {\r\n              if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};\r\n\r\n              const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;\r\n              const isMasked = maskedCells.includes(cellRef);\r\n\r\n              let format = styleFromSheet.n;\r\n              if (!format && typeof displayValue === \"string\") {\r\n                const detectedSymbol = detectCurrencySymbol(displayValue);\r\n                if (detectedSymbol) {\r\n                  format = getCurrencyFormatPattern(detectedSymbol);\r\n                } else if (displayValue.includes(\"%\")) {\r\n                  format = \"0.00%\";\r\n                }\r\n              }\r\n\r\n              let finalValue = rawValue;\r\n\r\n              if (typeof rawValue === \"string\") {\r\n                const cleaned = cleanCurrencyValue(rawValue).replace(/%/g, \"\");\r\n                finalValue = isNaN(cleaned) ? rawValue : Number(cleaned);\r\n              }\r\n\r\n              const patchedCell = {\r\n                v: finalValue,\r\n                m: displayValue,\r\n                t: typeof finalValue === \"number\" ? 2 : 1,\r\n                s: {\r\n                  ...(existingCellData?.[rowNum]?.[colNum]?.s || {}),\r\n                  ...(format ? { n: format } : {}),\r\n                  ht: 1,\r\n                  ...(isMasked ? { bg: { rgb: \"#FFFF00\" } } : {}),\r\n                },\r\n              };\r\n\r\n              if (formula) patchedCell.f = formula;\r\n\r\n              updatedCellData[rowNum][colNum] = patchedCell;\r\n            }\r\n          }\r\n        }\r\n\r\n        workbookDataRef.current = {\r\n          ...refWorkbook,\r\n          sheets: {\r\n            ...refWorkbook.sheets,\r\n            [\"sheet-01\"]: {\r\n              ...refWorkbook.sheets[\"sheet-01\"],\r\n              cellData: updatedCellData,\r\n            },\r\n          },\r\n        };\r\n\r\n        SetExcelApiData(\r\n          transformMatrixToApi(\r\n            updatedCellData,\r\n            setExcelCellMatrix,\r\n            userInteractedCellsRef.current,\r\n            maskedCells,\r\n            apiData?.responseSubmitted?.cellMatrixResponse || null\r\n          )\r\n        );\r\n\r\n        if (highlightAllMaskedCells) {\r\n          SetCellsFilled(false);\r\n        } else {\r\n          let anyFilled = false;\r\n          for (const cellRef of maskedCells) {\r\n            const { rowIndex, colIndex } = cellRefToIndices(cellRef);\r\n            const cell = updatedCellData?.[rowIndex]?.[colIndex];\r\n            if (cell?.v !== undefined && cell.v !== null && cell.v !== \"\") {\r\n              anyFilled = true;\r\n              break;\r\n            }\r\n          }\r\n          SetCellsFilled(anyFilled);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error in handleFormattedDataChange:\", error);\r\n      }\r\n    }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);\r\n\r\n    // Debounced version for regular updates\r\n    const handleDataChange = useCallback(() => {\r\n      handleDataChangeImmediate();\r\n    }, [handleDataChangeImmediate]);\r\n\r\n    useEffect(() => {\r\n      if (!workbookData || univerCreatedRef.current) return;\r\n\r\n      const { univerAPI } = createUniver({\r\n        locale: LocaleType.EN_US,\r\n        locales: { [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS) },\r\n        theme: defaultTheme,\r\n        presets: [UniverSheetsCorePreset()],\r\n      });\r\n\r\n      univerAPIRef.current = univerAPI;\r\n      univerAPI.createWorkbook(workbookData);\r\n\r\n      const initialCellData = workbookData.sheets[\"sheet-01\"].cellData;\r\n      SetExcelApiData(\r\n        transformMatrixToApi(\r\n          initialCellData,\r\n          setExcelCellMatrix,\r\n          userInteractedCellsRef.current,\r\n          maskedCells,\r\n          apiData?.responseSubmitted?.cellMatrixResponse || null\r\n        )\r\n      );\r\n\r\n      if (highlightAllMaskedCells) {\r\n        SetCellsFilled(false);\r\n      } else {\r\n        let anyMaskedFilled = false;\r\n        for (const cellRef of maskedCells) {\r\n          const { rowIndex, colIndex } = cellRefToIndices(cellRef);\r\n          const cell = initialCellData?.[rowIndex]?.[colIndex];\r\n          if (cell?.v !== undefined && cell.v !== null && cell.v !== \"\") {\r\n            anyMaskedFilled = true;\r\n            break;\r\n          }\r\n        }\r\n        SetCellsFilled(anyMaskedFilled);\r\n      }\r\n\r\n      const restrictPermissionOnCells = async () => {\r\n        const book = univerAPI?.getActiveWorkbook();\r\n        const sheet = book.getActiveSheet();\r\n        const bookId = book.getId();\r\n        const sheetId = sheet.getSheetId();\r\n        const permission = book.getPermission();\r\n        const allCells = [];\r\n        for (let row = 1; row <= 100; row++) {\r\n          for (let col = 0; col < 26; col++) {\r\n            allCells.push(`${String.fromCharCode(65 + col)}${row}`);\r\n          }\r\n        }\r\n        const cellsToLock = allCells.filter(\r\n          (cell) => !maskedCells.includes(cell)\r\n        );\r\n        const ranges = cellsToLock.map((cell) => sheet.getRange(cell));\r\n        const { permissionId } = await permission.addRangeBaseProtection(\r\n          bookId,\r\n          sheetId,\r\n          ranges\r\n        );\r\n        const editPoint =\r\n          permission.permissionPointsDefinition\r\n            .RangeProtectionPermissionEditPoint;\r\n        permission.rangeRuleChangedAfterAuth$.subscribe((id) => {\r\n          if (id === permissionId) {\r\n            permission.setRangeProtectionPermissionPoint(\r\n              bookId,\r\n              sheetId,\r\n              permissionId,\r\n              editPoint,\r\n              false\r\n            );\r\n          }\r\n        });\r\n      };\r\n\r\n      restrictPermissionOnCells();\r\n\r\n      // Create debounced version with longer delay and store reference\r\n      const debouncedHandleDataChange = debounce(handleDataChange, 100);\r\n      debouncedHandleRef.current = debouncedHandleDataChange;\r\n\r\n      univerAPI.addEvent(univerAPI.Event.SheetEditChanging, (params) => {\r\n        const newValue = params?.value?._data?.body?.dataStream;\r\n        const row = params?.row;\r\n        const column = params?.column;\r\n        if (newValue === undefined || row === undefined || column === undefined)\r\n          return;\r\n\r\n        const trimmedValue = String(newValue).trim();\r\n        const cellKey = `${row}-${column}`;\r\n        const cellRef = `${columnToLetter(column)}${row + 1}`;\r\n\r\n        // Track that user interacted with this cell\r\n        userInteractedCellsRef.current.add(cellRef);\r\n\r\n        // Store pending change - but store the properly typed value\r\n        let typedValue = trimmedValue;\r\n        let cellType = 1; // Default to text\r\n\r\n        // Check if the value is a number\r\n        if (\r\n          trimmedValue !== \"\" &&\r\n          !isNaN(trimmedValue) &&\r\n          !isNaN(parseFloat(trimmedValue))\r\n        ) {\r\n          typedValue = Number(trimmedValue);\r\n          cellType = 2; // Number type\r\n        }\r\n\r\n        pendingChangesRef.current.set(cellKey, typedValue);\r\n\r\n        const refWorkbook = workbookDataRef.current;\r\n        const cellData = refWorkbook?.sheets?.[\"sheet-01\"]?.cellData || {};\r\n        const updatedCellData = { ...cellData };\r\n        if (!updatedCellData[row]) updatedCellData[row] = {};\r\n        const existingCell = updatedCellData[row][column] || {};\r\n\r\n        const isMaskedCell = maskedCells.some((cellRef) => {\r\n          const { rowIndex, colIndex } = cellRefToIndices(cellRef);\r\n          return rowIndex === row && colIndex === column;\r\n        });\r\n\r\n        if (isMaskedCell) {\r\n          if (highlightAllMaskedCells) {\r\n            updatedCellData[row][column] = {\r\n              ...existingCell,\r\n              v: \"\",\r\n              t: 1,\r\n              s: {\r\n                bg: { rgb: \"#FFFF00\" },\r\n                ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n              },\r\n            };\r\n          } else {\r\n            updatedCellData[row][column] = {\r\n              ...existingCell,\r\n              v: typedValue,\r\n              t: cellType,\r\n              s: {\r\n                bg: { rgb: \"#FFFF00\" },\r\n                ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n              },\r\n            };\r\n          }\r\n        } else {\r\n          updatedCellData[row][column] = {\r\n            ...existingCell,\r\n            v: typedValue,\r\n            t: cellType,\r\n            s: {\r\n              ...(existingCell?.s || {}),\r\n              ht: 1, // Left align text (1 = left, 2 = center, 3 = right)\r\n            },\r\n          };\r\n        }\r\n\r\n        workbookDataRef.current = {\r\n          ...refWorkbook,\r\n          sheets: {\r\n            ...refWorkbook.sheets,\r\n            [\"sheet-01\"]: {\r\n              ...refWorkbook.sheets[\"sheet-01\"],\r\n              cellData: updatedCellData,\r\n            },\r\n          },\r\n        };\r\n\r\n        debouncedHandleDataChange();\r\n      });\r\n\r\n      // Use the enhanced data change handler for better formatting preservation\r\n      const debouncedFormattedDataChange = debounce(\r\n        handleFormattedDataChange,\r\n        100\r\n      );\r\n\r\n      // Aggressive formula formatting handler - triggers on ANY value change\r\n      const handleFormulaFormatting = () => {\r\n        const book = univerAPI?.getActiveWorkbook();\r\n        const sheet = book?.getActiveSheet();\r\n        if (!sheet) return;\r\n\r\n        // Check all cells for formulas that need formatting\r\n        const sheetData = sheet.getSheet().getSnapshot();\r\n        const maxRow = Math.min(sheetData.rowCount - 1, 100); // Limit to reasonable range\r\n        const maxCol = Math.min(sheetData.columnCount - 1, 26);\r\n\r\n        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\r\n          for (let colNum = 0; colNum <= maxCol; colNum++) {\r\n            const range = sheet.getRange(rowNum, colNum);\r\n            const formula = range.getFormula();\r\n            const calculatedValue = range.getValue();\r\n\r\n            if (\r\n              formula &&\r\n              formula.startsWith(\"=\") &&\r\n              typeof calculatedValue === \"number\"\r\n            ) {\r\n              const cellRefs = formula.match(/[A-Z]+\\d+/g);\r\n              if (cellRefs) {\r\n                let shouldApplyCurrencyFormat = false;\r\n\r\n                for (const cellRef of cellRefs) {\r\n                  const refIndices = cellRefToIndices(cellRef);\r\n                  if (refIndices) {\r\n                    const refRange = sheet.getRange(\r\n                      refIndices.rowIndex,\r\n                      refIndices.colIndex\r\n                    );\r\n                    const refDisplayValue = refRange.getDisplayValue();\r\n                    if (\r\n                      refDisplayValue &&\r\n                      typeof refDisplayValue === \"string\" &&\r\n                      detectCurrencySymbol(refDisplayValue)\r\n                    ) {\r\n                      shouldApplyCurrencyFormat =\r\n                        detectCurrencySymbol(refDisplayValue);\r\n                      break;\r\n                    }\r\n                  }\r\n                }\r\n\r\n                if (shouldApplyCurrencyFormat) {\r\n                  const currentDisplayValue = range.getDisplayValue();\r\n                  // Only apply if not already formatted with the detected currency\r\n                  if (\r\n                    !currentDisplayValue ||\r\n                    !detectCurrencySymbol(currentDisplayValue)\r\n                  ) {\r\n                    try {\r\n                      const formatPattern = getCurrencyFormatPattern(\r\n                        shouldApplyCurrencyFormat\r\n                      );\r\n                      range.setNumberFormat(formatPattern);\r\n                    } catch (e) {}\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      };\r\n\r\n      const captureAllFormulaResults = () => {\r\n        const univerAPI = univerAPIRef.current;\r\n        const book = univerAPI?.getActiveWorkbook();\r\n        const sheet = book?.getActiveSheet();\r\n        if (sheet) {\r\n          const sheetData = sheet.getSheet().getSnapshot();\r\n          const maxRow = sheetData.rowCount - 1;\r\n          const maxCol = sheetData.columnCount - 1;\r\n\r\n          for (let rowNum = 0; rowNum <= maxRow; rowNum++) {\r\n            for (let colNum = 0; colNum <= maxCol; colNum++) {\r\n              const range = sheet.getRange(rowNum, colNum);\r\n              if (range) {\r\n                const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;\r\n                const isMaskedCell = maskedCells.includes(cellRef);\r\n\r\n                // For masked cells, be very strict about what counts as user interaction\r\n                if (isMaskedCell) {\r\n                  let shouldTrack = false;\r\n\r\n                  // Check for formula first - formulas are always meaningful\r\n                  if (typeof range.getFormula === \"function\") {\r\n                    const formula = range.getFormula();\r\n                    if (formula) {\r\n                      shouldTrack = true;\r\n                    }\r\n                  }\r\n\r\n                  // For values without formulas, be very restrictive\r\n                  if (!shouldTrack && typeof range.getValue === \"function\") {\r\n                    const value = range.getValue();\r\n                    // ONLY track if:\r\n                    // 1. Value is not zero (zeros are likely from drag)\r\n                    // 2. Value is not empty/null/undefined\r\n                    // 3. Cell was already tracked (preserve existing tracking)\r\n                    if (\r\n                      value !== null &&\r\n                      value !== undefined &&\r\n                      value !== \"\" &&\r\n                      value !== 0 &&\r\n                      !userInteractedCellsRef.current.has(cellRef)\r\n                    ) {\r\n                      shouldTrack = true;\r\n                    }\r\n                  }\r\n\r\n                  // Only add to tracking if we're sure it's meaningful\r\n                  if (shouldTrack) {\r\n                    userInteractedCellsRef.current.add(cellRef);\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // Clean up any cells that were marked as user-interacted but only have zeros\r\n        // This is an aggressive cleanup for drag-generated zeros\r\n        const cellsToRemove = [];\r\n        userInteractedCellsRef.current.forEach((cellRef) => {\r\n          const indices = cellRefToIndices(cellRef);\r\n          if (indices) {\r\n            const { rowIndex, colIndex } = indices;\r\n            const range = sheet.getRange(rowIndex, colIndex);\r\n            if (range) {\r\n              const value = range.getValue();\r\n              const formula = range.getFormula();\r\n              const isMaskedCell = maskedCells.includes(cellRef);\r\n\r\n              // Remove from tracking if it's a masked cell with zero and no formula\r\n              if (isMaskedCell && value === 0 && !formula) {\r\n                cellsToRemove.push(cellRef);\r\n              }\r\n            }\r\n          }\r\n        });\r\n\r\n        // Remove the problematic cells from tracking\r\n        cellsToRemove.forEach((cellRef) => {\r\n          console.log(\r\n            `Removing cell ${cellRef} from user interaction tracking - had zero without formula`\r\n          );\r\n          userInteractedCellsRef.current.delete(cellRef);\r\n        });\r\n\r\n        if (cellsToRemove.length > 0) {\r\n          console.log(\r\n            `Cleaned up ${cellsToRemove.length} cells with drag-generated zeros`\r\n          );\r\n        }\r\n\r\n        // Force immediate data capture - no delays, no complications\r\n        handleDataChangeImmediate();\r\n\r\n        // Also trigger the formatted version as backup\r\n        setTimeout(() => {\r\n          handleFormattedDataChange();\r\n        }, 100);\r\n      };\r\n\r\n      // Trigger formula formatting on multiple events\r\n      const debouncedFormulaFormatting = debounce(handleFormulaFormatting, 200);\r\n\r\n      // Listen to SheetValueChanged for immediate formatting\r\n      univerAPI.addEvent(univerAPI.Event.SheetValueChanged, () => {\r\n        debouncedFormulaFormatting();\r\n        debouncedFormattedDataChange();\r\n      });\r\n\r\n      // AGGRESSIVE DRAG DETECTION - Capture data on ANY mouse activity\r\n      const containerElement = document.getElementById(excelID);\r\n      if (containerElement) {\r\n        let isDragging = false;\r\n        let dragTimer = null;\r\n        let isTyping = false;\r\n\r\n        const handleMouseDown = (e) => {\r\n          if (e.target.closest(\".univer-container\")) {\r\n            isDragging = false;\r\n            isTyping = false;\r\n            // Clear any existing timer\r\n            if (dragTimer) {\r\n              clearTimeout(dragTimer);\r\n              dragTimer = null;\r\n            }\r\n          }\r\n        };\r\n\r\n        // Track actual typing/input events\r\n        const handleKeyDown = (e) => {\r\n          if (e.target.closest(\".univer-container\")) {\r\n            isTyping = true;\r\n            // Reset typing flag after a short delay\r\n            setTimeout(() => {\r\n              isTyping = false;\r\n            }, 1000);\r\n          }\r\n        };\r\n\r\n        const handleMouseMove = (e) => {\r\n          if (e.buttons === 1) {\r\n            isDragging = true;\r\n          }\r\n        };\r\n\r\n        const handleMouseUp = () => {\r\n          if (isDragging) {\r\n            setTimeout(() => {\r\n              captureAllFormulaResults();\r\n            }, 100);\r\n\r\n            setTimeout(() => {\r\n              captureAllFormulaResults();\r\n            }, 300);\r\n\r\n            setTimeout(() => {\r\n              captureAllFormulaResults();\r\n            }, 600);\r\n\r\n            isDragging = false;\r\n          }\r\n        };\r\n\r\n        containerElement.addEventListener(\"mousedown\", handleMouseDown);\r\n        containerElement.addEventListener(\"mousemove\", handleMouseMove);\r\n        containerElement.addEventListener(\"mouseup\", handleMouseUp);\r\n        containerElement.addEventListener(\"keydown\", handleKeyDown);\r\n\r\n        // Store references for cleanup\r\n        containerElement._dragHandlers = {\r\n          mousedown: handleMouseDown,\r\n          mousemove: handleMouseMove,\r\n          mouseup: handleMouseUp,\r\n          keydown: handleKeyDown,\r\n        };\r\n\r\n        const handlePaste = () => {\r\n          setTimeout(() => {\r\n            debouncedFormattedDataChange();\r\n          }, 100);\r\n        };\r\n\r\n        containerElement.addEventListener(\"paste\", handlePaste);\r\n\r\n        // Store all references for cleanup\r\n        containerElement._pasteHandler = handlePaste;\r\n      }\r\n\r\n      // Also try to listen for any available fill/drag events\r\n      try {\r\n        // Try different possible event names for drag operations\r\n        const possibleEvents = [\r\n          \"SheetFillSeries\",\r\n          \"SheetAutofill\",\r\n          \"SheetDragFill\",\r\n          \"SheetCellsChanged\",\r\n        ];\r\n\r\n        possibleEvents.forEach((eventName) => {\r\n          if (univerAPI.Event[eventName]) {\r\n            univerAPI.addEvent(univerAPI.Event[eventName], (params) => {\r\n              setTimeout(() => {\r\n                debouncedFormulaFormatting();\r\n                debouncedFormattedDataChange();\r\n              }, 300);\r\n            });\r\n          }\r\n        });\r\n      } catch (e) {}\r\n\r\n      // Listen for paste events if available\r\n      try {\r\n        if (univerAPI.Event.ClipboardPasted) {\r\n          univerAPI.addEvent(\r\n            univerAPI.Event.ClipboardPasted,\r\n            debouncedFormattedDataChange\r\n          );\r\n        }\r\n      } catch (e) {}\r\n\r\n      univerCreatedRef.current = true;\r\n\r\n      return () => {\r\n        // Cancel any pending debounced calls\r\n        if (debouncedHandleRef.current?.cancel) {\r\n          debouncedHandleRef.current.cancel();\r\n        }\r\n\r\n        // Clean up DOM event listeners\r\n        const containerElement = document.getElementById(excelID);\r\n        if (containerElement) {\r\n          // Clean up paste handler\r\n          if (containerElement._pasteHandler) {\r\n            containerElement.removeEventListener(\r\n              \"paste\",\r\n              containerElement._pasteHandler\r\n            );\r\n            delete containerElement._pasteHandler;\r\n          }\r\n\r\n          // Clean up drag handlers\r\n          if (containerElement._dragHandlers) {\r\n            containerElement.removeEventListener(\r\n              \"mousedown\",\r\n              containerElement._dragHandlers.mousedown\r\n            );\r\n            containerElement.removeEventListener(\r\n              \"mousemove\",\r\n              containerElement._dragHandlers.mousemove\r\n            );\r\n            containerElement.removeEventListener(\r\n              \"mouseup\",\r\n              containerElement._dragHandlers.mouseup\r\n            );\r\n            containerElement.removeEventListener(\r\n              \"keydown\",\r\n              containerElement._dragHandlers.keydown\r\n            );\r\n            delete containerElement._dragHandlers;\r\n          }\r\n        }\r\n\r\n        univerAPIRef.current?.dispose?.();\r\n        univerAPIRef.current = null;\r\n        univerCreatedRef.current = false;\r\n        pendingChangesRef.current.clear();\r\n        userInteractedCellsRef.current.clear();\r\n      };\r\n    }, [\r\n      workbookData,\r\n      excelID,\r\n      SetExcelApiData,\r\n      SetCellsFilled,\r\n      highlightAllMaskedCells,\r\n      maskedCells,\r\n      handleDataChange,\r\n      handleFormattedDataChange,\r\n    ]);\r\n\r\n    return (\r\n      <div>\r\n        <div className=\"univer-container\" id={excelID} />\r\n        <ToastContainer transition={Zoom} />\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nExcelSheets.displayName = \"ExcelSheets\";\r\n\r\nexport default ExcelSheets;"], "mappings": ";;AAAA,SACEA,YAAY,EACZC,YAAY,EACZC,UAAU,EACVC,KAAK,QACA,mBAAmB;AAC1B,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,OAAOC,0BAA0B,MAAM,oDAAoD;AAE3F,OAAO,aAAa;AACpB,OAAO,qDAAqD;AAE5D,SAASC,aAAa,EAAEC,UAAU,QAAQ,gBAAgB;AAC1D,SAASL,UAAU,IAAIM,cAAc,QAAQ,gBAAgB;AAC7D,SACEC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,mBAAmB,EACnBC,UAAU,QACL,OAAO;AACd,SAASC,cAAc,EAAEC,IAAI,QAAQ,gBAAgB;AACrD,OAAO,uCAAuC;AAC9C,SAASC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC7B,IAAIC,SAAS;EACb,OAAO,UAAU,GAAGC,IAAI,EAAE;IACxBC,YAAY,CAACF,SAAS,CAAC;IACvBA,SAAS,GAAGG,UAAU,CAAC,MAAML,IAAI,CAACM,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,EAAEF,KAAK,CAAC;EAC7D,CAAC;AACH;;AAEA;AACA,MAAMM,oBAAoB,GAAIC,KAAK,IAAK;EACtC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAI;EAE1C,KAAK,MAAMC,QAAQ,IAAIb,UAAU,EAAE;IACjC,IAAIY,KAAK,CAACE,QAAQ,CAACD,QAAQ,CAACE,IAAI,CAAC,EAAE;MACjC,OAAOF,QAAQ,CAACE,IAAI;IACtB;EACF;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA,MAAMC,wBAAwB,GAAIC,cAAc,IAAK;EACnD,IAAI,CAACA,cAAc,EAAE,OAAO,IAAI;;EAEhC;EACA,IAAIA,cAAc,KAAK,KAAK,EAAE;IAC5B,OAAO,YAAY;EACrB;;EAEA;EACA,OAAQ,IAAGA,cAAe,QAAO;AACnC,CAAC;;AAED;AACA,MAAMC,kBAAkB,GAAIN,KAAK,IAAK;EACpC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAE3C,IAAIO,OAAO,GAAGP,KAAK;EACnB;EACA,KAAK,MAAMC,QAAQ,IAAIb,UAAU,EAAE;IACjCmB,OAAO,GAAGA,OAAO,CAACC,OAAO,CACvB,IAAIC,MAAM,CAACR,QAAQ,CAACE,IAAI,CAACK,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,EACrE,EACF,CAAC;EACH;;EAEA;EACAD,OAAO,GAAGA,OAAO,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACE,IAAI,CAAC,CAAC;EAE1C,OAAOH,OAAO;AAChB,CAAC;AAED,OAAO,MAAMI,cAAc,GAAIC,MAAM,IAAK;EACxC,IAAIC,GAAG,GAAG,CAAC;EACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtCD,GAAG,GAAGA,GAAG,GAAG,EAAE,IAAID,MAAM,CAACI,UAAU,CAACF,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAClD;EACA,OAAOD,GAAG,GAAG,CAAC;AAChB,CAAC;AAED,OAAO,MAAMI,cAAc,GAAIJ,GAAG,IAAK;EACrC,IAAID,MAAM,GAAG,EAAE;EACf,OAAOC,GAAG,IAAI,CAAC,EAAE;IACfD,MAAM,GAAGM,MAAM,CAACC,YAAY,CAAEN,GAAG,GAAG,EAAE,GAAI,EAAE,CAAC,GAAGD,MAAM;IACtDC,GAAG,GAAGO,IAAI,CAACC,KAAK,CAACR,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;EAChC;EACA,OAAOD,MAAM;AACf,CAAC;AAED,OAAO,MAAMU,gBAAgB,GAAIC,OAAO,IAAK;EAC3C,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,eAAe,CAAC;EAC5C,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,MAAM,GAAGC,SAAS,EAAEC,SAAS,CAAC,GAAGF,KAAK;EACtC,MAAMG,QAAQ,GAAGC,QAAQ,CAACF,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC;EAC5C,MAAMG,QAAQ,GAAGlB,cAAc,CAACc,SAAS,CAAC;EAC1C,OAAO;IAAEE,QAAQ;IAAEE;EAAS,CAAC;AAC/B,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAIC,OAAO,IAAK;EAC/C,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EAEd,IAAIH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,SAAS,EAAE;IACtBC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,OAAO,CAACN,OAAO,CAACI,SAAS,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,IAAI,EAAEvC,KAAK,CAAC,KAAK;MAC5D,MAAMwC,OAAO,GAAGlB,gBAAgB,CAACiB,IAAI,CAAC;MACtC,IAAI,CAACC,OAAO,EAAE;MACd,MAAM;QAAEb,QAAQ;QAAEE;MAAS,CAAC,GAAGW,OAAO;MAEtC,IAAI,CAACR,QAAQ,CAACL,QAAQ,CAAC,EAAEK,QAAQ,CAACL,QAAQ,CAAC,GAAG,CAAC,CAAC;MAEhD,IAAI,OAAO3B,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACyC,UAAU,CAAC,GAAG,CAAC,EAAE;QACtDT,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG;UAC7Ba,CAAC,EAAE1C,KAAK;UACR2C,CAAC,EAAE;YACDC,EAAE,EAAE,CAAC,CAAE;UACT;QACF,CAAC;MACH,CAAC,MAAM;QACLZ,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG;UAC7BgB,CAAC,EAAE7C,KAAK;UACR2C,CAAC,EAAE;YACDC,EAAE,EAAE,CAAC,CAAE;UACT;QACF,CAAC;MACH;;MAEAX,MAAM,GAAGb,IAAI,CAAC0B,GAAG,CAACb,MAAM,EAAEN,QAAQ,CAAC;MACnCO,MAAM,GAAGd,IAAI,CAAC0B,GAAG,CAACZ,MAAM,EAAEL,QAAQ,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA,IAAIkB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,OAAO,CAACjB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkB,WAAW,CAAC,EAAE;IACxClB,OAAO,CAACkB,WAAW,CAACX,OAAO,CAAEf,OAAO,IAAK;MACvC,MAAMiB,OAAO,GAAGlB,gBAAgB,CAACC,OAAO,CAAC;MACzC,IAAI,CAACiB,OAAO,EAAE;MACd,MAAM;QAAEb,QAAQ;QAAEE;MAAS,CAAC,GAAGW,OAAO;MAEtC,IAAI,CAACR,QAAQ,CAACL,QAAQ,CAAC,EAAEK,QAAQ,CAACL,QAAQ,CAAC,GAAG,CAAC,CAAC;MAChD,IAAI,CAACK,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,EAAEG,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG,CAAC,CAAC;MAEpEG,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,GAAG;QAC/B,IAAIX,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,IAAI,CAAC,CAAC,CAAC;QACzCO,EAAE,EAAE;UAAEC,GAAG,EAAE;QAAU,CAAC;QACtBP,EAAE,EAAE,CAAC,CAAE;MACT,CAAC;;MAED,IAAIb,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,uBAAuB,EAAE;QACpCpB,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACgB,CAAC,GAAG,EAAE;MACrC;IACF,CAAC,CAAC;EACJ;EAEA,OAAO;IACLQ,EAAE,EAAG,YAAWC,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;IAC5BC,IAAI,EAAE,aAAa;IACnBC,UAAU,EAAE,CAAC,UAAU,CAAC;IACxBC,MAAM,EAAE;MACN,UAAU,EAAE;QACVC,IAAI,EAAEjF,UAAU,CAACkF,IAAI;QACrBP,EAAE,EAAE,UAAU;QACdG,IAAI,EAAE,SAAS;QACfxB,QAAQ;QACR6B,QAAQ,EAAEzC,IAAI,CAAC0B,GAAG,CAACb,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;QAClC6B,WAAW,EAAE1C,IAAI,CAAC0B,GAAG,CAACZ,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;QACrC6B,gBAAgB,EAAE,EAAE;QACpBC,kBAAkB,EAAE;MACtB;IACF;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAGA,CAClCjC,QAAQ,EACRkC,kBAAkB,EAClBC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC,EAC/BnB,WAAW,GAAG,EAAE,EAChBoB,kBAAkB,GAAG,IAAI,KACtB;EAEHC,OAAO,CAACC,GAAG,CAACvC,QAAQ,EAAE,WAAW,CAAC;EAClC,MAAMwC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACN,kBAAkB,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7E,MAAMO,YAAY,GAAG,CAAC,CAAC;;EAEvB;EACAN,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1CxB,KAAK,CAAC8B,IAAI,CAACV,mBAAmB,CAChC,CAAC;EAED,IAAI,CAACnC,QAAQ,EACX,OAAO;IAAE8C,UAAU,EAAEN,gBAAgB;IAAErC,SAAS,EAAEyC;EAAa,CAAC;EAElExC,MAAM,CAACC,OAAO,CAACL,QAAQ,CAAC,CAACM,OAAO,CAAC,CAAC,CAACX,QAAQ,EAAEoD,GAAG,CAAC,KAAK;IACpD3C,MAAM,CAACC,OAAO,CAAC0C,GAAG,CAAC,CAACzC,OAAO,CAAC,CAAC,CAACT,QAAQ,EAAEU,IAAI,CAAC,KAAK;MAChD,IAAI,CAACiC,gBAAgB,CAAC7C,QAAQ,CAAC,EAAE6C,gBAAgB,CAAC7C,QAAQ,CAAC,GAAG,CAAC,CAAC;MAChE6C,gBAAgB,CAAC7C,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG;QAAE,GAAGU;MAAK,CAAC;MAElD,MAAMd,SAAS,GAAGR,cAAc,CAACW,QAAQ,CAACC,QAAQ,CAAC,CAAC;MACpD,MAAMH,SAAS,GAAGE,QAAQ,CAACD,QAAQ,CAAC,GAAG,CAAC;MACxC,MAAMqD,GAAG,GAAI,GAAEvD,SAAU,GAAEC,SAAU,EAAC;MACtC,MAAMuD,YAAY,GAAGhC,WAAW,CAAC/C,QAAQ,CAAC8E,GAAG,CAAC;MAE9C,IAAIC,YAAY,EAAE;QAChBX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,IAAIJ,mBAAmB,CAACe,GAAG,CAACF,GAAG,CAAC,EAAE;UAChC,MAAMG,oBAAoB,GACxB,CAAC5C,IAAI,CAACM,CAAC,KAAK,CAAC,IAAIN,IAAI,CAACM,CAAC,KAAK,GAAG,KAC/B,CAACN,IAAI,CAACG,CAAC,KACN,CAACH,IAAI,CAAC6C,CAAC,IAAI7C,IAAI,CAAC6C,CAAC,KAAK,EAAE,CAAC;UAE5B,IAAID,oBAAoB,EAAE;YACxBb,OAAO,CAACC,GAAG,CACR,cAAaS,GAAI,yCACpB,CAAC;YACD;UACF;UAEA,MAAMK,QAAQ,GACZ,OAAO9C,IAAI,CAACM,CAAC,KAAK,QAAQ,IAAK,CAACyC,KAAK,CAAC/C,IAAI,CAACM,CAAC,CAAC,IAAIN,IAAI,CAACgD,CAAC,KAAK,CAAE;UAChE,MAAMC,UAAU,GAAGH,QAAQ,GAAGI,MAAM,CAAClD,IAAI,CAACM,CAAC,CAAC,GAAG3B,MAAM,CAACqB,IAAI,CAACM,CAAC,CAAC;UAC7D+B,YAAY,CAACI,GAAG,CAAC,GAAGQ,UAAU;UAC9BlB,OAAO,CAACC,GAAG,CACR,2BAA0BS,GAAI,GAAE,EACjCQ,UAAU,EACT,UAAS,EACVjD,IAAI,CAACG,CAAC,IAAI,MACZ,CAAC;QACH;MACF,CAAC,MAAM;QACL,IACG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,CAAC,MAAK6C,SAAS,IAAInD,IAAI,CAACM,CAAC,KAAK,IAAI,IAAIN,IAAI,CAACM,CAAC,KAAK,EAAE,IACzD,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,CAAC,MAAK,CAAC,IAAIN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,CAAC,IAAIyB,mBAAmB,CAACe,GAAG,CAACF,GAAG,CAAE,EAC1D;UACA;UACA,MAAMK,QAAQ,GACZ,OAAO9C,IAAI,CAACM,CAAC,KAAK,QAAQ,IAAK,CAACyC,KAAK,CAAC/C,IAAI,CAACM,CAAC,CAAC,IAAIN,IAAI,CAACgD,CAAC,KAAK,CAAE;UAChEX,YAAY,CAACI,GAAG,CAAC,GAAGK,QAAQ,GAAGI,MAAM,CAAClD,IAAI,CAACM,CAAC,CAAC,GAAG3B,MAAM,CAACqB,IAAI,CAACM,CAAC,CAAC;QAChE;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,MAAM8C,YAAY,GAAG,CAAC,CAAC;EACvBvD,MAAM,CAACC,OAAO,CAACuC,YAAY,CAAC,CAACtC,OAAO,CAAC,CAAC,CAACf,OAAO,EAAEvB,KAAK,CAAC,KAAK;IACzD,MAAMiF,YAAY,GAAGhC,WAAW,CAAC/C,QAAQ,CAACqB,OAAO,CAAC;IAElD,IAAI0D,YAAY,IAAIjF,KAAK,KAAK,CAAC,EAAE;MAC/BsE,OAAO,CAACC,GAAG,CACR,+CAA8ChD,OAAQ,wBACzD,CAAC;MACD;IACF,CAAC,MAAM;MACLoE,YAAY,CAACpE,OAAO,CAAC,GAAGvB,KAAK;IAC/B;EACF,CAAC,CAAC;EAEF,KAAK,MAAM+E,GAAG,IAAIP,gBAAgB,EAAE;IAClC,KAAK,MAAM3D,GAAG,IAAI2D,gBAAgB,CAACO,GAAG,CAAC,EAAE;MACvC,MAAMxC,IAAI,GAAGiC,gBAAgB,CAACO,GAAG,CAAC,CAAClE,GAAG,CAAC;MACvC,MAAMY,SAAS,GAAGR,cAAc,CAACwE,MAAM,CAAC5E,GAAG,CAAC,CAAC;MAC7C,MAAMa,SAAS,GAAG+D,MAAM,CAACV,GAAG,CAAC,GAAG,CAAC;MACjC,MAAMC,GAAG,GAAI,GAAEvD,SAAU,GAAEC,SAAU,EAAC;MACtC,MAAMkE,QAAQ,GAAG3C,WAAW,CAAC/C,QAAQ,CAAC8E,GAAG,CAAC;MAC1C,MAAMa,gBAAgB,GAAG1B,mBAAmB,CAACe,GAAG,CAACF,GAAG,CAAC;MAErD,MAAMc,UAAU,GACd,CAACvD,IAAI,CAACM,CAAC,KAAK,CAAC,IAAIN,IAAI,CAACM,CAAC,KAAK,GAAG,KAC/B,CAACN,IAAI,CAACG,CAAC,KACN,CAACH,IAAI,CAAC6C,CAAC,IAAI7C,IAAI,CAAC6C,CAAC,KAAK,EAAE,CAAC,IAC1B,CAACS,gBAAgB;MAEnB,IAAIC,UAAU,EAAE;QACd,OAAOtB,gBAAgB,CAACO,GAAG,CAAC,CAAClE,GAAG,CAAC;MACnC;IACF;IACA,IAAIuB,MAAM,CAAC2D,IAAI,CAACvB,gBAAgB,CAACO,GAAG,CAAC,CAAC,CAAChE,MAAM,KAAK,CAAC,EAAE;MACnD,OAAOyD,gBAAgB,CAACO,GAAG,CAAC;IAC9B;EACF;EAEA,IAAI,OAAOb,kBAAkB,KAAK,UAAU,EAAE;IAC5CA,kBAAkB,CAACM,gBAAgB,CAAC;EACtC;EAEAF,OAAO,CAACC,GAAG,CAACoB,YAAY,EAAE,gBAAgB,CAAC;EAE3C,OAAO;IAAEb,UAAU,EAAEN,gBAAgB;IAAErC,SAAS,EAAEwD;EAAa,CAAC;AAClE,CAAC;AAED,OAAO,MAAMK,6BAA6B,GAAGA,CAC3ClB,UAAU,EACV7B,WAAW,GAAG,EAAE,EAChBG,uBAAuB,GAAG,KAAK,KAC5B;EACH,MAAMpB,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACG,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC;EAE7D1C,MAAM,CAACC,OAAO,CAACL,QAAQ,CAAC,CAACM,OAAO,CAAC,CAAC,CAACX,QAAQ,EAAEoD,GAAG,CAAC,KAAK;IACpD3C,MAAM,CAACC,OAAO,CAAC0C,GAAG,CAAC,CAACzC,OAAO,CAAC,CAAC,CAACT,QAAQ,EAAEU,IAAI,CAAC,KAAK;MAChD,IAAI,CAACA,IAAI,CAACI,CAAC,EAAEJ,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC;MACxB,OAAOJ,IAAI,CAACI,CAAC,CAACC,EAAE;MAChB,OAAOL,IAAI,CAACI,CAAC,CAACsD,EAAE;MAChB,OAAO1D,IAAI,CAACI,CAAC,CAACuD,EAAE;MAChB3D,IAAI,CAACI,CAAC,CAACC,EAAE,GAAG,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIG,KAAK,CAACC,OAAO,CAACC,WAAW,CAAC,EAAE;IAC9BA,WAAW,CAACX,OAAO,CAAEf,OAAO,IAAK;MAC/B,MAAMiB,OAAO,GAAGlB,gBAAgB,CAACC,OAAO,CAAC;MACzC,IAAI,CAACiB,OAAO,EAAE;MACd,MAAM;QAAEb,QAAQ;QAAEE;MAAS,CAAC,GAAGW,OAAO;MACtC,IAAI,CAACR,QAAQ,CAACL,QAAQ,CAAC,EAAEK,QAAQ,CAACL,QAAQ,CAAC,GAAG,CAAC,CAAC;MAChD,IAAI,CAACK,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,EAAEG,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG,CAAC,CAAC;MAEpE,MAAMsE,KAAK,GAAG;QACZjD,EAAE,EAAE;UAAEC,GAAG,EAAE;QAAU,CAAC;QACtBP,EAAE,EAAE,CAAC,CAAE;MACT,CAAC;;MAEDZ,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,GAAGwD,KAAK;MACtC,IAAI,CAAC/C,uBAAuB,EAAE;QAC5BpB,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACgB,CAAC,GAAG,EAAE;QACnC,IAAIb,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACa,CAAC,EAChC,OAAOV,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACa,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEA,OAAO;IACLW,EAAE,EAAG,YAAWC,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;IAC5BC,IAAI,EAAE,aAAa;IACnBC,UAAU,EAAE,CAAC,UAAU,CAAC;IACxBC,MAAM,EAAE;MACN,UAAU,EAAE;QACVC,IAAI,EAAEjF,UAAU,CAACkF,IAAI;QACrBP,EAAE,EAAE,UAAU;QACdG,IAAI,EAAE,SAAS;QACfxB,QAAQ;QACR6B,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,gBAAgB,EAAE,EAAE;QACpBC,kBAAkB,EAAE;MACtB;IACF;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAMoC,kCAAkC,GAAGA,CAChDtB,UAAU,EACVuB,iBAAiB,EACjBpD,WAAW,GAAG,EAAE,EAChBqD,sBAAsB,GAAG,IAAI,KAC1B;EACH,MAAMtE,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACG,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC;EAE7D1C,MAAM,CAACC,OAAO,CAACL,QAAQ,CAAC,CAACM,OAAO,CAAC,CAAC,CAACX,QAAQ,EAAEoD,GAAG,CAAC,KAAK;IACpD3C,MAAM,CAACC,OAAO,CAAC0C,GAAG,CAAC,CAACzC,OAAO,CAAC,CAAC,CAACT,QAAQ,EAAEU,IAAI,CAAC,KAAK;MAChD,IAAI,CAACA,IAAI,CAACI,CAAC,EAAEJ,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC;MACxB,OAAOJ,IAAI,CAACI,CAAC,CAACC,EAAE;MAChB,OAAOL,IAAI,CAACI,CAAC,CAACsD,EAAE;MAChB,OAAO1D,IAAI,CAACI,CAAC,CAACuD,EAAE;MAChB3D,IAAI,CAACI,CAAC,CAACC,EAAE,GAAG,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAM2D,cAAc,GAClBF,iBAAiB,IACjB,OAAOA,iBAAiB,KAAK,QAAQ,IACrCjE,MAAM,CAAC2D,IAAI,CAACM,iBAAiB,CAAC,CAACG,IAAI,CAAEC,GAAG,IAAK,CAACnB,KAAK,CAACmB,GAAG,CAAC,CAAC;EAE3D,IAAI1D,KAAK,CAACC,OAAO,CAACC,WAAW,CAAC,EAAE;IAC9BA,WAAW,CAACX,OAAO,CAAEf,OAAO,IAAK;MAC/B,MAAMiB,OAAO,GAAGlB,gBAAgB,CAACC,OAAO,CAAC;MACzC,IAAI,CAACiB,OAAO,EAAE;MACd,MAAM;QAAEb,QAAQ;QAAEE;MAAS,CAAC,GAAGW,OAAO;MACtC,IAAI,CAACR,QAAQ,CAACL,QAAQ,CAAC,EAAEK,QAAQ,CAACL,QAAQ,CAAC,GAAG,CAAC,CAAC;MAChD,IAAI,CAACK,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,EAAEG,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG,CAAC,CAAC;MAEpE,MAAMsE,KAAK,GAAG;QACZjD,EAAE,EAAE;UAAEC,GAAG,EAAE;QAAU,CAAC;QACtBP,EAAE,EAAE;MACN,CAAC;MAEDZ,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,GAAGwD,KAAK;MAEtC,IAAIO,cAAc,GAAG,IAAI;MAEzB,IAAIL,iBAAiB,EAAE;QACrB,IAAIE,cAAc,EAAE;UAAA,IAAAI,qBAAA;UAClB,MAAMC,aAAa,IAAAD,qBAAA,GAAGN,iBAAiB,CAAC1E,QAAQ,CAAC,cAAAgF,qBAAA,uBAA3BA,qBAAA,CAA8B9E,QAAQ,CAAC;UAC7D,IAAI+E,aAAa,EAAE;YAAA,IAAAC,qBAAA;YACjB;YACA,IAAIP,sBAAsB,aAAtBA,sBAAsB,eAAtBA,sBAAsB,CAAEQ,OAAO,EAAE;cACnCR,sBAAsB,CAACQ,OAAO,CAACC,GAAG,CAACxF,OAAO,CAAC;YAC7C;YAEAS,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,GAAG;cAC7B,GAAGG,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC;cAC/B,GAAG+E,aAAa;cAChBjE,CAAC,EAAE;gBACD,GAAGX,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC;gBACjC,GAAGiE,aAAa,CAACjE,CAAC;gBAClBO,EAAE,EAAE;kBAAEC,GAAG,EAAE;gBAAU,CAAC;gBACtBP,EAAE,EAAE;cACN;YACF,CAAC;YAED,KAAAiE,qBAAA,GAAI7E,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,cAAAkE,qBAAA,eAA9BA,qBAAA,CAAgCG,CAAC,EAAE;cACrC,IAAIC,OAAO,GAAGjF,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,CAACqE,CAAC;cAC9C,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;gBAC/BA,OAAO,GAAGA,OAAO,CAACzG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;gBACtCwB,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,CAACqE,CAAC,GAAG;kBAAEC,OAAO,EAAEA;gBAAQ,CAAC;cACzD,CAAC,MAAM,IAAIA,OAAO,CAACA,OAAO,EAAE;gBAC1B,IAAIC,YAAY,GAAGD,OAAO,CAACA,OAAO,CAACzG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;gBACvDwB,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,CAACqE,CAAC,GAAG;kBAAEC,OAAO,EAAEC;gBAAa,CAAC;cAC9D;cAEA,IACEN,aAAa,CAACxB,CAAC,IACfwB,aAAa,CAACxB,CAAC,KAAKpD,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACgB,CAAC,EAClD;gBACAb,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACuD,CAAC,GAAGwB,aAAa,CAACxB,CAAC;cAClD;YACF;YAEA;UACF;QACF,CAAC,MAAM;UACLsB,cAAc,GAAGL,iBAAiB,CAAC9E,OAAO,CAAC;UAC3C;UACA,IACEmF,cAAc,KAAKhB,SAAS,IAC5BgB,cAAc,KAAK,IAAI,IACvBA,cAAc,KAAK,EAAE,IACrBJ,sBAAsB,aAAtBA,sBAAsB,eAAtBA,sBAAsB,CAAEQ,OAAO,EAC/B;YACAR,sBAAsB,CAACQ,OAAO,CAACC,GAAG,CAACxF,OAAO,CAAC;UAC7C;QACF;MACF;MAEA,IACEmF,cAAc,KAAKhB,SAAS,IAC5BgB,cAAc,KAAK,IAAI,IACvBA,cAAc,KAAK,EAAE,EACrB;QACA,IAAIS,QAAQ,GAAG,CAAC;QAChB,IAAI3B,UAAU,GAAGkB,cAAc;QAE/B,IACE,OAAOA,cAAc,KAAK,QAAQ,IAClCA,cAAc,CAACjE,UAAU,CAAC,GAAG,CAAC,EAC9B;UACAT,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACa,CAAC,GAAGgE,cAAc;QACjD,CAAC,MAAM;UACL,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;YACtClB,UAAU,GAAGkB,cAAc;YAC3BS,QAAQ,GAAG,CAAC;UACd,CAAC,MAAM,IAAI,OAAOT,cAAc,KAAK,QAAQ,EAAE;YAC7C,MAAMU,WAAW,GAAGV,cAAc,CAAChG,IAAI,CAAC,CAAC;YACzC,IACE,CAAC4E,KAAK,CAAC8B,WAAW,CAAC,IACnB,CAAC9B,KAAK,CAAC+B,UAAU,CAACD,WAAW,CAAC,CAAC,IAC/BA,WAAW,KAAK,EAAE,EAClB;cACA5B,UAAU,GAAGC,MAAM,CAAC2B,WAAW,CAAC;cAChCD,QAAQ,GAAG,CAAC;YACd,CAAC,MAAM;cACL3B,UAAU,GAAGkB,cAAc;cAC3BS,QAAQ,GAAG,CAAC;YACd;UACF;UAEAnF,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACgB,CAAC,GAAG2C,UAAU;UAC3CxD,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAAC0D,CAAC,GAAG4B,QAAQ;UAEzC,IAAIA,QAAQ,KAAK,CAAC,EAAE;YAAA,IAAAG,oBAAA,EAAAC,eAAA;YAClB,IAAI,CAACvF,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,EAAE;cACnCX,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,GAAG,CAAC,CAAC;YACrC;;YAEA;YACA,MAAM6E,YAAY,GAAG1C,UAAU,aAAVA,UAAU,wBAAAwC,oBAAA,GAAVxC,UAAU,CAAGnD,QAAQ,CAAC,cAAA2F,oBAAA,uBAAtBA,oBAAA,CAAyBzF,QAAQ,CAAC;YACvD,IAAI2F,YAAY,aAAZA,YAAY,gBAAAD,eAAA,GAAZC,YAAY,CAAE7E,CAAC,cAAA4E,eAAA,eAAfA,eAAA,CAAiBP,CAAC,EAAE;cACtB;cACA,IAAIC,OAAO,GAAGO,YAAY,CAAC7E,CAAC,CAACqE,CAAC;cAC9B,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;gBAC/BA,OAAO,GAAGA,OAAO,CAACzG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;gBACtCwB,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,CAACqE,CAAC,GAAG;kBAAEC,OAAO,EAAEA;gBAAQ,CAAC;cACzD,CAAC,MAAM,IAAIA,OAAO,CAACA,OAAO,EAAE;gBAC1B,IAAIC,YAAY,GAAGD,OAAO,CAACA,OAAO,CAACzG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;gBACvDwB,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,CAACqE,CAAC,GAAG;kBAAEC,OAAO,EAAEC;gBAAa,CAAC;cAC9D;YACF,CAAC,MAAM;cACL;cACA,MAAMO,SAAS,GAAGzF,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACgB,CAAC;cAChD,MAAM6E,cAAc,GAAG3H,oBAAoB,CACzCmB,MAAM,CAACuG,SAAS,IAAI,EAAE,CACxB,CAAC;cACD,MAAME,aAAa,GAAGvH,wBAAwB,CAC5CsH,cAAc,IAAI,GACpB,CAAC;cACD1F,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACc,CAAC,CAACqE,CAAC,GAAG;gBAAEC,OAAO,EAAEU;cAAc,CAAC;YAC/D;UACF;QACF;MACF,CAAC,MAAM;QACL3F,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACgB,CAAC,GAAG,EAAE;QACnCb,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAAC0D,CAAC,GAAG,CAAC;QAClC,IAAIvD,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACa,CAAC,EAAE;UAClC,OAAOV,QAAQ,CAACL,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAACa,CAAC;QACvC;MACF;IACF,CAAC,CAAC;EACJ;EAEA4B,OAAO,CAACC,GAAG,CAACvC,QAAQ,EAAE,aAAa,CAAC;EACpC,OAAO;IACLqB,EAAE,EAAG,YAAWC,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;IAC5BC,IAAI,EAAE,aAAa;IACnBC,UAAU,EAAE,CAAC,UAAU,CAAC;IACxBC,MAAM,EAAE;MACN,UAAU,EAAE;QACVC,IAAI,EAAEjF,UAAU,CAACkF,IAAI;QACrBP,EAAE,EAAE,UAAU;QACdG,IAAI,EAAE,SAAS;QACfxB,QAAQ;QACR6B,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,gBAAgB,EAAE,EAAE;QACpBC,kBAAkB,EAAE;MACtB;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAM4D,WAAW,gBAAAC,EAAA,eAAG5I,UAAU,CAAA6I,EAAA,GAAAD,EAAA,CAC5B,CACE;EACEE,SAAS;EACT9E,WAAW;EACX+E,eAAe;EACfC,cAAc;EACdC,WAAW;EACXnG,OAAO;EACPoG,OAAO;EACP9B,iBAAiB;EACjBjD,uBAAuB,GAAG,KAAK;EAC/Bc,kBAAkB,GAAG;AACvB,CAAC,EACDc,GAAG,KACA;EAAA6C,EAAA;EACH,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGtJ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMuJ,gBAAgB,GAAGxJ,MAAM,CAAC,KAAK,CAAC;EACtC,MAAMyJ,YAAY,GAAGzJ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM0J,eAAe,GAAG1J,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM2J,iBAAiB,GAAG3J,MAAM,CAAC,IAAI4J,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,MAAMC,kBAAkB,GAAG7J,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMwH,sBAAsB,GAAGxH,MAAM,CAAC,IAAIsF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElD;EACApF,mBAAmB,CAACgG,GAAG,EAAE,OAAO;IAC9B4D,QAAQ,EAAEA,CAAA,KAAM;MACd;MACA,IAAID,kBAAkB,CAAC7B,OAAO,EAAE;QAAA,IAAA+B,qBAAA,EAAAC,sBAAA;QAC9B,CAAAD,qBAAA,IAAAC,sBAAA,GAAAH,kBAAkB,CAAC7B,OAAO,EAACiC,MAAM,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAAG,IAAA,CAAAF,sBAAoC,CAAC;MACvC;MACA;MACAG,yBAAyB,CAAC,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMC,uBAAuB,GAAGtK,WAAW,CAAC,MAAM;IAChD,IAAI,CAACyH,iBAAiB,IAAI,CAACpD,WAAW,IAAIA,WAAW,CAAClC,MAAM,KAAK,CAAC,EAAE;MAClE,OAAO,KAAK;IACd;IAEA,OAAOkC,WAAW,CAACkG,KAAK,CAAE5H,OAAO,IAAK;MACpC,MAAMvB,KAAK,GAAGqG,iBAAiB,CAAC9E,OAAO,CAAC;MACxC,OAAOvB,KAAK,KAAK0F,SAAS,IAAI1F,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE;IAC9D,CAAC,CAAC;EACJ,CAAC,EAAE,CAACqG,iBAAiB,EAAEpD,WAAW,CAAC,CAAC;EAEpCpE,SAAS,CAAC,MAAM;IACd,IAAIuK,MAAM;IAEV,IAAIrH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE+C,UAAU,IAAI,CAACuB,iBAAiB,EAAE;MAC7C+C,MAAM,GAAGpD,6BAA6B,CACpCjE,OAAO,CAAC+C,UAAU,EAClB7B,WAAW,EACXG,uBACF,CAAC;IACH,CAAC,MAAM,IAAIiD,iBAAiB,EAAE;MAC5B,IAAItE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE+C,UAAU,EAAE;QAAA,IAAAuE,qBAAA;QACvB,MAAMC,YAAY,GAAGvH,OAAO,aAAPA,OAAO,wBAAAsH,qBAAA,GAAPtH,OAAO,CAAEsE,iBAAiB,cAAAgD,qBAAA,uBAA1BA,qBAAA,CAA4BE,kBAAkB;QAEnE,IAAID,YAAY,EAAE;UAChBF,MAAM,GAAGhD,kCAAkC,CACzCrE,OAAO,CAAC+C,UAAU,EAClBwE,YAAY,EACZrG,WAAW,EACXqD,sBACF,CAAC;QACH,CAAC,MAAM;UACL8C,MAAM,GAAGhD,kCAAkC,CACzCrE,OAAO,CAAC+C,UAAU,EAClBuB,iBAAiB,EACjBpD,WAAW,EACXqD,sBACF,CAAC;QACH;MACF,CAAC,MAAM;QACL8C,MAAM,GAAGtH,oBAAoB,CAAC;UAC5BK,SAAS,EAAEkE,iBAAiB;UAC5BpD,WAAW;UACXG;QACF,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLgG,MAAM,GAAGtH,oBAAoB,CAAC;QAC5BK,SAAS,EAAEJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,SAAS;QAC7Bc,WAAW;QACXG;MACF,CAAC,CAAC;IACJ;IACA;IACA,IAAIgG,MAAM,EAAE;MAAA,IAAAI,OAAA,EAAAC,cAAA,EAAAC,oBAAA;MACV,MAAMC,eAAe,GAAG;QACtB,GAAGP,MAAM;QACTQ,MAAM,EAAEjL,cAAc,CAACkL,KAAK;QAC5BC,UAAU,EAAE;MACd,CAAC;MAEDzB,eAAe,CAACsB,eAAe,CAAC;MAChCnB,eAAe,CAAC1B,OAAO,GAAG6C,eAAe;MACzCrB,gBAAgB,CAACxB,OAAO,GAAG,KAAK;MAChC2B,iBAAiB,CAAC3B,OAAO,CAACiD,KAAK,CAAC,CAAC;;MAEjC;MACA,IAAI1D,iBAAiB,KAAAmD,OAAA,GAAIJ,MAAM,cAAAI,OAAA,gBAAAC,cAAA,GAAND,OAAA,CAAQ9F,MAAM,cAAA+F,cAAA,gBAAAC,oBAAA,GAAdD,cAAA,CAAiB,UAAU,CAAC,cAAAC,oBAAA,eAA5BA,oBAAA,CAA8B1H,QAAQ,EAAE;QAAA,IAAAgI,sBAAA;QAC/DhC,eAAe,CACb/D,oBAAoB,CAClBmF,MAAM,CAAC1F,MAAM,CAAC,UAAU,CAAC,CAAC1B,QAAQ,EAClCkC,kBAAkB,EAClBoC,sBAAsB,CAACQ,OAAO,EAC9B7D,WAAW,EACX,CAAAlB,OAAO,aAAPA,OAAO,wBAAAiI,sBAAA,GAAPjI,OAAO,CAAEsE,iBAAiB,cAAA2D,sBAAA,uBAA1BA,sBAAA,CAA4BT,kBAAkB,KAAI,IACpD,CACF,CAAC;MACH;;MAEA;MACA,IAAI,CAAClD,iBAAiB,EAAE;QACtBC,sBAAsB,CAACQ,OAAO,CAACiD,KAAK,CAAC,CAAC;MACxC;IACF;EACF,CAAC,EAAE,CACDhI,OAAO,EACPkB,WAAW,EACXG,uBAAuB,EACvBiD,iBAAiB,EACjB6C,uBAAuB,CACxB,CAAC;;EAEF;EACA,MAAMD,yBAAyB,GAAGrK,WAAW,CAAC,MAAM;IAAA,IAAAqL,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAClD,MAAMC,SAAS,GAAG7B,YAAY,CAACzB,OAAO;IACtC,MAAMuD,IAAI,GAAGD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,iBAAiB,CAAC,CAAC;IAC3C,MAAMC,KAAK,GAAGF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,cAAc,CAAC,CAAC;IACpC,IAAI,CAACD,KAAK,EAAE;IAEZ,MAAME,WAAW,GAAGjC,eAAe,CAAC1B,OAAO;IAC3C,MAAM4D,gBAAgB,GACpB,CAAAD,WAAW,aAAXA,WAAW,wBAAAR,mBAAA,GAAXQ,WAAW,CAAE/G,MAAM,cAAAuG,mBAAA,wBAAAC,qBAAA,GAAnBD,mBAAA,CAAsB,UAAU,CAAC,cAAAC,qBAAA,uBAAjCA,qBAAA,CAAmClI,QAAQ,KAAI,CAAC,CAAC;IACnD,MAAM2I,eAAe,GAAG,CAAC,CAAC;IAE1B,MAAMC,SAAS,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAChD,MAAM7I,MAAM,GAAG2I,SAAS,CAAC/G,QAAQ,GAAG,CAAC;IACrC,MAAM3B,MAAM,GAAG0I,SAAS,CAAC9G,WAAW,GAAG,CAAC;;IAExC;IACA,KAAK,IAAIiH,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;MAC/C,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;QAAA,IAAAC,qBAAA;QAC/C,MAAMC,OAAO,GAAI,GAAEH,MAAO,IAAGC,MAAO,EAAC;QACrC,MAAMG,KAAK,GAAGZ,KAAK,CAACa,QAAQ,CAACL,MAAM,EAAEC,MAAM,CAAC;QAC5C,MAAMK,OAAO,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC,IAAI,EAAE;QACxC,IAAIC,eAAe,GAAGJ,KAAK,CAACK,QAAQ,CAAC,CAAC;;QAEtC;QACA,IAAIC,YAAY,GAAGF,eAAe;QAClC,IAAIF,OAAO,IAAI,OAAOF,KAAK,CAACO,eAAe,KAAK,UAAU,EAAE;UAC1DD,YAAY,GAAGN,KAAK,CAACO,eAAe,CAAC,CAAC;UACtC;UACA,IAAID,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK/F,SAAS,IAAI+F,YAAY,KAAKF,eAAe,EAAE;YAC3FA,eAAe,GAAGE,YAAY;UAChC;QACF;;QAEA;QACA,IAAIF,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK7F,SAAS,EAAE;UAC7D6F,eAAe,GAAG,EAAE;QACtB;;QAEA;QACA,IAAI9C,iBAAiB,CAAC3B,OAAO,CAAC5B,GAAG,CAACgG,OAAO,CAAC,EAAE;UAC1CK,eAAe,GAAG9C,iBAAiB,CAAC3B,OAAO,CAAC6E,GAAG,CAACT,OAAO,CAAC;QAC1D;QAEA,MAAM3J,OAAO,GAAI,GAAEN,cAAc,CAAC+J,MAAM,CAAE,GAAED,MAAM,GAAG,CAAE,EAAC;QACxD,MAAMa,YAAY,GAAGlB,gBAAgB,aAAhBA,gBAAgB,wBAAAO,qBAAA,GAAhBP,gBAAgB,CAAGK,MAAM,CAAC,cAAAE,qBAAA,uBAA1BA,qBAAA,CAA6BD,MAAM,CAAC;QACzD,MAAM/F,YAAY,GAAGhC,WAAW,CAAC/C,QAAQ,CAACqB,OAAO,CAAC;QAClD,MAAMsK,iBAAiB,GAAGvF,sBAAsB,CAACQ,OAAO,CAAC5B,GAAG,CAAC3D,OAAO,CAAC;QAErE,MAAMuK,WAAW,GACfP,eAAe,KAAK,EAAE,IACtBA,eAAe,KAAK,IAAI,IACvB,OAAOA,eAAe,KAAK,QAAQ,IAClCA,eAAe,KAAK,CAAC,IACrB,CAACF,OAAQ;QAEb,MAAMU,OAAO,GAAG9G,YAAY,GACxB4G,iBAAiB,IAAI,CAACC,WAAW,GAChCD,iBAAiB,IAAI,CAACC,WAAW,IAClCT,OAAO,IACP5C,iBAAiB,CAAC3B,OAAO,CAAC5B,GAAG,CAACgG,OAAO,CAAC;QAE1C,IAAIa,OAAO,EAAE;UACX,IAAI,CAACpB,eAAe,CAACI,MAAM,CAAC,EAAEJ,eAAe,CAACI,MAAM,CAAC,GAAG,CAAC,CAAC;;UAE1D;UACA,IAAI5D,QAAQ,GAAG,CAAC,CAAC,CAAC;UAClB,IAAI3B,UAAU,GAAG+F,eAAe;;UAEhC;UACA,IAAIF,OAAO,IAAIE,eAAe,KAAK,EAAE,IAAI,OAAOA,eAAe,KAAK,WAAW,EAAE;YAC/E;YACA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;cACvC,MAAMhL,OAAO,GAAGgL,eAAe,CAAC/K,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;cACrD,IAAI,CAAC8E,KAAK,CAAC/E,OAAO,CAAC,IAAI,CAAC+E,KAAK,CAAC+B,UAAU,CAAC9G,OAAO,CAAC,CAAC,EAAE;gBAClDiF,UAAU,GAAGC,MAAM,CAAClF,OAAO,CAAC;gBAC5B4G,QAAQ,GAAG,CAAC,CAAC,CAAC;cAChB,CAAC,MAAM;gBACL3B,UAAU,GAAG+F,eAAe;gBAC5BpE,QAAQ,GAAG,CAAC,CAAC,CAAC;cAChB;YACF,CAAC,MAAM,IAAI,OAAOoE,eAAe,KAAK,QAAQ,EAAE;cAC9C/F,UAAU,GAAG+F,eAAe;cAC5BpE,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB;UACF,CAAC,MAAM,IACL,CAACkE,OAAO,IACRE,eAAe,KAAK,EAAE,IACtB,OAAOA,eAAe,KAAK,WAAW,EACtC;YACA;YACA,MAAMnE,WAAW,GAAGlG,MAAM,CAACqK,eAAe,CAAC,CAAC7K,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC4E,KAAK,CAAC8B,WAAW,CAAC,IAAI,CAAC9B,KAAK,CAAC+B,UAAU,CAACD,WAAW,CAAC,CAAC,EAAE;cAC1D5B,UAAU,GAAGC,MAAM,CAAC2B,WAAW,CAAC;cAChCD,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB;UACF;;UAEA,MAAM6E,WAAW,GAAG;YAClBnJ,CAAC,EAAE2C,UAAU;YACbD,CAAC,EAAE4B,QAAQ;YACXxE,CAAC,EAAE;cACD,IAAI,CAAAiJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEjJ,CAAC,KAAI,CAAC,CAAC,CAAC;cAC1BC,EAAE,EAAE,CAAC,CAAE;YACT;UACF,CAAC;;UACD,IAAIyI,OAAO,EAAE;YACXW,WAAW,CAACtJ,CAAC,GAAG2I,OAAO;UACzB;UACA,IAAIpG,YAAY,EAAE;YAChB+G,WAAW,CAACrJ,CAAC,GAAG;cACd,GAAGqJ,WAAW,CAACrJ,CAAC;cAChBO,EAAE,EAAE;gBAAEC,GAAG,EAAE;cAAU,CAAC;cACtBP,EAAE,EAAE,CAAC,CAAE;YACT,CAAC;;YACD,IAAIQ,uBAAuB,EAAE;cAC3B4I,WAAW,CAACnJ,CAAC,GAAG,EAAE;YACpB;UACF;UACA8H,eAAe,CAACI,MAAM,CAAC,CAACC,MAAM,CAAC,GAAGgB,WAAW;QAC/C;MACF;IACF;;IAEA;IACAvD,iBAAiB,CAAC3B,OAAO,CAACiD,KAAK,CAAC,CAAC;IAEjCvB,eAAe,CAAC1B,OAAO,GAAG;MACxB,GAAG2D,WAAW;MACd/G,MAAM,EAAE;QACN,GAAG+G,WAAW,CAAC/G,MAAM;QACrB,CAAC,UAAU,GAAG;UACZ,GAAG+G,WAAW,CAAC/G,MAAM,CAAC,UAAU,CAAC;UACjC1B,QAAQ,EAAE2I;QACZ;MACF;IACF,CAAC;IAED3C,eAAe,CACb/D,oBAAoB,CAClB0G,eAAe,EACfzG,kBAAkB,EAClBoC,sBAAsB,CAACQ,OAAO,EAC9B7D,WAAW,EACX,CAAAlB,OAAO,aAAPA,OAAO,wBAAAoI,sBAAA,GAAPpI,OAAO,CAAEsE,iBAAiB,cAAA8D,sBAAA,uBAA1BA,sBAAA,CAA4BZ,kBAAkB,KAAI,IACpD,CACF,CAAC;IAED,IAAInG,uBAAuB,EAAE;MAC3B6E,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACL,IAAIgE,SAAS,GAAG,KAAK;MACrB,KAAK,MAAM1K,OAAO,IAAI0B,WAAW,EAAE;QAAA,IAAAiJ,qBAAA;QACjC,MAAM;UAAEvK,QAAQ;UAAEE;QAAS,CAAC,GAAGP,gBAAgB,CAACC,OAAO,CAAC;QACxD,MAAMgB,IAAI,GAAGoI,eAAe,aAAfA,eAAe,wBAAAuB,qBAAA,GAAfvB,eAAe,CAAGhJ,QAAQ,CAAC,cAAAuK,qBAAA,uBAA3BA,qBAAA,CAA8BrK,QAAQ,CAAC;QACpD,IAAI,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,CAAC,MAAK6C,SAAS,IAAInD,IAAI,CAACM,CAAC,KAAK,IAAI,IAAIN,IAAI,CAACM,CAAC,KAAK,EAAE,EAAE;UAC7DoJ,SAAS,GAAG,IAAI;UAChB;QACF;MACF;MACAhE,cAAc,CAACgE,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACjE,eAAe,EAAEC,cAAc,EAAE7E,uBAAuB,EAAEH,WAAW,CAAC,CAAC;;EAE3E;EACA,MAAMkJ,yBAAyB,GAAGvN,WAAW,CAAC,MAAM;IAClD,IAAI;MAAA,IAAAwN,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF,MAAMlC,SAAS,GAAG7B,YAAY,CAACzB,OAAO;MACtC,MAAMuD,IAAI,GAAGD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,iBAAiB,CAAC,CAAC;MAC3C,MAAMC,KAAK,GAAGF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,cAAc,CAAC,CAAC;MACpC,IAAI,CAACD,KAAK,IAAI,OAAOA,KAAK,CAACa,QAAQ,KAAK,UAAU,EAAE;MAEpD,MAAMX,WAAW,GAAGjC,eAAe,CAAC1B,OAAO;MAC3C,MAAM4D,gBAAgB,GACpB,CAAAD,WAAW,aAAXA,WAAW,wBAAA2B,oBAAA,GAAX3B,WAAW,CAAE/G,MAAM,cAAA0I,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,UAAU,CAAC,cAAAC,qBAAA,uBAAjCA,qBAAA,CAAmCrK,QAAQ,KAAI,CAAC,CAAC;MACnD,MAAM2I,eAAe,GAAG,CAAC,CAAC;MAE1B,MAAMC,SAAS,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAChD,MAAM7I,MAAM,GAAG2I,SAAS,CAAC/G,QAAQ,GAAG,CAAC;MACrC,MAAM3B,MAAM,GAAG0I,SAAS,CAAC9G,WAAW,GAAG,CAAC;MAExC,KAAK,IAAIiH,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;QAC/C,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;UAAA,IAAAuB,qBAAA,EAAAC,sBAAA,EAAAC,eAAA;UAC/C,MAAMvB,OAAO,GAAI,GAAEH,MAAO,IAAGC,MAAO,EAAC;UACrC,MAAMG,KAAK,GAAGZ,KAAK,CAACa,QAAQ,CAACL,MAAM,EAAEC,MAAM,CAAC;UAC5C,IAAI,CAACG,KAAK,IAAI,OAAOA,KAAK,CAACG,UAAU,KAAK,UAAU,EAAE;UAEtD,MAAMD,OAAO,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC;UAClC,MAAMoB,QAAQ,GAAGvB,KAAK,CAACK,QAAQ,CAAC,CAAC;UACjC,MAAMC,YAAY,IAAAc,qBAAA,IAAAC,sBAAA,GAAGrB,KAAK,CAACO,eAAe,cAAAc,sBAAA,uBAArBA,sBAAA,CAAAxD,IAAA,CAAAmC,KAAwB,CAAC,cAAAoB,qBAAA,cAAAA,qBAAA,GAAIG,QAAQ;UAC1D,MAAMC,cAAc,GAAG,EAAAF,eAAA,GAAAtB,KAAK,CAACyB,QAAQ,cAAAH,eAAA,uBAAdA,eAAA,CAAAzD,IAAA,CAAAmC,KAAiB,CAAC,KAAI,CAAC,CAAC;UAE/C,IACEE,OAAO,IACPqB,QAAQ,KAAK,IAAI,IACjBzJ,WAAW,CAAC/C,QAAQ,CAAE,GAAEe,cAAc,CAAC+J,MAAM,CAAE,GAAED,MAAM,GAAG,CAAE,EAAC,CAAC,EAC9D;YAAA,IAAA8B,sBAAA,EAAAC,sBAAA;YACA,IAAI,CAACnC,eAAe,CAACI,MAAM,CAAC,EAAEJ,eAAe,CAACI,MAAM,CAAC,GAAG,CAAC,CAAC;YAE1D,MAAMxJ,OAAO,GAAI,GAAEN,cAAc,CAAC+J,MAAM,CAAE,GAAED,MAAM,GAAG,CAAE,EAAC;YACxD,MAAMnF,QAAQ,GAAG3C,WAAW,CAAC/C,QAAQ,CAACqB,OAAO,CAAC;YAE9C,IAAIwL,MAAM,GAAGJ,cAAc,CAAC3F,CAAC;YAC7B,IAAI,CAAC+F,MAAM,IAAI,OAAOtB,YAAY,KAAK,QAAQ,EAAE;cAC/C,MAAM/D,cAAc,GAAG3H,oBAAoB,CAAC0L,YAAY,CAAC;cACzD,IAAI/D,cAAc,EAAE;gBAClBqF,MAAM,GAAG3M,wBAAwB,CAACsH,cAAc,CAAC;cACnD,CAAC,MAAM,IAAI+D,YAAY,CAACvL,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACrC6M,MAAM,GAAG,OAAO;cAClB;YACF;YAEA,IAAIvH,UAAU,GAAGkH,QAAQ;YAEzB,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAChC,MAAMnM,OAAO,GAAGD,kBAAkB,CAACoM,QAAQ,CAAC,CAAClM,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;cAC9DgF,UAAU,GAAGF,KAAK,CAAC/E,OAAO,CAAC,GAAGmM,QAAQ,GAAGjH,MAAM,CAAClF,OAAO,CAAC;YAC1D;YAEA,MAAMyM,WAAW,GAAG;cAClBnK,CAAC,EAAE2C,UAAU;cACbJ,CAAC,EAAEqG,YAAY;cACflG,CAAC,EAAE,OAAOC,UAAU,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;cACzC7C,CAAC,EAAE;gBACD,IAAI,CAAA+H,gBAAgB,aAAhBA,gBAAgB,wBAAAmC,sBAAA,GAAhBnC,gBAAgB,CAAGK,MAAM,CAAC,cAAA8B,sBAAA,wBAAAC,sBAAA,GAA1BD,sBAAA,CAA6B7B,MAAM,CAAC,cAAA8B,sBAAA,uBAApCA,sBAAA,CAAsCnK,CAAC,KAAI,CAAC,CAAC,CAAC;gBAClD,IAAIoK,MAAM,GAAG;kBAAE/F,CAAC,EAAE+F;gBAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChCnK,EAAE,EAAE,CAAC;gBACL,IAAIgD,QAAQ,GAAG;kBAAE1C,EAAE,EAAE;oBAAEC,GAAG,EAAE;kBAAU;gBAAE,CAAC,GAAG,CAAC,CAAC;cAChD;YACF,CAAC;YAED,IAAIkI,OAAO,EAAE2B,WAAW,CAACtK,CAAC,GAAG2I,OAAO;YAEpCV,eAAe,CAACI,MAAM,CAAC,CAACC,MAAM,CAAC,GAAGgC,WAAW;UAC/C;QACF;MACF;MAEAxE,eAAe,CAAC1B,OAAO,GAAG;QACxB,GAAG2D,WAAW;QACd/G,MAAM,EAAE;UACN,GAAG+G,WAAW,CAAC/G,MAAM;UACrB,CAAC,UAAU,GAAG;YACZ,GAAG+G,WAAW,CAAC/G,MAAM,CAAC,UAAU,CAAC;YACjC1B,QAAQ,EAAE2I;UACZ;QACF;MACF,CAAC;MAED3C,eAAe,CACb/D,oBAAoB,CAClB0G,eAAe,EACfzG,kBAAkB,EAClBoC,sBAAsB,CAACQ,OAAO,EAC9B7D,WAAW,EACX,CAAAlB,OAAO,aAAPA,OAAO,wBAAAuK,sBAAA,GAAPvK,OAAO,CAAEsE,iBAAiB,cAAAiG,sBAAA,uBAA1BA,sBAAA,CAA4B/C,kBAAkB,KAAI,IACpD,CACF,CAAC;MAED,IAAInG,uBAAuB,EAAE;QAC3B6E,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACL,IAAIgE,SAAS,GAAG,KAAK;QACrB,KAAK,MAAM1K,OAAO,IAAI0B,WAAW,EAAE;UAAA,IAAAgK,sBAAA;UACjC,MAAM;YAAEtL,QAAQ;YAAEE;UAAS,CAAC,GAAGP,gBAAgB,CAACC,OAAO,CAAC;UACxD,MAAMgB,IAAI,GAAGoI,eAAe,aAAfA,eAAe,wBAAAsC,sBAAA,GAAftC,eAAe,CAAGhJ,QAAQ,CAAC,cAAAsL,sBAAA,uBAA3BA,sBAAA,CAA8BpL,QAAQ,CAAC;UACpD,IAAI,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,CAAC,MAAK6C,SAAS,IAAInD,IAAI,CAACM,CAAC,KAAK,IAAI,IAAIN,IAAI,CAACM,CAAC,KAAK,EAAE,EAAE;YAC7DoJ,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QACAhE,cAAc,CAACgE,SAAS,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACd5I,OAAO,CAAC4I,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC,EAAE,CAAClF,eAAe,EAAEC,cAAc,EAAE7E,uBAAuB,EAAEH,WAAW,CAAC,CAAC;;EAE3E;EACA,MAAMkK,gBAAgB,GAAGvO,WAAW,CAAC,MAAM;IACzCqK,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACA,yBAAyB,CAAC,CAAC;EAE/BpK,SAAS,CAAC,MAAM;IAAA,IAAAuO,sBAAA;IACd,IAAI,CAAChF,YAAY,IAAIE,gBAAgB,CAACxB,OAAO,EAAE;IAE/C,MAAM;MAAEsD;IAAU,CAAC,GAAGjM,YAAY,CAAC;MACjCyL,MAAM,EAAEvL,UAAU,CAACwL,KAAK;MACxBwD,OAAO,EAAE;QAAE,CAAChP,UAAU,CAACwL,KAAK,GAAGvL,KAAK,CAAC,CAAC,CAAC,EAAEE,0BAA0B;MAAE,CAAC;MACtE8O,KAAK,EAAElP,YAAY;MACnBmP,OAAO,EAAE,CAAChP,sBAAsB,CAAC,CAAC;IACpC,CAAC,CAAC;IAEFgK,YAAY,CAACzB,OAAO,GAAGsD,SAAS;IAChCA,SAAS,CAACoD,cAAc,CAACpF,YAAY,CAAC;IAEtC,MAAMqF,eAAe,GAAGrF,YAAY,CAAC1E,MAAM,CAAC,UAAU,CAAC,CAAC1B,QAAQ;IAChEgG,eAAe,CACb/D,oBAAoB,CAClBwJ,eAAe,EACfvJ,kBAAkB,EAClBoC,sBAAsB,CAACQ,OAAO,EAC9B7D,WAAW,EACX,CAAAlB,OAAO,aAAPA,OAAO,wBAAAqL,sBAAA,GAAPrL,OAAO,CAAEsE,iBAAiB,cAAA+G,sBAAA,uBAA1BA,sBAAA,CAA4B7D,kBAAkB,KAAI,IACpD,CACF,CAAC;IAED,IAAInG,uBAAuB,EAAE;MAC3B6E,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACL,IAAIyF,eAAe,GAAG,KAAK;MAC3B,KAAK,MAAMnM,OAAO,IAAI0B,WAAW,EAAE;QAAA,IAAA0K,qBAAA;QACjC,MAAM;UAAEhM,QAAQ;UAAEE;QAAS,CAAC,GAAGP,gBAAgB,CAACC,OAAO,CAAC;QACxD,MAAMgB,IAAI,GAAGkL,eAAe,aAAfA,eAAe,wBAAAE,qBAAA,GAAfF,eAAe,CAAG9L,QAAQ,CAAC,cAAAgM,qBAAA,uBAA3BA,qBAAA,CAA8B9L,QAAQ,CAAC;QACpD,IAAI,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,CAAC,MAAK6C,SAAS,IAAInD,IAAI,CAACM,CAAC,KAAK,IAAI,IAAIN,IAAI,CAACM,CAAC,KAAK,EAAE,EAAE;UAC7D6K,eAAe,GAAG,IAAI;UACtB;QACF;MACF;MACAzF,cAAc,CAACyF,eAAe,CAAC;IACjC;IAEA,MAAME,yBAAyB,GAAG,MAAAA,CAAA,KAAY;MAC5C,MAAMvD,IAAI,GAAGD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,iBAAiB,CAAC,CAAC;MAC3C,MAAMC,KAAK,GAAGF,IAAI,CAACG,cAAc,CAAC,CAAC;MACnC,MAAMqD,MAAM,GAAGxD,IAAI,CAACyD,KAAK,CAAC,CAAC;MAC3B,MAAMC,OAAO,GAAGxD,KAAK,CAACyD,UAAU,CAAC,CAAC;MAClC,MAAMC,UAAU,GAAG5D,IAAI,CAAC6D,aAAa,CAAC,CAAC;MACvC,MAAMC,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAIpJ,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,GAAG,EAAEA,GAAG,EAAE,EAAE;QACnC,KAAK,IAAIlE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAEA,GAAG,EAAE,EAAE;UACjCsN,QAAQ,CAACC,IAAI,CAAE,GAAElN,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGN,GAAG,CAAE,GAAEkE,GAAI,EAAC,CAAC;QACzD;MACF;MACA,MAAMsJ,WAAW,GAAGF,QAAQ,CAACG,MAAM,CAChC/L,IAAI,IAAK,CAACU,WAAW,CAAC/C,QAAQ,CAACqC,IAAI,CACtC,CAAC;MACD,MAAMgM,MAAM,GAAGF,WAAW,CAACG,GAAG,CAAEjM,IAAI,IAAKgI,KAAK,CAACa,QAAQ,CAAC7I,IAAI,CAAC,CAAC;MAC9D,MAAM;QAAEkM;MAAa,CAAC,GAAG,MAAMR,UAAU,CAACS,sBAAsB,CAC9Db,MAAM,EACNE,OAAO,EACPQ,MACF,CAAC;MACD,MAAMI,SAAS,GACbV,UAAU,CAACW,0BAA0B,CAClCC,kCAAkC;MACvCZ,UAAU,CAACa,0BAA0B,CAACC,SAAS,CAAE1L,EAAE,IAAK;QACtD,IAAIA,EAAE,KAAKoL,YAAY,EAAE;UACvBR,UAAU,CAACe,iCAAiC,CAC1CnB,MAAM,EACNE,OAAO,EACPU,YAAY,EACZE,SAAS,EACT,KACF,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC;IAEDf,yBAAyB,CAAC,CAAC;;IAE3B;IACA,MAAMqB,yBAAyB,GAAG1P,QAAQ,CAAC4N,gBAAgB,EAAE,GAAG,CAAC;IACjExE,kBAAkB,CAAC7B,OAAO,GAAGmI,yBAAyB;IAEtD7E,SAAS,CAAC8E,QAAQ,CAAC9E,SAAS,CAAC+E,KAAK,CAACC,iBAAiB,EAAGC,MAAM,IAAK;MAAA,IAAAC,aAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;MAChE,MAAMC,QAAQ,GAAGN,MAAM,aAANA,MAAM,wBAAAC,aAAA,GAAND,MAAM,CAAErP,KAAK,cAAAsP,aAAA,wBAAAC,mBAAA,GAAbD,aAAA,CAAeM,KAAK,cAAAL,mBAAA,wBAAAC,qBAAA,GAApBD,mBAAA,CAAsBM,IAAI,cAAAL,qBAAA,uBAA1BA,qBAAA,CAA4BM,UAAU;MACvD,MAAM/K,GAAG,GAAGsK,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtK,GAAG;MACvB,MAAMgL,MAAM,GAAGV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEU,MAAM;MAC7B,IAAIJ,QAAQ,KAAKjK,SAAS,IAAIX,GAAG,KAAKW,SAAS,IAAIqK,MAAM,KAAKrK,SAAS,EACrE;MAEF,MAAMsK,YAAY,GAAG9O,MAAM,CAACyO,QAAQ,CAAC,CAACjP,IAAI,CAAC,CAAC;MAC5C,MAAMwK,OAAO,GAAI,GAAEnG,GAAI,IAAGgL,MAAO,EAAC;MAClC,MAAMxO,OAAO,GAAI,GAAEN,cAAc,CAAC8O,MAAM,CAAE,GAAEhL,GAAG,GAAG,CAAE,EAAC;;MAErD;MACAuB,sBAAsB,CAACQ,OAAO,CAACC,GAAG,CAACxF,OAAO,CAAC;;MAE3C;MACA,IAAI0O,UAAU,GAAGD,YAAY;MAC7B,IAAI7I,QAAQ,GAAG,CAAC,CAAC,CAAC;;MAElB;MACA,IACE6I,YAAY,KAAK,EAAE,IACnB,CAAC1K,KAAK,CAAC0K,YAAY,CAAC,IACpB,CAAC1K,KAAK,CAAC+B,UAAU,CAAC2I,YAAY,CAAC,CAAC,EAChC;QACAC,UAAU,GAAGxK,MAAM,CAACuK,YAAY,CAAC;QACjC7I,QAAQ,GAAG,CAAC,CAAC,CAAC;MAChB;;MAEAsB,iBAAiB,CAAC3B,OAAO,CAACoJ,GAAG,CAAChF,OAAO,EAAE+E,UAAU,CAAC;MAElD,MAAMxF,WAAW,GAAGjC,eAAe,CAAC1B,OAAO;MAC3C,MAAM9E,QAAQ,GAAG,CAAAyI,WAAW,aAAXA,WAAW,wBAAAgF,oBAAA,GAAXhF,WAAW,CAAE/G,MAAM,cAAA+L,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,UAAU,CAAC,cAAAC,qBAAA,uBAAjCA,qBAAA,CAAmC1N,QAAQ,KAAI,CAAC,CAAC;MAClE,MAAM2I,eAAe,GAAG;QAAE,GAAG3I;MAAS,CAAC;MACvC,IAAI,CAAC2I,eAAe,CAAC5F,GAAG,CAAC,EAAE4F,eAAe,CAAC5F,GAAG,CAAC,GAAG,CAAC,CAAC;MACpD,MAAM6G,YAAY,GAAGjB,eAAe,CAAC5F,GAAG,CAAC,CAACgL,MAAM,CAAC,IAAI,CAAC,CAAC;MAEvD,MAAM9K,YAAY,GAAGhC,WAAW,CAACuD,IAAI,CAAEjF,OAAO,IAAK;QACjD,MAAM;UAAEI,QAAQ;UAAEE;QAAS,CAAC,GAAGP,gBAAgB,CAACC,OAAO,CAAC;QACxD,OAAOI,QAAQ,KAAKoD,GAAG,IAAIlD,QAAQ,KAAKkO,MAAM;MAChD,CAAC,CAAC;MAEF,IAAI9K,YAAY,EAAE;QAChB,IAAI7B,uBAAuB,EAAE;UAC3BuH,eAAe,CAAC5F,GAAG,CAAC,CAACgL,MAAM,CAAC,GAAG;YAC7B,GAAGnE,YAAY;YACf/I,CAAC,EAAE,EAAE;YACL0C,CAAC,EAAE,CAAC;YACJ5C,CAAC,EAAE;cACDO,EAAE,EAAE;gBAAEC,GAAG,EAAE;cAAU,CAAC;cACtBP,EAAE,EAAE,CAAC,CAAE;YACT;UACF,CAAC;QACH,CAAC,MAAM;UACL+H,eAAe,CAAC5F,GAAG,CAAC,CAACgL,MAAM,CAAC,GAAG;YAC7B,GAAGnE,YAAY;YACf/I,CAAC,EAAEoN,UAAU;YACb1K,CAAC,EAAE4B,QAAQ;YACXxE,CAAC,EAAE;cACDO,EAAE,EAAE;gBAAEC,GAAG,EAAE;cAAU,CAAC;cACtBP,EAAE,EAAE,CAAC,CAAE;YACT;UACF,CAAC;QACH;MACF,CAAC,MAAM;QACL+H,eAAe,CAAC5F,GAAG,CAAC,CAACgL,MAAM,CAAC,GAAG;UAC7B,GAAGnE,YAAY;UACf/I,CAAC,EAAEoN,UAAU;UACb1K,CAAC,EAAE4B,QAAQ;UACXxE,CAAC,EAAE;YACD,IAAI,CAAAiJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEjJ,CAAC,KAAI,CAAC,CAAC,CAAC;YAC1BC,EAAE,EAAE,CAAC,CAAE;UACT;QACF,CAAC;MACH;;MAEA4F,eAAe,CAAC1B,OAAO,GAAG;QACxB,GAAG2D,WAAW;QACd/G,MAAM,EAAE;UACN,GAAG+G,WAAW,CAAC/G,MAAM;UACrB,CAAC,UAAU,GAAG;YACZ,GAAG+G,WAAW,CAAC/G,MAAM,CAAC,UAAU,CAAC;YACjC1B,QAAQ,EAAE2I;UACZ;QACF;MACF,CAAC;MAEDsE,yBAAyB,CAAC,CAAC;IAC7B,CAAC,CAAC;;IAEF;IACA,MAAMkB,4BAA4B,GAAG5Q,QAAQ,CAC3C4M,yBAAyB,EACzB,GACF,CAAC;;IAED;IACA,MAAMiE,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAM/F,IAAI,GAAGD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,iBAAiB,CAAC,CAAC;MAC3C,MAAMC,KAAK,GAAGF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,cAAc,CAAC,CAAC;MACpC,IAAI,CAACD,KAAK,EAAE;;MAEZ;MACA,MAAMK,SAAS,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAChD,MAAM7I,MAAM,GAAGb,IAAI,CAACiP,GAAG,CAACzF,SAAS,CAAC/G,QAAQ,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACtD,MAAM3B,MAAM,GAAGd,IAAI,CAACiP,GAAG,CAACzF,SAAS,CAAC9G,WAAW,GAAG,CAAC,EAAE,EAAE,CAAC;MAEtD,KAAK,IAAIiH,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;QAC/C,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;UAC/C,MAAMG,KAAK,GAAGZ,KAAK,CAACa,QAAQ,CAACL,MAAM,EAAEC,MAAM,CAAC;UAC5C,MAAMK,OAAO,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC;UAClC,MAAMC,eAAe,GAAGJ,KAAK,CAACK,QAAQ,CAAC,CAAC;UAExC,IACEH,OAAO,IACPA,OAAO,CAAC5I,UAAU,CAAC,GAAG,CAAC,IACvB,OAAO8I,eAAe,KAAK,QAAQ,EACnC;YACA,MAAM+E,QAAQ,GAAGjF,OAAO,CAAC7J,KAAK,CAAC,YAAY,CAAC;YAC5C,IAAI8O,QAAQ,EAAE;cACZ,IAAIC,yBAAyB,GAAG,KAAK;cAErC,KAAK,MAAMhP,OAAO,IAAI+O,QAAQ,EAAE;gBAC9B,MAAME,UAAU,GAAGlP,gBAAgB,CAACC,OAAO,CAAC;gBAC5C,IAAIiP,UAAU,EAAE;kBACd,MAAMC,QAAQ,GAAGlG,KAAK,CAACa,QAAQ,CAC7BoF,UAAU,CAAC7O,QAAQ,EACnB6O,UAAU,CAAC3O,QACb,CAAC;kBACD,MAAM6O,eAAe,GAAGD,QAAQ,CAAC/E,eAAe,CAAC,CAAC;kBAClD,IACEgF,eAAe,IACf,OAAOA,eAAe,KAAK,QAAQ,IACnC3Q,oBAAoB,CAAC2Q,eAAe,CAAC,EACrC;oBACAH,yBAAyB,GACvBxQ,oBAAoB,CAAC2Q,eAAe,CAAC;oBACvC;kBACF;gBACF;cACF;cAEA,IAAIH,yBAAyB,EAAE;gBAC7B,MAAMI,mBAAmB,GAAGxF,KAAK,CAACO,eAAe,CAAC,CAAC;gBACnD;gBACA,IACE,CAACiF,mBAAmB,IACpB,CAAC5Q,oBAAoB,CAAC4Q,mBAAmB,CAAC,EAC1C;kBACA,IAAI;oBACF,MAAMhJ,aAAa,GAAGvH,wBAAwB,CAC5CmQ,yBACF,CAAC;oBACDpF,KAAK,CAACyF,eAAe,CAACjJ,aAAa,CAAC;kBACtC,CAAC,CAAC,OAAOkJ,CAAC,EAAE,CAAC;gBACf;cACF;YACF;UACF;QACF;MACF;IACF,CAAC;IAED,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;MACrC,MAAM1G,SAAS,GAAG7B,YAAY,CAACzB,OAAO;MACtC,MAAMuD,IAAI,GAAGD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,iBAAiB,CAAC,CAAC;MAC3C,MAAMC,KAAK,GAAGF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,cAAc,CAAC,CAAC;MACpC,IAAID,KAAK,EAAE;QACT,MAAMK,SAAS,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAChD,MAAM7I,MAAM,GAAG2I,SAAS,CAAC/G,QAAQ,GAAG,CAAC;QACrC,MAAM3B,MAAM,GAAG0I,SAAS,CAAC9G,WAAW,GAAG,CAAC;QAExC,KAAK,IAAIiH,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;UAC/C,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI9I,MAAM,EAAE8I,MAAM,EAAE,EAAE;YAC/C,MAAMG,KAAK,GAAGZ,KAAK,CAACa,QAAQ,CAACL,MAAM,EAAEC,MAAM,CAAC;YAC5C,IAAIG,KAAK,EAAE;cACT,MAAM5J,OAAO,GAAI,GAAEN,cAAc,CAAC+J,MAAM,CAAE,GAAED,MAAM,GAAG,CAAE,EAAC;cACxD,MAAM9F,YAAY,GAAGhC,WAAW,CAAC/C,QAAQ,CAACqB,OAAO,CAAC;;cAElD;cACA,IAAI0D,YAAY,EAAE;gBAChB,IAAI8L,WAAW,GAAG,KAAK;;gBAEvB;gBACA,IAAI,OAAO5F,KAAK,CAACG,UAAU,KAAK,UAAU,EAAE;kBAC1C,MAAMD,OAAO,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC;kBAClC,IAAID,OAAO,EAAE;oBACX0F,WAAW,GAAG,IAAI;kBACpB;gBACF;;gBAEA;gBACA,IAAI,CAACA,WAAW,IAAI,OAAO5F,KAAK,CAACK,QAAQ,KAAK,UAAU,EAAE;kBACxD,MAAMxL,KAAK,GAAGmL,KAAK,CAACK,QAAQ,CAAC,CAAC;kBAC9B;kBACA;kBACA;kBACA;kBACA,IACExL,KAAK,KAAK,IAAI,IACdA,KAAK,KAAK0F,SAAS,IACnB1F,KAAK,KAAK,EAAE,IACZA,KAAK,KAAK,CAAC,IACX,CAACsG,sBAAsB,CAACQ,OAAO,CAAC5B,GAAG,CAAC3D,OAAO,CAAC,EAC5C;oBACAwP,WAAW,GAAG,IAAI;kBACpB;gBACF;;gBAEA;gBACA,IAAIA,WAAW,EAAE;kBACfzK,sBAAsB,CAACQ,OAAO,CAACC,GAAG,CAACxF,OAAO,CAAC;gBAC7C;cACF;YACF;UACF;QACF;MACF;;MAEA;MACA;MACA,MAAMyP,aAAa,GAAG,EAAE;MACxB1K,sBAAsB,CAACQ,OAAO,CAACxE,OAAO,CAAEf,OAAO,IAAK;QAClD,MAAMiB,OAAO,GAAGlB,gBAAgB,CAACC,OAAO,CAAC;QACzC,IAAIiB,OAAO,EAAE;UACX,MAAM;YAAEb,QAAQ;YAAEE;UAAS,CAAC,GAAGW,OAAO;UACtC,MAAM2I,KAAK,GAAGZ,KAAK,CAACa,QAAQ,CAACzJ,QAAQ,EAAEE,QAAQ,CAAC;UAChD,IAAIsJ,KAAK,EAAE;YACT,MAAMnL,KAAK,GAAGmL,KAAK,CAACK,QAAQ,CAAC,CAAC;YAC9B,MAAMH,OAAO,GAAGF,KAAK,CAACG,UAAU,CAAC,CAAC;YAClC,MAAMrG,YAAY,GAAGhC,WAAW,CAAC/C,QAAQ,CAACqB,OAAO,CAAC;;YAElD;YACA,IAAI0D,YAAY,IAAIjF,KAAK,KAAK,CAAC,IAAI,CAACqL,OAAO,EAAE;cAC3C2F,aAAa,CAAC5C,IAAI,CAAC7M,OAAO,CAAC;YAC7B;UACF;QACF;MACF,CAAC,CAAC;;MAEF;MACAyP,aAAa,CAAC1O,OAAO,CAAEf,OAAO,IAAK;QACjC+C,OAAO,CAACC,GAAG,CACR,iBAAgBhD,OAAQ,4DAC3B,CAAC;QACD+E,sBAAsB,CAACQ,OAAO,CAACmK,MAAM,CAAC1P,OAAO,CAAC;MAChD,CAAC,CAAC;MAEF,IAAIyP,aAAa,CAACjQ,MAAM,GAAG,CAAC,EAAE;QAC5BuD,OAAO,CAACC,GAAG,CACR,cAAayM,aAAa,CAACjQ,MAAO,kCACrC,CAAC;MACH;;MAEA;MACAkI,yBAAyB,CAAC,CAAC;;MAE3B;MACApJ,UAAU,CAAC,MAAM;QACfsM,yBAAyB,CAAC,CAAC;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;;IAED;IACA,MAAM+E,0BAA0B,GAAG3R,QAAQ,CAAC6Q,uBAAuB,EAAE,GAAG,CAAC;;IAEzE;IACAhG,SAAS,CAAC8E,QAAQ,CAAC9E,SAAS,CAAC+E,KAAK,CAACgC,iBAAiB,EAAE,MAAM;MAC1DD,0BAA0B,CAAC,CAAC;MAC5Bf,4BAA4B,CAAC,CAAC;IAChC,CAAC,CAAC;;IAEF;IACA,MAAMiB,gBAAgB,GAAGC,QAAQ,CAACC,cAAc,CAACnJ,OAAO,CAAC;IACzD,IAAIiJ,gBAAgB,EAAE;MACpB,IAAIG,UAAU,GAAG,KAAK;MACtB,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIC,QAAQ,GAAG,KAAK;MAEpB,MAAMC,eAAe,GAAIb,CAAC,IAAK;QAC7B,IAAIA,CAAC,CAACc,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,EAAE;UACzCL,UAAU,GAAG,KAAK;UAClBE,QAAQ,GAAG,KAAK;UAChB;UACA,IAAID,SAAS,EAAE;YACb5R,YAAY,CAAC4R,SAAS,CAAC;YACvBA,SAAS,GAAG,IAAI;UAClB;QACF;MACF,CAAC;;MAED;MACA,MAAMK,aAAa,GAAIhB,CAAC,IAAK;QAC3B,IAAIA,CAAC,CAACc,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,EAAE;UACzCH,QAAQ,GAAG,IAAI;UACf;UACA5R,UAAU,CAAC,MAAM;YACf4R,QAAQ,GAAG,KAAK;UAClB,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC;MAED,MAAMK,eAAe,GAAIjB,CAAC,IAAK;QAC7B,IAAIA,CAAC,CAACkB,OAAO,KAAK,CAAC,EAAE;UACnBR,UAAU,GAAG,IAAI;QACnB;MACF,CAAC;MAED,MAAMS,aAAa,GAAGA,CAAA,KAAM;QAC1B,IAAIT,UAAU,EAAE;UACd1R,UAAU,CAAC,MAAM;YACfiR,wBAAwB,CAAC,CAAC;UAC5B,CAAC,EAAE,GAAG,CAAC;UAEPjR,UAAU,CAAC,MAAM;YACfiR,wBAAwB,CAAC,CAAC;UAC5B,CAAC,EAAE,GAAG,CAAC;UAEPjR,UAAU,CAAC,MAAM;YACfiR,wBAAwB,CAAC,CAAC;UAC5B,CAAC,EAAE,GAAG,CAAC;UAEPS,UAAU,GAAG,KAAK;QACpB;MACF,CAAC;MAEDH,gBAAgB,CAACa,gBAAgB,CAAC,WAAW,EAAEP,eAAe,CAAC;MAC/DN,gBAAgB,CAACa,gBAAgB,CAAC,WAAW,EAAEH,eAAe,CAAC;MAC/DV,gBAAgB,CAACa,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MAC3DZ,gBAAgB,CAACa,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;;MAE3D;MACAT,gBAAgB,CAACc,aAAa,GAAG;QAC/BC,SAAS,EAAET,eAAe;QAC1BU,SAAS,EAAEN,eAAe;QAC1BO,OAAO,EAAEL,aAAa;QACtBM,OAAO,EAAET;MACX,CAAC;MAED,MAAMU,WAAW,GAAGA,CAAA,KAAM;QACxB1S,UAAU,CAAC,MAAM;UACfsQ,4BAA4B,CAAC,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAEDiB,gBAAgB,CAACa,gBAAgB,CAAC,OAAO,EAAEM,WAAW,CAAC;;MAEvD;MACAnB,gBAAgB,CAACoB,aAAa,GAAGD,WAAW;IAC9C;;IAEA;IACA,IAAI;MACF;MACA,MAAME,cAAc,GAAG,CACrB,iBAAiB,EACjB,eAAe,EACf,eAAe,EACf,mBAAmB,CACpB;MAEDA,cAAc,CAACnQ,OAAO,CAAEoQ,SAAS,IAAK;QACpC,IAAItI,SAAS,CAAC+E,KAAK,CAACuD,SAAS,CAAC,EAAE;UAC9BtI,SAAS,CAAC8E,QAAQ,CAAC9E,SAAS,CAAC+E,KAAK,CAACuD,SAAS,CAAC,EAAGrD,MAAM,IAAK;YACzDxP,UAAU,CAAC,MAAM;cACfqR,0BAA0B,CAAC,CAAC;cAC5Bf,4BAA4B,CAAC,CAAC;YAChC,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOU,CAAC,EAAE,CAAC;;IAEb;IACA,IAAI;MACF,IAAIzG,SAAS,CAAC+E,KAAK,CAACwD,eAAe,EAAE;QACnCvI,SAAS,CAAC8E,QAAQ,CAChB9E,SAAS,CAAC+E,KAAK,CAACwD,eAAe,EAC/BxC,4BACF,CAAC;MACH;IACF,CAAC,CAAC,OAAOU,CAAC,EAAE,CAAC;IAEbvI,gBAAgB,CAACxB,OAAO,GAAG,IAAI;IAE/B,OAAO,MAAM;MAAA,IAAA8L,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACX;MACA,KAAAF,sBAAA,GAAIjK,kBAAkB,CAAC7B,OAAO,cAAA8L,sBAAA,eAA1BA,sBAAA,CAA4B7J,MAAM,EAAE;QACtCJ,kBAAkB,CAAC7B,OAAO,CAACiC,MAAM,CAAC,CAAC;MACrC;;MAEA;MACA,MAAMqI,gBAAgB,GAAGC,QAAQ,CAACC,cAAc,CAACnJ,OAAO,CAAC;MACzD,IAAIiJ,gBAAgB,EAAE;QACpB;QACA,IAAIA,gBAAgB,CAACoB,aAAa,EAAE;UAClCpB,gBAAgB,CAAC2B,mBAAmB,CAClC,OAAO,EACP3B,gBAAgB,CAACoB,aACnB,CAAC;UACD,OAAOpB,gBAAgB,CAACoB,aAAa;QACvC;;QAEA;QACA,IAAIpB,gBAAgB,CAACc,aAAa,EAAE;UAClCd,gBAAgB,CAAC2B,mBAAmB,CAClC,WAAW,EACX3B,gBAAgB,CAACc,aAAa,CAACC,SACjC,CAAC;UACDf,gBAAgB,CAAC2B,mBAAmB,CAClC,WAAW,EACX3B,gBAAgB,CAACc,aAAa,CAACE,SACjC,CAAC;UACDhB,gBAAgB,CAAC2B,mBAAmB,CAClC,SAAS,EACT3B,gBAAgB,CAACc,aAAa,CAACG,OACjC,CAAC;UACDjB,gBAAgB,CAAC2B,mBAAmB,CAClC,SAAS,EACT3B,gBAAgB,CAACc,aAAa,CAACI,OACjC,CAAC;UACD,OAAOlB,gBAAgB,CAACc,aAAa;QACvC;MACF;MAEA,CAAAW,qBAAA,GAAAtK,YAAY,CAACzB,OAAO,cAAA+L,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBG,OAAO,cAAAF,sBAAA,uBAA7BA,sBAAA,CAAA9J,IAAA,CAAA6J,qBAAgC,CAAC;MACjCtK,YAAY,CAACzB,OAAO,GAAG,IAAI;MAC3BwB,gBAAgB,CAACxB,OAAO,GAAG,KAAK;MAChC2B,iBAAiB,CAAC3B,OAAO,CAACiD,KAAK,CAAC,CAAC;MACjCzD,sBAAsB,CAACQ,OAAO,CAACiD,KAAK,CAAC,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,CACD3B,YAAY,EACZD,OAAO,EACPH,eAAe,EACfC,cAAc,EACd7E,uBAAuB,EACvBH,WAAW,EACXkK,gBAAgB,EAChBhB,yBAAyB,CAC1B,CAAC;EAEF,oBACE7M,OAAA;IAAA2T,QAAA,gBACE3T,OAAA;MAAK4T,SAAS,EAAC,kBAAkB;MAAC7P,EAAE,EAAE8E;IAAQ;MAAAgL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjDhU,OAAA,CAACJ,cAAc;MAACqU,UAAU,EAAEpU;IAAK;MAAAgU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjC,CAAC;AAEV,CAAC,iCACH,CAAC;AAACE,GAAA,GA96BI5L,WAAW;AAg7BjBA,WAAW,CAAC6L,WAAW,GAAG,aAAa;AAEvC,eAAe7L,WAAW;AAAC,IAAAE,EAAA,EAAA0L,GAAA;AAAAE,YAAA,CAAA5L,EAAA;AAAA4L,YAAA,CAAAF,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}