{"name": "get-user-locale", "version": "2.3.0", "description": "Returns a list of strings representing the user's preferred languages.", "type": "module", "sideEffects": false, "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "source": "./src/index.ts", "types": "./dist/cjs/index.d.ts", "exports": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "scripts": {"build": "yarn build-esm && yarn build-cjs", "build-esm": "tsc --project tsconfig.build.json --outDir dist/esm --module esnext", "build-cjs": "tsc --project tsconfig.build.json --outDir dist/cjs --module commonjs && echo '{\n  \"type\": \"commonjs\"\n}' > dist/cjs/package.json", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "prepack": "yarn clean && yarn build", "prettier": "prettier --check . --cache", "test": "yarn lint && yarn tsc && yarn prettier && yarn unit", "tsc": "tsc --noEmit", "unit": "vitest"}, "keywords": ["locale", "language", "language-detection"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@types/lodash.memoize": "^4.1.7", "lodash.memoize": "^4.1.1"}, "devDependencies": {"eslint": "^8.26.0", "eslint-config-wojtekmaj": "^0.8.4", "husky": "^8.0.0", "jsdom": "^21.1.0", "prettier": "^2.7.0", "pretty-quick": "^3.1.0", "rimraf": "^3.0.0", "typescript": "^5.0.0", "vitest": "^0.30.1"}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "https://github.com/wojtekmaj/get-user-locale.git"}, "funding": "https://github.com/wojtekmaj/get-user-locale?sponsor=1", "packageManager": "yarn@3.1.0"}