{"version": 3, "file": "ReactHookFormInput.js", "names": ["React", "useRef", "useCallback", "useImperativeHandle", "Controller", "PropTypes", "ReactHookFormInput", "ref", "Component", "name", "defaultValue", "shouldUnregister", "control", "rules", "onChange_", "onChange", "onBlur_", "onBlur", "rest", "internalRef", "setRef", "instance", "current", "undefined", "renderInputComponent", "restReactHookFormControlledFieldProps", "focus", "setComponentRef", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "onFocus", "props", "field", "forwardRef", "propTypes", "elementType", "isRequired", "string", "bool", "object", "func"], "sources": ["../../source/react-hook-form/ReactHookFormInput.js"], "sourcesContent": ["import React, { useRef, useCallback, useImperativeHandle } from 'react'\r\nimport { Controller } from 'react-hook-form'\r\nimport PropTypes from 'prop-types'\r\n\r\nlet ReactHookFormInput = ({\r\n  Component,\r\n  name,\r\n  defaultValue,\r\n  shouldUnregister,\r\n  control,\r\n  rules,\r\n  onChange: onChange_,\r\n  onBlur: onBlur_,\r\n  ...rest\r\n}, ref) => {\r\n  const internalRef = useRef()\r\n\r\n  const setRef = useCallback((instance) => {\r\n    internalRef.current = instance\r\n    if (ref) {\r\n      if (typeof ref === 'function') {\r\n        ref(instance)\r\n      } else {\r\n        ref.current = instance\r\n      }\r\n    }\r\n  }, [ref])\r\n\r\n  // `feact-hook-form` doesn't know how to properly handle `undefined` values.\r\n  // https://github.com/react-hook-form/react-hook-form/issues/2990\r\n  defaultValue = defaultValue === undefined ? null : defaultValue\r\n\r\n  const renderInputComponent = ({\r\n    ref,\r\n    onChange,\r\n    onBlur,\r\n    // `restReactHookFormControlledFieldProps` contain properties like `name` and `value`.\r\n    // https://github.com/react-hook-form/react-hook-form/blob/b0e6c3057ac12a7b12d5616aecf3791acb7d7204/src/types/controller.ts#L21-L30\r\n    ...restReactHookFormControlledFieldProps\r\n  }) => {\r\n    // Setting `ref` passed by `react-hook-form` results in a bug:\r\n    // when an initial value is defined (example: \"+78005553535\")\r\n    // it seems to be set directly on the `ref`d `<input/>`\r\n    // by `react-hook-form` and the result is a non-formatted\r\n    // \"+78005553535\" initial value in the `<input/>`.\r\n    //\r\n    // To work around that bug, a fake `ref` is assigned,\r\n    // so that it could only `.focus()` it and no more.\r\n    //\r\n    // `useImperativeHandle()` hook seems to allow `ref` being `undefined`.\r\n    //\r\n    // if (ref) {\r\n      useImperativeHandle(ref, () => ({\r\n        focus() {\r\n          internalRef.current.focus()\r\n        }\r\n      }))\r\n    // }\r\n\r\n    const setComponentRef = useCallback((instance) => {\r\n      setRef(instance)\r\n      // if (ref) {\r\n      //   if (typeof ref === 'function') {\r\n      //     ref(instance)\r\n      //   } else {\r\n      //     ref.current = instance\r\n      //   }\r\n      // }\r\n    }, [ref, setRef])\r\n\r\n    // This function may not work correctly when `defaultValues` are set for the input\r\n    // and the user clears the input value manually: the default value may re-appear as a result.\r\n    // https://github.com/catamphetamine/react-phone-number-input/issues/405#issuecomment-1295885201\r\n    const onChangeCombined = useCallback((value) => {\r\n      // `react-hook-form` doesn't know how to properly handle `undefined` values.\r\n      // https://github.com/react-hook-form/react-hook-form/issues/2990\r\n      if (value === undefined) {\r\n        value = null\r\n      }\r\n      onChange(value)\r\n      if (onChange_) {\r\n        onChange_(value)\r\n      }\r\n    }, [\r\n      onChange,\r\n      onChange_\r\n    ])\r\n\r\n    const onBlurCombined = useCallback((event) => {\r\n      onBlur(event)\r\n      if (onBlur_) {\r\n        onBlur_(event)\r\n      }\r\n    }, [\r\n      onBlur,\r\n      onBlur_\r\n    ])\r\n\r\n    return (\r\n      <Component\r\n        {...rest}\r\n        {...restReactHookFormControlledFieldProps}\r\n        ref={setComponentRef}\r\n        onChange={onChangeCombined}\r\n        onBlur={onBlurCombined}/>\r\n    )\r\n  }\r\n\r\n  // `react-hook-form@7` no longer accepts `onFocus` property.\r\n  // Since this component can be used with both `v6` and `v7`,\r\n  // the `onFocus` property is left here.\r\n  const onFocus = useCallback(() => {\r\n    // internalRef.current.disabled = false\r\n    internalRef.current.focus()\r\n  }, [])\r\n\r\n  return (\r\n    <Controller\r\n      control={control}\r\n      name={name}\r\n      defaultValue={defaultValue}\r\n      shouldUnregister={shouldUnregister}\r\n      rules={rules}\r\n      onFocus={onFocus}\r\n      render={(props) => {\r\n        // Differentiate between `react-hook-form@6` and `react-hook-form@7`.\r\n        // https://react-hook-form.com/migrate-v6-to-v7/\r\n        // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/57\r\n        // `props` (before v7) and `props.fields` (in v7) contain properties like:\r\n        // `ref`, `name`, `value`, `onChange`, `onBlur`.\r\n        // https://github.com/react-hook-form/react-hook-form/blob/b0e6c3057ac12a7b12d5616aecf3791acb7d7204/src/types/controller.ts#L21-L30\r\n        return renderInputComponent(props.field || props)\r\n      }}/>\r\n  )\r\n}\r\n\r\nReactHookFormInput = React.forwardRef(ReactHookFormInput)\r\n\r\nReactHookFormInput.propTypes = {\r\n  Component: PropTypes.elementType.isRequired,\r\n  name: PropTypes.string.isRequired,\r\n  defaultValue: PropTypes.string,\r\n  // A quote from `react-hook-form`:\r\n  // Without `shouldUnregister: true`, an input value would be retained when input is removed.\r\n  // Setting `shouldUnregister: true` makes the form behave more closer to native.\r\n  shouldUnregister: PropTypes.bool,\r\n  // A developer should pass a `control` object that is returned from `useForm()` hook.\r\n  // Not required when using `<FormProvider/>`.\r\n  control: PropTypes.object,\r\n  rules: PropTypes.object,\r\n  onChange: PropTypes.func,\r\n  onBlur: PropTypes.func\r\n}\r\n\r\nexport default ReactHookFormInput"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,MAAhB,EAAwBC,WAAxB,EAAqCC,mBAArC,QAAgE,OAAhE;AACA,SAASC,UAAT,QAA2B,iBAA3B;AACA,OAAOC,SAAP,MAAsB,YAAtB;;AAEA,IAAIC,kBAAkB,GAAG,kCAUtBC,GAVsB,EAUd;EAAA,IATTC,SASS,QATTA,SASS;EAAA,IARTC,IAQS,QARTA,IAQS;EAAA,IAPTC,YAOS,QAPTA,YAOS;EAAA,IANTC,gBAMS,QANTA,gBAMS;EAAA,IALTC,OAKS,QALTA,OAKS;EAAA,IAJTC,KAIS,QAJTA,KAIS;EAAA,IAHCC,SAGD,QAHTC,QAGS;EAAA,IAFDC,OAEC,QAFTC,MAES;EAAA,IADNC,IACM;;EACT,IAAMC,WAAW,GAAGlB,MAAM,EAA1B;EAEA,IAAMmB,MAAM,GAAGlB,WAAW,CAAC,UAACmB,QAAD,EAAc;IACvCF,WAAW,CAACG,OAAZ,GAAsBD,QAAtB;;IACA,IAAId,GAAJ,EAAS;MACP,IAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;QAC7BA,GAAG,CAACc,QAAD,CAAH;MACD,CAFD,MAEO;QACLd,GAAG,CAACe,OAAJ,GAAcD,QAAd;MACD;IACF;EACF,CATyB,EASvB,CAACd,GAAD,CATuB,CAA1B,CAHS,CAcT;EACA;;EACAG,YAAY,GAAGA,YAAY,KAAKa,SAAjB,GAA6B,IAA7B,GAAoCb,YAAnD;;EAEA,IAAMc,oBAAoB,GAAG,SAAvBA,oBAAuB,QAOvB;IAAA,IANJjB,GAMI,SANJA,GAMI;IAAA,IALJQ,QAKI,SALJA,QAKI;IAAA,IAJJE,MAII,SAJJA,MAII;IAAA,IADDQ,qCACC;;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACEtB,mBAAmB,CAACI,GAAD,EAAM;MAAA,OAAO;QAC9BmB,KAD8B,mBACtB;UACNP,WAAW,CAACG,OAAZ,CAAoBI,KAApB;QACD;MAH6B,CAAP;IAAA,CAAN,CAAnB,CAbE,CAkBJ;;IAEA,IAAMC,eAAe,GAAGzB,WAAW,CAAC,UAACmB,QAAD,EAAc;MAChDD,MAAM,CAACC,QAAD,CAAN,CADgD,CAEhD;MACA;MACA;MACA;MACA;MACA;MACA;IACD,CATkC,EAShC,CAACd,GAAD,EAAMa,MAAN,CATgC,CAAnC,CApBI,CA+BJ;IACA;IACA;;IACA,IAAMQ,gBAAgB,GAAG1B,WAAW,CAAC,UAAC2B,KAAD,EAAW;MAC9C;MACA;MACA,IAAIA,KAAK,KAAKN,SAAd,EAAyB;QACvBM,KAAK,GAAG,IAAR;MACD;;MACDd,QAAQ,CAACc,KAAD,CAAR;;MACA,IAAIf,SAAJ,EAAe;QACbA,SAAS,CAACe,KAAD,CAAT;MACD;IACF,CAVmC,EAUjC,CACDd,QADC,EAEDD,SAFC,CAViC,CAApC;IAeA,IAAMgB,cAAc,GAAG5B,WAAW,CAAC,UAAC6B,KAAD,EAAW;MAC5Cd,MAAM,CAACc,KAAD,CAAN;;MACA,IAAIf,OAAJ,EAAa;QACXA,OAAO,CAACe,KAAD,CAAP;MACD;IACF,CALiC,EAK/B,CACDd,MADC,EAEDD,OAFC,CAL+B,CAAlC;IAUA,oBACE,oBAAC,SAAD,eACME,IADN,EAEMO,qCAFN;MAGE,GAAG,EAAEE,eAHP;MAIE,QAAQ,EAAEC,gBAJZ;MAKE,MAAM,EAAEE;IALV,GADF;EAQD,CA1ED,CAlBS,CA8FT;EACA;EACA;;;EACA,IAAME,OAAO,GAAG9B,WAAW,CAAC,YAAM;IAChC;IACAiB,WAAW,CAACG,OAAZ,CAAoBI,KAApB;EACD,CAH0B,EAGxB,EAHwB,CAA3B;EAKA,oBACE,oBAAC,UAAD;IACE,OAAO,EAAEd,OADX;IAEE,IAAI,EAAEH,IAFR;IAGE,YAAY,EAAEC,YAHhB;IAIE,gBAAgB,EAAEC,gBAJpB;IAKE,KAAK,EAAEE,KALT;IAME,OAAO,EAAEmB,OANX;IAOE,MAAM,EAAE,gBAACC,KAAD,EAAW;MACjB;MACA;MACA;MACA;MACA;MACA;MACA,OAAOT,oBAAoB,CAACS,KAAK,CAACC,KAAN,IAAeD,KAAhB,CAA3B;IACD;EAfH,EADF;AAkBD,CAlID;;AAoIA3B,kBAAkB,gBAAGN,KAAK,CAACmC,UAAN,CAAiB7B,kBAAjB,CAArB;AAEAA,kBAAkB,CAAC8B,SAAnB,GAA+B;EAC7B5B,SAAS,EAAEH,SAAS,CAACgC,WAAV,CAAsBC,UADJ;EAE7B7B,IAAI,EAAEJ,SAAS,CAACkC,MAAV,CAAiBD,UAFM;EAG7B5B,YAAY,EAAEL,SAAS,CAACkC,MAHK;EAI7B;EACA;EACA;EACA5B,gBAAgB,EAAEN,SAAS,CAACmC,IAPC;EAQ7B;EACA;EACA5B,OAAO,EAAEP,SAAS,CAACoC,MAVU;EAW7B5B,KAAK,EAAER,SAAS,CAACoC,MAXY;EAY7B1B,QAAQ,EAAEV,SAAS,CAACqC,IAZS;EAa7BzB,MAAM,EAAEZ,SAAS,CAACqC;AAbW,CAA/B;AAgBA,eAAepC,kBAAf"}