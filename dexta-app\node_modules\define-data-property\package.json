{"name": "define-data-property", "version": "1.1.1", "description": "Define a data property on an object. Will fall back to assignment in an engine without descriptors.", "main": "index.js", "exports": {".": [{"types": "./index.d.ts", "default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "sideEffects": false, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run emit-types", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "tsc": "tsc -p .", "preemit-types": "rm -f *.ts *.ts.map test/*.ts test/*.ts.map", "emit-types": "npm run tsc -- --noEmit false --emitDeclarationOnly", "postemit-types": "rm test/*.ts test/*.ts.map", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/define-data-property.git"}, "keywords": ["define", "data", "property", "object", "accessor", "javascript", "ecmascript", "enumerable", "configurable", "writable"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/define-data-property/issues"}, "homepage": "https://github.com/ljharb/define-data-property#readme", "dependencies": {"get-intrinsic": "^1.2.1", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.0"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/es-value-fixtures": "^1.4.1", "@types/for-each": "^0.3.1", "@types/get-intrinsic": "^1.2.0", "@types/gopd": "^1.0.1", "@types/has": "^1.0.0", "@types/has-property-descriptors": "^1.0.1", "@types/object-inspect": "^1.8.2", "@types/object.getownpropertydescriptors": "^2.1.2", "@types/tape": "^5.6.1", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has": "^1.0.3", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.3", "object.getownpropertydescriptors": "^2.1.7", "reflect.ownkeys": "^1.1.4", "safe-publish-latest": "^2.0.0", "tape": "^5.7.1", "typescript": "^5.3.0-dev.20231012"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "!*.ts", "!*.ts.map", "types/reflect.ownkeys"]}}