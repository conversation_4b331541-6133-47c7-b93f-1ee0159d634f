{"name": "bfj", "version": "7.0.2", "description": "Big-friendly JSON. Asynchronous streaming functions for large JSON data sets.", "homepage": "https://gitlab.com/philbooth/bfj", "bugs": "https://gitlab.com/philbooth/bfj/issues", "license": "MIT", "author": "<PERSON> (https://gitlab.com/philbooth)", "main": "./src", "keywords": ["json", "streamify", "stringify", "walk", "parse", "parser", "serialise", "serialize", "read", "write", "async", "asynchronous"], "repository": {"type": "git", "url": "https://gitlab.com/philbooth/bfj.git"}, "engines": {"node": ">= 8.0.0"}, "dependencies": {"bluebird": "^3.5.5", "check-types": "^11.1.1", "hoopy": "^0.1.4", "tryer": "^1.0.1"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^6.0.1", "mocha": "^6.1.4", "please-release-me": "^2.1.2", "proxyquire": "^2.1.0", "request": "^2.88.0", "spooks": "^2.0.0"}, "scripts": {"lint": "eslint src", "test": "npm run unit && npm run integration", "unit": "mocha --ui tdd --reporter spec --recursive --colors --slow 120 test/unit", "integration": "mocha --ui tdd --reporter spec --colors test/integration", "perf": "wget -O test/mtg.json http://mtgjson.com/json/AllSets-x.json && node test/performance mtg"}}