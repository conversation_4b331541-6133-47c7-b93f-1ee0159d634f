{"version": 3, "file": "PhoneInputWithCountry.js", "names": ["React", "PropTypes", "ReactHookFormInput", "PhoneInputWithCountry_", "metadata", "metadataType", "createPhoneInput", "defaultMetadata", "PhoneInputWithCountry", "ref", "rest", "forwardRef", "propTypes"], "sources": ["../../source/react-hook-form/PhoneInputWithCountry.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport ReactHookFormInput from './ReactHookFormInput.js'\r\nimport PhoneInputWithCountry_ from '../PhoneInputWithCountryDefault.js'\r\n\r\nimport { metadata as metadataType } from '../PropTypes.js'\r\n\r\nexport function createPhoneInput(defaultMetadata) {\r\n  let PhoneInputWithCountry = ({\r\n    metadata = defaultMetadata,\r\n    ...rest\r\n  }, ref) => {\r\n    return (\r\n      <ReactHookFormInput\r\n        {...rest}\r\n        ref={ref}\r\n        metadata={metadata}\r\n        Component={PhoneInputWithCountry_}\r\n      />\r\n    )\r\n  }\r\n\r\n  PhoneInputWithCountry = React.forwardRef(PhoneInputWithCountry)\r\n\r\n  PhoneInputWithCountry.propTypes = {\r\n    metadata: metadataType\r\n  }\r\n\r\n  return PhoneInputWithCountry\r\n}\r\n\r\nexport default createPhoneInput()"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,SAAP,MAAsB,YAAtB;AAEA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,sBAAP,MAAmC,oCAAnC;AAEA,SAASC,QAAQ,IAAIC,YAArB,QAAyC,iBAAzC;AAEA,OAAO,SAASC,gBAAT,CAA0BC,eAA1B,EAA2C;EAChD,IAAIC,qBAAqB,GAAG,qCAGzBC,GAHyB,EAGjB;IAAA,yBAFTL,QAES;IAAA,IAFTA,QAES,8BAFEG,eAEF;IAAA,IADNG,IACM;;IACT,oBACE,oBAAC,kBAAD,eACMA,IADN;MAEE,GAAG,EAAED,GAFP;MAGE,QAAQ,EAAEL,QAHZ;MAIE,SAAS,EAAED;IAJb,GADF;EAQD,CAZD;;EAcAK,qBAAqB,gBAAGR,KAAK,CAACW,UAAN,CAAiBH,qBAAjB,CAAxB;EAEAA,qBAAqB,CAACI,SAAtB,GAAkC;IAChCR,QAAQ,EAAEC;EADsB,CAAlC;EAIA,OAAOG,qBAAP;AACD;AAED,eAAeF,gBAAgB,EAA/B"}