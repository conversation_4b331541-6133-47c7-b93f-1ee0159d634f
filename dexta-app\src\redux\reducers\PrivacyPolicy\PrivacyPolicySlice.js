import { createSlice } from "@reduxjs/toolkit";
const initialState = {
  AcceptPolicy: false,
};

export const PrivacyPolicySlice = createSlice({
  name: "policy",
  initialState,
  reducers: {
    setPolicyToTrue: (state, action) => {
      state.AcceptPolicy = true;
    },
    setPolicyToFalse: (state, action) => {
      state.AcceptPolicy = false;
    },
  },
});

export const { setPolicyToTrue, setPolicyToFalse } = PrivacyPolicySlice.actions;
export default PrivacyPolicySlice.reducer;
