{"version": 3, "sources": ["../../src/js/index.js"], "names": [], "mappings": ";;;;;;AAAA;;;;;;AAEA;;;;;;;;;AASA;;;;;;;;;;;;;;AAcA;;;;;;;AAOA;;;;;;;AAOA", "file": "index.js", "sourcesContent": ["import InputRange from './input-range/input-range';\n\n/**\n * @ignore\n * @typedef {Object} ClientRect\n * @property {number} height\n * @property {number} left\n * @property {number} top\n * @property {number} width\n */\n\n/**\n * @typedef {Object} InputRangeClassNames\n * @property {string} activeTrack\n * @property {string} disabledInputRange\n * @property {string} inputRange\n * @property {string} labelContainer\n * @property {string} maxLabel\n * @property {string} minLabel\n * @property {string} slider\n * @property {string} sliderContainer\n * @property {string} track\n * @property {string} valueLabel\n */\n\n/**\n * @typedef {Function} LabelFormatter\n * @param {number} value\n * @param {string} type\n * @return {string}\n */\n\n/**\n * @ignore\n * @typedef {Object} Point\n * @property {number} x\n * @property {number} y\n */\n\n/**\n * @typedef {Object} Range\n * @property {number} min - Min value\n * @property {number} max - Max value\n */\n\nexport default InputRange;\n"]}