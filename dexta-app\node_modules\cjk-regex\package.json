{"name": "cjk-regex", "type": "module", "version": "3.3.0", "description": "regular expression for matching CJK text", "keywords": ["cjk", "regular-expression"], "exports": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "repository": "https://github.com/ikatyang-collab/cjk-regex", "homepage": "https://github.com/ikatyang-collab/cjk-regex#readme", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ikatyang"}, "license": "MIT", "dependencies": {"regexp-util": "^2.0.1", "unicode-regex": "^4.1.0"}, "devDependencies": {"@vitest/coverage-v8": "3.0.8", "prettier": "3.5.3", "standard-version": "9.5.0", "typescript": "5.8.2", "vite": "6.2.1", "vitest": "3.0.8"}, "engines": {"node": ">=16"}, "files": ["/lib/**/*", "/CHANGELOG.md"], "scripts": {"lint": "prettier --check .", "fix": "prettier --write .", "test": "vitest", "build": "tsc -p ./tsconfig.build.json", "release": "standard-version"}}