{"name": "@jest/transform", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-transform"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/core": "^7.1.0", "@jest/types": "^27.5.1", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^1.4.0", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-util": "^27.5.1", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "source-map": "^0.6.1", "write-file-atomic": "^3.0.0"}, "devDependencies": {"@jest/test-utils": "^27.5.1", "@types/babel__core": "^7.1.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0", "@types/graceful-fs": "^4.1.2", "@types/micromatch": "^4.0.1", "@types/write-file-atomic": "^3.0.0", "dedent": "^0.7.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}