"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M14.14 12h-.06c.81 0 1.28-.91.82-1.57L9.82 3.17c-.4-.57-1.24-.57-1.64 0L3.1 10.43c-.46.66.01 1.57.82 1.57h-.06L.99 16.46c-.43.66.05 1.54.84 1.54H7v2c0 1.1.9 2 2 2s2-.9 2-2v-2h5.17c.79 0 1.27-.88.84-1.54L14.14 12z"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M23.01 16.46 20.14 12h-.06c.81 0 1.28-.91.82-1.57l-5.08-7.26c-.4-.57-1.24-.57-1.64 0l-1.57 2.24 3.11 4.44c.43.61.48 1.41.14 2.07-.08.16-.18.3-.3.43l2.29 3.57c.4.62.42 1.4.07 2.04-.01.02-.02.03-.03.04h4.28c.79 0 1.27-.88.84-1.54zM13 20c0 1.1.9 2 2 2s2-.9 2-2v-1h-4v1z"
}, "1")], 'ForestRounded');
exports.default = _default;