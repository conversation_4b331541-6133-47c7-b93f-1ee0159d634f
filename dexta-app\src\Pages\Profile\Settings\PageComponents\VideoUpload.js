import React, { useState, useRef } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";
import { useSelector } from "react-redux";
import VideoJS from "../../../../Components/VideoPlayer";
import UploadModal from "../../../../Components/UploadModal";
import { FaVideoSlash } from "react-icons/fa";
import { FaUpload } from "react-icons/fa";
import King from "../../../../Assets/preee.png";
import Premium from "../../../../Components/Modals/Premium";
import { useTranslation } from "react-i18next";

const VideoContainer = ({
  files,
  setFiles,
  companyUploadType,
  companyData,
  companyID,
  uploadModal,
  setUploadModal,
  setDeleteModal,
}) => {
  const { t } = useTranslation();
  const containerRef = useRef(null);
  const [premiumOpen, setPremiumOpen] = useState(false);
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const playerRef = React.useRef(null);

  const videoJsOptions = {
    autoplay: false,
    controls: true,
    responsive: true,
    fluid: true,
    sources: [
      {
        src: companyData?.companyVideo,
        type: "video/mp4",
      },
    ],
  };

  const handlePlayerReady = (player) => {
    playerRef.current = player;
    player.on("waiting", () => {
      videojs.log("player is waiting");
    });
    player.on("dispose", () => {
      videojs.log("player will dispose");
    });
  };

  const handleUploadClick = () => {
    if (user_package_check === "free") {
      setPremiumOpen(true);
    } else {
      setUploadModal(true);
    }
  };

  return (
    <>
      <div className="mb-5">
        <div className="flex items-center mb-2">
          <h2
            className="text-lg font-semibold"
            style={{ fontFamily: "Archia Semibold" }}
          >
            {t("company_introduction_video")}
          </h2>
        </div>
        <p
          className="text-gray-600 mb-4 text-sm"
          style={{ fontFamily: "Silka" }}
        >
          {t("video_description")}
        </p>

        {companyData?.companyVideo === null ? (
          <div className="max-w-2xl mx-auto">
            <div
              className="border-2 border-dashed border-[#D3D5D8] bg-gray-50 rounded-lg p-6 cursor-pointer hover:border-coalColor transition-colors duration-200"
              onClick={handleUploadClick}
            >
              <div className="flex flex-col md:flex-row items-center md:items-start py-4">
                <div className="flex-shrink-0 md:mr-6 mb-4 md:mb-0">
                  <div className="bg-gray-100 p-4 rounded-full">
                    <FaVideoSlash className="w-10 h-10 text-gray-400" />
                  </div>
                </div>
                <div className="flex-grow text-center md:text-left">
                  <h3
                    className="text-lg font-medium mb-1"
                    style={{ fontFamily: "Silka" }}
                  >
                    {t("no_introduction_video")}
                  </h3>
                  <p
                    className="text-gray-500 mb-4"
                    style={{ fontFamily: "Silka" }}
                  >
                    {t("upload_video_description")}
                  </p>
                  <div className="flex flex-col md:flex-row items-center">
                    <button
                      className="flex items-center px-4 py-2 bg-coalColor text-white rounded-md hover:bg-primaryGreen hover:text-black transition-colors duration-200"
                      style={{ fontFamily: "Silka" }}
                    >
                      <FaUpload className="mr-2" />
                      {t("upload_video")}
                    </button>

                    {user_package_check === "free" && (
                      <div className="mt-3 md:mt-0 md:ml-4 flex items-center">
                        <img
                          src={King}
                          className="w-5 h-5 mr-2"
                          alt="Premium feature"
                        />
                        <span className="text-sm text-gray-500">
                          {t("premium_feature")}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white border border-[#D3D5D8] rounded-lg overflow-hidden shadow-sm">
            <div className="max-w-2xl mx-auto p-4">
              <div ref={containerRef} className="video-container-wrapper">
                <VideoJS options={videoJsOptions} onReady={handlePlayerReady} />
              </div>
            </div>

            <div className="p-4 bg-gray-50 border-t border-[#D3D5D8]">
              <div className="flex flex-col md:flex-row justify-between items-center max-w-2xl mx-auto">
                <div className="mb-3 md:mb-0 md:mr-4">
                  <h3 className="font-medium" style={{ fontFamily: "Silka" }}>
                    {t("change_your_video")}
                  </h3>
                  <p
                    className="text-sm text-gray-500"
                    style={{ fontFamily: "Silka" }}
                  >
                    {t("video_shown_description")}
                  </p>
                </div>
                <div className="flex space-x-2 flex-shrink-0">
                  <button
                    onClick={handleUploadClick}
                    className="whitespace-nowrap min-w-[100px] px-3 py-1.5 bg-coalColor text-white text-sm rounded hover:bg-primaryGreen hover:text-black transition-colors duration-200"
                    style={{ fontFamily: "Silka" }}
                  >
                    {t("change")}
                  </button>
                  <button
                    onClick={() => setDeleteModal(true)}
                    className="whitespace-nowrap min-w-[100px] px-3 py-1.5 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-100 transition-colors duration-200"
                    style={{ fontFamily: "Silka" }}
                  >
                    {t("delete")}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <Premium premiumOpen={premiumOpen} setPremiumOpen={setPremiumOpen} />
      <UploadModal
        files={files}
        setFiles={setFiles}
        companyID={companyID}
        uploadModal={uploadModal}
        setUploadModal={setUploadModal}
      />
    </>
  );
};

export default VideoContainer;
