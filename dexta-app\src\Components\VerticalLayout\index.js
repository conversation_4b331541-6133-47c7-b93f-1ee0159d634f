import React from "react";
import withRouter from "../Common/withRouter";
import NavbarUser from "../Navbar/NavbarUser";

const Layout = (props) => {
  const { router } = props;
  const isCreateTestRoute = router.location.pathname === "/create-test";

  return (
    <React.Fragment>
      <div id="preloader">
        <NavbarUser />
        <div
          className={`main-content ${
            !isCreateTestRoute ? "overflow-x-hidden" : ""
          }`}
        >
          {props.children}
        </div>
      </div>
    </React.Fragment>
  );
};

Layout.propTypes = {};

export default withRouter(Layout);
