{"name": "geoip-country", "version": "5.0.202505202342", "description": "Quick country lookup from IP address", "keywords": ["geo", "geoip", "ip", "ipv4", "ipv6", "geolookup", "maxmind", "geolite", "country"], "homepage": "https://github.com/sapics/geoip-country", "author": "sapics (https://github.com/sapics)", "files": ["lib/", "data/", "scripts/", "LICENSE", "EULA"], "main": "lib/geoip.js", "repository": {"type": "git", "url": "git://github.com/sapics/geoip-country.git"}, "engines": {"node": ">=10.20.0"}, "scripts": {"test": "nodeunit --reporter=minimal test/tests.js", "updatedb": "node scripts/updatedb.js", "updatedb-debug": "node scripts/updatedb.js debug"}, "dependencies": {"async": "^2.6.4", "countries-list": "^3.1.1", "ip-address": "^6.4.0", "yauzl": "^2.10.0"}, "config": {"update": true}, "devDependencies": {"dayjs": "^1.11.13", "geoip-lite": "^1.4.10", "nodeunit-x": "^0.16.0"}, "license": "MaxMind GeoLite2 License"}