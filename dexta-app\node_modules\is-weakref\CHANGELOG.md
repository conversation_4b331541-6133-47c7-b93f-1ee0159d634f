# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/is-weakref/compare/v1.0.1...v1.0.2) - 2021-12-10

### Commits

- [actions] reuse common workflows [`2375b1f`](https://github.com/inspect-js/is-weakref/commit/2375b1f9798b08c7af98481bbb38b4105835dacf)
- [meta] do not publish workflow files [`4c1be42`](https://github.com/inspect-js/is-weakref/commit/4c1be423afacabf2f3aa9e8bf02f668bdeaf3a20)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`7ec78ce`](https://github.com/inspect-js/is-weakref/commit/7ec78ce58c7553469eee97ae82fe147dfccde611)
- [readme] update URLs [`6306f09`](https://github.com/inspect-js/is-weakref/commit/6306f09a7df388150fb1d0b855b6f9e60165a457)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`7a1601e`](https://github.com/inspect-js/is-weakref/commit/7a1601e93ae50a791751a96d33073f5e65f3d3c9)
- [readme] add actions and codecov badges [`67ecd14`](https://github.com/inspect-js/is-weakref/commit/67ecd14b8b0192456932d1d54838accbf90ff5c0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape` [`1a5013b`](https://github.com/inspect-js/is-weakref/commit/1a5013bddcb9edc23025571810f9a2eebda53683)
- [actions] update codecov uploader [`b57b037`](https://github.com/inspect-js/is-weakref/commit/b57b037a547f3ecfa3d3f079a8015ec005c7181b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`da49017`](https://github.com/inspect-js/is-weakref/commit/da49017800d628c9bcd2f094d49783d6ee649c50)
- [meta] simplify "exports" [`9b88835`](https://github.com/inspect-js/is-weakref/commit/9b8883585506c135a3fcb9f55d0944a13b4eb3e6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`c7e77f4`](https://github.com/inspect-js/is-weakref/commit/c7e77f495308f3385adfaa1f4ac78a2632e0bcde)
- [Dev Deps] update `eslint` [`417b29e`](https://github.com/inspect-js/is-weakref/commit/417b29e7ceacebe24aef15422544443f4b59e181)
- [meta] add `safe-publish-latest`; use `prepublishOnly` script for npm 7+ [`b1b99f4`](https://github.com/inspect-js/is-weakref/commit/b1b99f45e0977d10f8472e9272e48a696145c2b1)
- [Deps] update `call-bind` [`aea342e`](https://github.com/inspect-js/is-weakref/commit/aea342e9e301deeb938e62b92a37cf991c5f7dbc)
- [actions] update workflows [`786c2d3`](https://github.com/inspect-js/is-weakref/commit/786c2d3dd4486acec09786220d3dd9fd48e70e93)

## [v1.0.1](https://github.com/inspect-js/is-weakref/compare/v1.0.0...v1.0.1) - 2020-12-04

### Commits

- [Tests] migrate tests to Github Actions [`05b4faa`](https://github.com/inspect-js/is-weakref/commit/05b4faa167c67f42c792e35c07adcb6b87e7dea0)
- [Tests] run `nyc` on all tests [`8df2e4b`](https://github.com/inspect-js/is-weakref/commit/8df2e4bd66bb6b7d55f389f28e6bb167fe1deb5a)
- [actions] add "Allow Edits" workflow [`4a716b8`](https://github.com/inspect-js/is-weakref/commit/4a716b8fcc025fe889a0f09ccaee7a9f748b1c66)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect` [`be23cf3`](https://github.com/inspect-js/is-weakref/commit/be23cf305f46db8b1c8a26d1c74b096fdba00056)
- [Refactor] use `call-bind` instead of `es-abstract` [`a933a96`](https://github.com/inspect-js/is-weakref/commit/a933a9643ddf7cddfd9f9f3cf44d675cc4c86ce5)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`4473ed2`](https://github.com/inspect-js/is-weakref/commit/4473ed2e73fed47cd2fa42b8d9cac17e941d2c08)
- [readme] remove travis badge [`bd3bfcd`](https://github.com/inspect-js/is-weakref/commit/bd3bfcd2c187099d2215232a7621fb960e1e2807)

## v1.0.0 - 2020-08-01

### Commits

- Initial commit [`dd86394`](https://github.com/inspect-js/is-weakref/commit/dd86394d7da000724c6e17c79077879c381e9ea3)
- readme [`f4defca`](https://github.com/inspect-js/is-weakref/commit/f4defcac48d1d99b019b596ab26bd868de1adfe9)
- Tests [`13d8139`](https://github.com/inspect-js/is-weakref/commit/13d8139dedf424239daf357261c39d3f8c33d662)
- npm init [`55a2bb7`](https://github.com/inspect-js/is-weakref/commit/55a2bb7c53b893396a51da969e352702cafe9a0e)
- Implementation [`1ec84e3`](https://github.com/inspect-js/is-weakref/commit/1ec84e36de4315d44c8da540faa27836832bb0f3)
- [meta] add auto-changelog [`ab9ce44`](https://github.com/inspect-js/is-weakref/commit/ab9ce44be717312c5221bf3d2f3f6d2dd8c6ac88)
- [actions] add automatic rebasing / merge commit blocking [`3d3f4d5`](https://github.com/inspect-js/is-weakref/commit/3d3f4d54bed6e455b2a0d0f20c87d454bf78af26)
- [meta] add "funding"; create `FUNDING.yml` [`f35ef3d`](https://github.com/inspect-js/is-weakref/commit/f35ef3de16eb06447acf3c39bdc164ba0e7bdf45)
- [Tests] add `npm run lint` [`af2123d`](https://github.com/inspect-js/is-weakref/commit/af2123d4754c14f7befa66ba01e1d72858723651)
- [Tests] use shared travis-ci configs [`042b4de`](https://github.com/inspect-js/is-weakref/commit/042b4dec08d882ae9137f4ad05ae24a1457da0f8)
- Only apps should have lockfiles [`fcae604`](https://github.com/inspect-js/is-weakref/commit/fcae604cb1422faae9311dd4219032895c0a9a2e)
