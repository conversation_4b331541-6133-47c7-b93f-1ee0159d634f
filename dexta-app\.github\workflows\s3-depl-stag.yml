name: s3-depl

on:
  push:
    branches: [ staging ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'

      - name: Install Yarn
        run: npm install --global yarn

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Clean and Install Dependencies
        env:
          CI: false
        run: |
          yarn install
          yarn add ajv
          yarn build

      - name: Build React App
        env:
          TSC_COMPILE_ON_ERROR: true
          REACT_APP_STATIC_SITE: ${{ secrets.REACT_APP_STATIC_SITE_STAGING }}
          REACT_APP_Server: https://staged.dexta.io/api
          REACT_APP_STRIPE_PUBLISHABLE_KEY: ${{ secrets.REACT_APP_STRIPE_PUBLISHABLE_KEY_DEV }}
          REACT_APP_RECAPTCHA_SITE_KEY: ${{ secrets.REACT_APP_RECAPTCHA_SITE_KEY }}

        run: |
          yarn build

      - name: Deploy app build to S3 bucket
        run: aws s3 sync ./build/ s3://dexta-app-staging --delete
