import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { IoMdClose } from "react-icons/io";
import { Currencies, type, LoadingMap } from "./data";
import { useQuery } from "@tanstack/react-query";
import { getPackageDetailsUser } from "../hooks/getPackageDetails";
import ReactHtmlParser from "react-html-parser";
import styles from "./styling.module.css";
import styles2 from "./styling2.module.css";
import styles3 from "./styling3.module.css";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "./modals.css";
import { getwebfeatures } from "../hooks/getwebfeatures";
import React from "react";
import { GiCheckMark } from "react-icons/gi";
import { upgradePackage } from "../../Pages/Profile/Settings/hooks/upgradePackage";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { getUserPackage } from "../../Pages/Profile/Settings/hooks/getUserPackage";
import Loader from "react-loader-spinner";
import { useSelector, useDispatch } from "react-redux";
import { createSubscription } from "../../Pages/Profile/Settings/hooks/createSubscription";
import PayPlan from "./Payforplan";
import { setPlanDetails } from "../../redux/reducers/PlanDetails/PlanDetailsSlice";
import { setPackageData } from "../../redux/reducers/packageData/packageDataSlice";
import { toast, ToastContainer, Zoom } from "react-toastify";
import star from "../../Dexta_assets/star.png";
import GeneralModal from "./GeneralModal";
import { getButtonVariant, isButtonDisabled } from "./ModalStyles/PlansStyling";
import { getAddons } from "../hooks/getaddons";
import { addonsUpgrade } from "../hooks/addonsUpgrade";
import { getUserData } from "../../Pages/Profile/MyAssessments/Assessments-main/hooks/getUserData";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";
import axios from "axios";
import http from "../../http";
import * as moment from "moment";
import { getMyCoupon } from "../hooks/getMyCoupon";
import { getPackage } from "../hooks/getPackage";
import NoContentModal from "./NoContent";
import { useTranslation } from "react-i18next";

export default function Plans({ plansModal, setPlansModal }) {
  const { t } = useTranslation();
  const cancelButtonRef = useRef(null);
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const [selecteditem, setSelectedItem] = useState("gbp");
  const [selectedType, setSelectedType] = useState("month");
  const userID = localStorage.getItem("CP-USER-ID");
  const [selectedSign, setSelectedSign] = useState("£");
  const queryClient = useQueryClient();
  const [loadingPackage, setLoadingPackage] = useState(0);
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const is_free_user = useSelector((state) => state.examdone.setFree);
  const navigate = useNavigate();
  const [paymentOpen, setPaymentOpen] = useState(false);
  const dispatch = useDispatch();
  const [generalModal, setGeneralModal] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState(false);
  const [heading, setHeading] = useState("");
  const [shouldUpdate, setShouldUpdate] = useState("");
  const [description, setdescription] = useState("");
  const [closeClicked, setCloseClicked] = useState(false);
  const [packageDetails, setPackageDetails] = useState({
    id: null,
    selectedItem: null,
    selectedType: null,
    stripeProductID: null,
    stripePriceID: null,
    stripeYearlyPriceID: null,
  });
  const [freeCheck, setFreeCheck] = useState("");
  const [freePackage, setFreePackage] = useState(false);
  const [noButtons, setNoButtons] = useState("");
  const [onlyAddonPurchase, setOnlyAddonPurchase] = useState(false);
  const [purchaseAddon, setPurchaseAddon] = useState(false);
  const [immediateUpgrade, setImmediateUpgrade] = useState(false);
  const [whatdataaddon, setwhatdataaddon] = useState(false);
  const [packageCode, setPackageCode] = useState("");
  const [noContentModal, setNoContentNodal] = useState(false);
  const [discountedPackages, setDiscountedPackages] = useState([]);
  //#region Fetching packages data from api
  const { data, isLoading, error } = useQuery({
    queryKey: ["packages", selecteditem, selectedType],
    queryFn: () => getPackageDetailsUser(selecteditem, selectedType),
  });
  //#endregion
  const [dataUser, setUserData] = useState(null);

  //#region Fetching user data
  const fetchUser = async () => {
    try {
      const data = await http.get(`/users/${userID}`);
      setUserData(data?.data);
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };
  useEffect(() => {
    fetchUser();
  }, [generalModal]);
  //#endregion

  useEffect(() => {
    const fetchData = async () => {
      if (
        parsed?.dsc &&
        parsed?.package_id &&
        parsed?.currency &&
        parsed?.interval &&
        data?.data
      ) {
        const dsc_obj = {
          dsc: parsed?.dsc,
          package_id: parsed?.package_id,
          currency: parsed?.currency,
          interval: parsed?.interval,
        };

        localStorage.setItem("DSC_OBJ", JSON.stringify(dsc_obj));

        const token = localStorage.getItem("CP-USER-TOKEN");
        const userId = localStorage.getItem("CP-USER-ID");

        if (!token || !userId) {
          // navigate to login page
          return;
        }

        try {
          // if API fails, navigate to login page again,
          // or maybe there are other apis would fail definitely and
          // lead user to login page

          const packageX = data?.data.find(
            (obj) => obj.id == parsed.package_id
          );

          if (packageX) {
            setPlansModal(true);
            setSelectedType(parsed.interval);

            const selectedCurrency = Currencies.find(
              (curr) => curr.value === parsed.currency
            );

            if (selectedCurrency) {
              setSelectedItem(selectedCurrency.value);
              setSelectedSign(selectedCurrency.sign);
            }

            try {
              const coupon = await http.get(`/coupons/${parsed.dsc}`);

              if (coupon) {
                try {
                  const validateCoupon = await http.post(`/coupons/validate`, {
                    code: coupon?.data?.code,
                    userId: dataUser.id,
                  });

                  if (!validateCoupon) {
                    localStorage.removeItem("DSC_OBJ");
                    navigate(location.pathname, { replace: true });
                    toast.error(t("plans.coupon_validation_failed"));
                    return;
                  }

                  // if everything is good we will call these methods
                  if (
                    getText(packageX) === t("plans.free_forever") &&
                    dataUser?.userAddOns?.length === 0
                  ) {
                    setFreeCheck("free");
                    setOnlyAddonPurchase(true);
                  } else {
                    setFreeCheck("");
                  }

                  // attaching coupon for card detail component
                  packageX.coupon = coupon.data;
                  handlePlanSelection(packageX);
                } catch (validationError) {
                  if (validationError.response) {
                    const { status, data } = validationError.response;
                    navigate(location.pathname, { replace: true });
                    console.log(
                      `Coupon validation error: ${status}`,
                      data?.message
                    );
                    toast.error(data?.message[0]);
                  } else {
                    navigate(location.pathname, { replace: true });
                    console.error(
                      "Unexpected validation error:",
                      validationError
                    );
                    toast.error(t("plans.unexpected_validation_error"));
                  }
                  localStorage.removeItem("DSC_OBJ");
                  return;
                }
              }
            } catch (fetchError) {
              if (fetchError.response) {
                const { status, data } = fetchError.response;
                console.error(`Coupon fetch error: ${status}`, data);

                if (status === 404) {
                  navigate(location.pathname, { replace: true });
                  toast.error(t("plans.coupon_not_found"));
                } else {
                  navigate(location.pathname, { replace: true });
                  toast.error(
                    data?.message || t("plans.failed_to_fetch_coupon")
                  );
                }
              } else {
                navigate(location.pathname, { replace: true });
                console.error("Unexpected fetch error:", fetchError);
                toast.error(t("plans.unexpected_coupon_error"));
              }

              localStorage.removeItem("DSC_OBJ");
            }
          }
        } catch (error) {
          console.error("user API call failed:", error);
        }
      }
    };

    fetchData();
  }, [
    parsed?.dsc,
    parsed?.package_id,
    parsed?.currency,
    parsed?.interval,
    data,
  ]);

  //#region Fetching packages data from api
  const {
    data: featureData,
    isLoading: featureLoading,
    error: featureError,
  } = useQuery({ queryKey: ["web-features"], queryFn: () => getwebfeatures() });
  //#endregion

  //#region Upgrade Subscription Status
  const UpdgradePackage = (details) => {
    const {
      id,
      selectedItem,
      selectedType,
      stripeProductID,
      stripePriceID,
      stripeYearlyPriceID,
    } = details;
    const postData = JSON.stringify({
      subscription: subscriptionData?.id,
      package: id,
      stripeProductID: stripeProductID,
      stripePriceID:
        selectedType === "year" ? stripeYearlyPriceID : stripePriceID,
      currency: selectedItem,
      interval: selectedType,
      description: "Downgrading Subscription",
    });
    try {
      packageMutate(postData);
    } catch (err) {
      console.error(err.message);
    }
  };

  const { mutate: packageMutate, isLoading: upgradeLoading } = useMutation(
    upgradePackage,
    {
      onSuccess: (res) => {
        queryClient.invalidateQueries("/subscriptions/upgrade");
        if (freePackage) {
          dispatch(setPackageData("free"));
        } else {
          dispatch(setPackageData("pro"));
        }
        if (purchaseAddon) {
          HandleAddons();
        } else {
          setGeneralModal(false);
          toast.success(t("plans.downgrade_success"), {
            toastId: "copy-success",
            style: { width: "450px" },
          });
        }

        // if (closeClicked) {
        //   if (freePackage) {
        //     dispatch(setPackageData("free"));
        //   } else {
        //     dispatch(setPackageData("pro"));
        //   }
        //   setGeneralModal(false);
        //   toast.success("You have successfully changed your package", {
        //     toastId: "copy-success",
        //     style: { width: "450px" },
        //   });
        //   setLoadingPackage(0);
        //   setFreePackage(false);
        // } else {
        //   if (freePackage) {
        //     setGeneralModal(false);
        //     dispatch(setPackageData("free"));
        //     HandleAddons();
        //   } else {
        //     setGeneralModal(false);
        //     dispatch(setPackageData("pro"));
        //     toast.success("You have successfully changed your package", {
        //       toastId: "copy-success",
        //       style: { width: "450px" },
        //     });
        //     setLoadingPackage(0);
        //     setFreePackage(false);
        //   }
        // }
      },
    }
  );

  const { mutate: subscriptionMutate, isLoading: subcriptionLoading } =
    useMutation(createSubscription, {
      onSuccess: () => {
        queryClient.invalidateQueries("/subscriptions");
      },
    });

  //#endregion

  //#region Fetch user Subscription Details
  const {
    data: subscriptionData,
    isLoading: subscriptionLoading,
    refetch,
  } = useQuery(["/subscriptions/subscriptionsByUserId", userID], () =>
    getUserPackage(userID)
  );

  // / When subscriptionData comes, auto set currency
  useEffect(() => {
    if (subscriptionData?.currency) {
      setSelectedItem(subscriptionData.currency);
      const currencyData = Currencies.find(
        (i) => i.value === subscriptionData.currency
      );
      setSelectedSign(currencyData?.sign);
    }
  }, [subscriptionData?.currency, Currencies]);

  useEffect(() => {
    refetch();
  }, [paymentOpen, refetch]);
  useMemo(() => {
    if (subscriptionData?.interval) {
      setSelectedType(subscriptionData?.interval);
    }
  }, [subscriptionLoading]);
  //#endregion

  //#region check upgrade or downgrade
  const getText = (i) => {
    const userSubscription = subscriptionData;
    const userSubscriptionPackage = subscriptionData?.package;
    if (i.id == 1 && i.id == userSubscriptionPackage?.id) {
      return i.code === "free" ? i.name : undefined;
    } else if (i.id == userSubscriptionPackage?.id) {
      if (userSubscription?.interval == selectedType) {
        return i?.name;
      }
      return selectedType === "month" ? t("plans.downgrade") : "";
    } else {
      if (i?.code == "free") {
        return t("plans.downgrade_with_data_addon");
      }
      if (i?.code == "pro" && userSubscriptionPackage?.code == "free") {
        return t("plans.upgrade");
      }
      if (i?.code == "pro" && userSubscriptionPackage?.code == "Enterprise") {
        return t("plans.downgrade");
      }
      if (i?.code == "Enterprise" && userSubscriptionPackage?.code != i?.code) {
        return t("plans.upgrade");
      }
    }
  };
  //#endregion

  //#region decide which modal to open
  const handlePlanSelection = (i) => {
    const planDetails = {
      name: i.name,
      id: i.id,
      selectedType,
      productID: i.stripeProductID,
      priceID: i.stripePriceID,
      yearlyID: i.stripeYearlyPriceID,
      selectedSign,
      selectedAmount: i.prices[0]?.price,
      selecteditem,
      discountValue: i?.coupon?.discountValue,
      couponCode: i?.coupon?.code,
      code: i?.code,
    };

    dispatch(setPlanDetails(planDetails));
    setPaymentOpen(true);

    if (user_package_check !== "free") {
      setShouldUpdate(true);
    }
    if (user_package_check === "free" && dataUser?.userAddOns?.length === 0) {
      setShouldUpdate(true);
    }
  };
  //#endregion

  //#region Button content
  const renderButtonContent = (i) => {
    if (
      (upgradeLoading || subscriptionLoading || addonsmutateLoading) &&
      loadingPackage === i.id
    ) {
      const actionText =
        getText(i) === t("plans.downgrade")
          ? t("plans.downgrading")
          : t("plans.upgrading");
      return (
        <span className="flex items-center justify-center">
          <Loader type="Oval" color="white" height={20} width={20} />
          <span className="ml-2">{actionText}</span>
        </span>
      );
    }
    // If the button would show 'Free forever', show 'Explore' instead
    if (
      getText(i) === "Free forever" ||
      getText(i) === t("plans.free_forever")
    ) {
      return t("plans.explore");
    }
    return getText(i);
  };

  console.log(
    user_package_check === "free",
    is_free_user,
    !subscriptionData?.downgradeDetails?.addOnId,
    "Sharam kro"
  );
  const getButtonAriaLabel = (i) => {
    return `${getText(i)} plan ${
      isButtonDisabled(i, subscriptionData) ? t("plans.current_plan_aria") : ""
    }`;
  };
  //#endregion

  //#region Handle Downgrade Modal
  const handleDowngradeModal = (packageName, btnName) => {
    if (packageName === t("plans.free_forever")) {
      setFreePackage(true);
    }
    setGeneralModal(true);
    setHeading(t("plans.confirm_package_downgrade"));
    setdescription(
      packageName === t("plans.starter") ? (
        t("plans.downgrade_confirmation")
      ) : (
        <>
          {btnName === t("plans.downgrade_with_data_addon") ? (
            <p>
              {t("plans.downgrade_with_addon_confirmation", {
                sign: selectedSign,
                price:
                  dataaddons?.data[0]?.prices?.[selecteditem.toUpperCase()],
              })}
            </p>
          ) : (
            <p>{t("plans.downgrade_without_addon_confirmation")}</p>
          )}
        </>
      )
    );
  };
  //#endregion

  //#region Fetching addons data
  const {
    data: dataaddons,
    isLoading: addonsloading,
    error: addonserror,
  } = useQuery({
    queryKey: ["addons"],
    queryFn: () => getAddons(),
  });
  //#endregion

  //#region Purchasing addons
  const HandleAddons = () => {
    let data = {
      addOnId: dataaddons?.data[0]?.id,
      addOnPriceIds: dataaddons?.data[0]?.stripePriceId,
      description: "Monthly premium reports",
      currency: packageDetails?.selectedItem,
      interval: "monthly",
      coupon: "DISCOUNT2024",
    };
    try {
      addonsMutate(data);
    } catch (err) {
      console.log(err.message);
    }
  };

  const { mutate: addonsMutate, isLoading: addonsmutateLoading } = useMutation(
    addonsUpgrade,
    {
      onSuccess: (res) => {
        queryClient.invalidateQueries("/subscriptions/addon");
        setGeneralModal(false);
        toast.success(t("plans.purchase_success"), {
          toastId: "copy-success",
          style: { width: "450px" },
        });
        setLoadingPackage(0);
        setFreePackage(false);
        setPurchaseAddon(false);
        setImmediateUpgrade(true);
        fetchUser();
        // refetchUser();
      },
    }
  );
  //#endregion

  const [myCouponcode, setMyCouponCode] = useState("");
  const [myCouponPrice, setMyCouponPrice] = useState(0);
  //#region Fetching addons data
  const {
    data: mycoupon,
    isLoading: couponLoading,
    error: couponError,
    refetch: refetchCoupon,
  } = useQuery({
    queryKey: ["/coupons/my-latest"],
    queryFn: () => getMyCoupon(),
    onSuccess: (data) => {
      if (data) {
        setMyCouponCode(data.code);
        setMyCouponPrice(data.discountValue);

        // Save package codes from successful API response to discountedPackages array
        if (data.packages && Array.isArray(data.packages)) {
          const packageCodes = data.packages.map((pkg) => pkg.code);
          setDiscountedPackages(packageCodes);
        }
      }
    },
  });
  //#endregion

  //#region Fetching package data
  const {
    data: packageData,
    isLoading: packageLoading,
    error: packageError,
  } = useQuery({
    queryKey: subscriptionData?.downgradeDetails?.packageId
      ? ["/packages", subscriptionData.downgradeDetails.packageId]
      : null,
    queryFn: () => getPackage(subscriptionData?.downgradeDetails?.packageId),
    onSuccess: (data) => {
      if (subscriptionData?.downgradeDetails.packageId === data?.id) {
        setPackageCode(data?.code);
      }
    },
    enabled: !!subscriptionData?.downgradeDetails?.packageId, // Only run the query if packageId exists
  });

  const shouldDisplayDowngradeMessage = (i, packageCode) => {
    if (i?.code === packageCode) {
      return true;
    }
  };

  const shouldDisplayDowngradeButton = () => {
    if (dataUser?.userAddOns?.length === 0) {
      if (!immediateUpgrade) {
        return true;
      } else {
        return false;
      }
    }
    return true;
  };

  console.log(
    "results",
    dataUser?.userAddOns?.length,
    shouldDisplayDowngradeButton(),
    dataUser
  );
  //#endregion

  const handleOpenEmailInboxButtonClick = () => {
    window.open("https://dexta.io/contact", "_blank");
  };

  // document.title = "Plans | Dexta";

  return (
    <Transition.Root show={plansModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-40"
        initialFocus={cancelButtonRef}
        onClose={setPlansModal}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" />
        </Transition.Child>
        <ToastContainer
          position="top-center"
          transition={Zoom}
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={true}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
        <PayPlan
          paymentOpen={paymentOpen}
          setPaymentOpen={setPaymentOpen}
          refetch={refetch}
          shouldUpdate={shouldUpdate}
          setShouldUpdate={setShouldUpdate}
          subscriptionID={shouldUpdate ? subscriptionData?.id : null}
          addOnId={dataaddons?.data[0]?.id}
          addOnPriceIds={dataaddons?.data[0]?.stripePriceId}
          currency={packageDetails?.selectedItem}
          freeCheck={freeCheck}
          onlyAddonPurchase={onlyAddonPurchase}
          setOnlyAddonPurchase={setOnlyAddonPurchase}
          addonPrice={dataaddons?.data[0]?.prices?.[selecteditem.toUpperCase()]}
          mycouponprice={myCouponPrice}
          mycouponcode={myCouponcode}
          setMyCouponCode={setMyCouponCode}
          setMyCouponPrice={setMyCouponPrice}
          refetchCoupon={refetchCoupon}
          setUserData={setUserData}
          setImmediateUpgrade={setImmediateUpgrade}
          discountedPackages={discountedPackages}
        />
        <GeneralModal
          generalModal={generalModal}
          setGeneralModal={setGeneralModal}
          noButtons={noButtons}
          setNoButtons={setNoButtons}
          heading={(() => {
            if (!confirmationModal) {
              return heading;
            }
            if (confirmationModal && whatdataaddon) {
              return t("plans.avoid_losing_data_access");
            }
            return t("plans.premium_features_not_available");
          })()}
          description={(() => {
            if (!confirmationModal) {
              return description;
            }

            if (confirmationModal && whatdataaddon) {
              return (
                <div className="flex flex-col">
                  <p>{t("plans.data_addon_description")}</p>
                </div>
              );
            }

            if (freePackage) {
              return (
                <>
                  {t("plans.downgrade_explore_warning", {
                    date: moment(subscriptionData.commitmentExpiry)
                      .utcOffset(0)
                      .format("MMMM Do, YYYY [at] h:mm A"),
                  })}
                </>
              );
            }
            return (
              <div className="flex flex-col">
                <p>
                  {t("plans.downgrade_starter_warning", {
                    date: moment(subscriptionData.commitmentExpiry)
                      .utcOffset(0)
                      .format("MMMM Do, YYYY [at] h:mm A"),
                  })}
                </p>
              </div>
            );
          })()}
          descriptionPadding="px-3"
          generalCase={true}
          onClose={() => {
            setCloseClicked(true);
            setGeneralModal(false);
            // if (confirmationModal) {
            //   if (freePackage) {
            //     UpdgradePackage(packageDetails);
            //   } else {
            //     setGeneralModal(false);
            //   }
            // } else {
            //   setGeneralModal(false);
            // }
          }}
          onButtonClick={() => {
            setCloseClicked(false);
            if (confirmationModal && !freePackage) {
              UpdgradePackage(packageDetails);
            } else if (confirmationModal && freePackage) {
              UpdgradePackage(packageDetails);
            } else {
              if (!freePackage) {
                setwhatdataaddon(false);
                setConfirmationModal(true);
              } else if (freePackage && packageCode === "free") {
                UpdgradePackage(packageDetails);
              } else {
                console.log("AOA");
                setwhatdataaddon(false);
                setConfirmationModal(true);

                // UpdgradePackage(packageDetails);
              }
            }
          }}
          buttonText={
            confirmationModal && freePackage ? t("plans.yes") : t("plans.yes")
          }
          loadingBtn={
            !closeClicked &&
            (upgradeLoading || subscriptionLoading || addonsmutateLoading)
          }
          customWidthButton={`${"w-[8rem]"}`}
          loadingText={
            freePackage && packageCode === "free"
              ? t("plans.purchasing")
              : t("plans.downgrading")
          }
          customLoadingDimension={13}
          closeButtonText={`${
            confirmationModal && freePackage
              ? t("plans.cancel")
              : t("plans.cancel")
          }`}
          closeButtonLoading={
            closeClicked &&
            (upgradeLoading || subscriptionLoading || addonsmutateLoading)
          }
          closingLoadingText={t("plans.downgrading")}
        />
        <NoContentModal
          noContentModal={noContentModal}
          setNoContentNodal={setNoContentNodal}
          customWidth="sm:max-w-lg"
          heading={t("plans.restriction")}
          componentChild={t("plans.same_currency_message")}
          padding="pr-0"
          button={true}
          textCenter="text-center"
          descriptionWidth="w-5/6 text-center mx-auto flex justify-center"
          textSize={"text-lg"}
        />
        <div className="fixed inset-0 z-40 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center  text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform rounded-lg bg-bodyColor text-left shadow-xl transition-all w-full h-full">
                <div className="bg-bodyColor px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start justify-between py-4">
                    <div></div>
                    <h1
                      className="text-3xl font-bold select-text"
                      style={{ fontFamily: "Archia Bold" }}
                    >
                      {t("plans.title")}
                    </h1>
                    <IoMdClose
                      className="w-5 h-5 cursor-pointer"
                      onClick={() => setPlansModal(false)}
                    />
                  </div>
                </div>
                {/* <div className="px-4 py-3 flex md:w-4/5 mt-4 mx-auto flex-col"> */}
                <div className="px-4 py-3 flex md:w-[95%] mt-4 mx-auto flex-col">
                  <div className="flex md:flex-row sm:flex-col justify-between w-full">
                    <div
                      className="flex flex-row items-center border-black py-1 px-2 mt-2 border-2 bg-white rounded-lg font-medium sm:overflow-auto"
                      style={{ fontFamily: "Silka" }}
                    >
                      {Currencies.map((i) => (
                        <button
                          type="button"
                          className={`text-black flex hover:text-white hover:bg-coalColor ${
                            selecteditem === i.value
                              ? "bg-coalColor text-white"
                              : "bg-white text-coalColor"
                          } focus:outline-none font-medium rounded-lg sm:text-xs md:text-sm sm:px-6 md:px-8 sm:py-3 md:py-4 text-center`}
                          onClick={() => {
                            setSelectedItem(i.value);
                            setSelectedSign(i?.sign);
                          }}
                        >
                          {i.title}
                        </button>
                      ))}
                    </div>
                    <div
                      className="flex flex-row items-center sm:w-4/5 md:w-auto md:justify-end border-black md:py-0 sm:py-1 sm:pl-4 md:pl-2 px-2 mt-2 border-2 rounded-lg font-medium"
                      style={{ fontFamily: "Silka" }}
                    >
                      {type.map((i) => (
                        <button
                          type="button"
                          className={`text-black flex hover:text-white hover:bg-coalColor ${
                            selectedType === i.value
                              ? "bg-coalColor text-white"
                              : "bg-white text-black"
                          } focus:outline-none font-medium rounded-lg sm:text-xs md:text-sm sm:px-6 md:px-8 sm:py-3 md:py-4 text-center`}
                          onClick={() => setSelectedType(i.value)}
                        >
                          {t(
                            `plans.${
                              i.value === "month" ? "monthly" : "yearly"
                            }`
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="mt-5 grid md:grid-cols-3 gap-3">
                    {isLoading ? (
                      <>
                        {LoadingMap?.map((i) => (
                          <div
                            className={`p-5 border rounded-lg border-gray-300 shadow-[0_3px_10px_rgb(0,0,0,0.2)] bg-white loading-card`}
                          >
                            <div className="h-[130px]">
                              <div className="flex justify-between">
                                <SkeletonTheme
                                  baseColor="#e2e2e2"
                                  highlightColor="#bdbdbd"
                                >
                                  <h1 className="text-4xl font-bold w-1/2">
                                    <Skeleton count={1} />
                                  </h1>
                                </SkeletonTheme>
                              </div>
                              <SkeletonTheme
                                baseColor="#e2e2e2"
                                highlightColor="#bdbdbd"
                              >
                                <p className="mt-4 text-sm">
                                  <Skeleton count={1} />
                                </p>
                              </SkeletonTheme>
                            </div>
                            <div className="flex flex-row">
                              <SkeletonTheme
                                baseColor="#e2e2e2"
                                highlightColor="#bdbdbd"
                              >
                                <p className="text-4xl w-1/4">
                                  <Skeleton count={1} />
                                </p>
                              </SkeletonTheme>
                            </div>
                            <SkeletonTheme
                              baseColor="#e2e2e2"
                              highlightColor="#bdbdbd"
                            >
                              <p className="italic text-gray-500 text-xs mt-2">
                                <Skeleton count={1} />
                              </p>
                            </SkeletonTheme>

                            <SkeletonTheme
                              baseColor="#e2e2e2"
                              highlightColor="#bdbdbd"
                            >
                              <div className="w-full mt-5">
                                <Skeleton count={1} height={50} />
                              </div>
                            </SkeletonTheme>

                            <div className="mt-10">
                              <SkeletonTheme
                                baseColor="#e2e2e2"
                                highlightColor="#bdbdbd"
                              >
                                <div className="w-1/2">
                                  {" "}
                                  <Skeleton count={4} />
                                </div>
                              </SkeletonTheme>
                            </div>
                            <hr className="w-full mt-5 bg-gray-500 border-1" />
                            <div className="mt-6 px-2">
                              <SkeletonTheme
                                baseColor="#e2e2e2"
                                highlightColor="#bdbdbd"
                              >
                                <div>
                                  {" "}
                                  <Skeleton count={4} />
                                </div>
                              </SkeletonTheme>
                            </div>
                          </div>
                        ))}
                      </>
                    ) : (
                      <>
                        {data?.data?.map((i) => (
                          <div
                            className={`border rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] loadingplan ${
                              i?.subscribeBy === null &&
                              "bg-white shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
                            }`}
                          >
                            <div
                              className={`grid grid-cols-12 sm:px-3 2xl:px-5 py-3 rounded-t-lg ${
                                i?.code == "free" && "bg-[#C0FF06]"
                              } ${i?.code == "pro" && "bg-[#FFB500]"} ${
                                i?.code == "Enterprise" &&
                                "bg-[#FF5812] text-white"
                              }`}
                            >
                              <div className="col-span-9 flex flex-col">
                                <div className="flex flex-row ">
                                  <h1
                                    className="xl:text-lg 2xl:text-2xl font-bold"
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {i?.code === "free"
                                      ? t("plans.explore")
                                      : i?.code === "pro"
                                      ? t("plans.starter")
                                      : i?.code === "Enterprise"
                                      ? t("plans.pro")
                                      : i?.name}
                                  </h1>
                                  {i?.discountPrice > 0 &&
                                    i?.code !== "free" &&
                                    i?.prices[0]?.interval === "year" && (
                                      <p className="bg-coalColor my-auto rounded-lg text-xs 3xl:text-sm text-white flex flex-row ml-2 items-center px-1 3xl:ml-5 pb-[3px] pt-[1px] whitespace-nowrap max-w-full overflow-hidden">
                                        <img
                                          src={star}
                                          alt="Star icon"
                                          className="w-3 h-3 mx-auto flex-shrink-0"
                                        />
                                        <span className="ml-[2px] mr-[2px] text-xs !font-normal flex items-center my-auto flex-shrink-0">
                                          {t("plans.save")}
                                        </span>
                                        <span className="text-xs ml-[2px] !font-normal flex-shrink-0">
                                          {selectedSign !== "SAR" &&
                                            selectedSign}
                                          {Intl.NumberFormat("en-US", {
                                            style: "decimal",
                                            minimumFractionDigits: 0,
                                          }).format(
                                            Math.floor(i?.discountPrice)
                                          )}
                                          {selectedSign === "SAR" && (
                                            <span className="ml-1">
                                              {selectedSign}
                                            </span>
                                          )}
                                        </span>
                                      </p>
                                    )}
                                  {i?.discountPrice > 0 &&
                                    i?.code !== "free" &&
                                    i?.prices[0]?.interval === "month" && (
                                      <p className="bg-coalColor my-auto text-xs 3xl:text-sm rounded-lg text-white flex flex-row items-center px-1 ml-2 3xl:ml-5 pb-[3px] pt-[1px] whitespace-nowrap max-w-full overflow-hidden">
                                        <img
                                          src={star}
                                          alt="Star icon"
                                          className="w-3 h-3 pb-[1px] mx-auto flex-shrink-0"
                                        />
                                        <span className="ml-[2px] mr-[2px] text-xs !font-normal flex items-center my-auto flex-shrink-0">
                                          {t("plans.save")}{" "}
                                        </span>
                                        <span className="text-xs ml-[2px] !font-normal flex-shrink-0">
                                          {selectedSign !== "SAR" &&
                                            selectedSign}
                                          {Intl.NumberFormat("en-US", {
                                            style: "decimal",
                                            minimumFractionDigits: 0,
                                          }).format(
                                            Math.floor(i?.discountPrice)
                                          )}
                                          {selectedSign === "SAR" && (
                                            <span className="ml-1">
                                              {selectedSign}
                                            </span>
                                          )}
                                        </span>
                                        <span className="ml-1 text-xs !font-normal truncate">
                                          {t("plans.by_paying_annually")}
                                        </span>
                                      </p>
                                    )}
                                </div>
                                <p
                                  className="mt-2 xl:text-sm 2xl:text-base"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {i.subHeading}
                                </p>
                              </div>
                              <div className="col-span-3">
                                <div className="ml-auto">
                                  {/* Top Text */}
                                  <p
                                    className="flex items-center text-right"
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {selectedType === "year" ? (
                                      <span
                                        className={`text-base 3xl:text-2xl ml-auto text-right font-bold tracking-tight ${
                                          i?.code === "Enterprise"
                                            ? "text-white"
                                            : "text-coalColor"
                                        }`}
                                      >
                                        {selectedSign !== "SAR" && selectedSign}
                                        {i?.prices[0]?.price
                                          ? Intl.NumberFormat("en-US", {
                                              style: "decimal",
                                              minimumFractionDigits: 0,
                                              // minimumFractionDigits: 2,
                                              // maximumFractionDigits: 2,
                                            }).format(
                                              Math.floor(i.prices[0]?.price)
                                            )
                                          : 0}
                                        {selectedSign === "SAR" && (
                                          <span className="ml-1">
                                            {selectedSign}
                                          </span>
                                        )}
                                      </span>
                                    ) : (
                                      <span
                                        className={`text-base 3xl:text-2xl ml-auto text-right font-bold tracking-tight ${
                                          i?.code === "Enterprise"
                                            ? "text-white"
                                            : "text-coalColor"
                                        }`}
                                      >
                                        {selectedSign !== "SAR" && selectedSign}
                                        {i?.prices[0]?.price
                                          ? Intl.NumberFormat("en-US", {
                                              style: "decimal",
                                              minimumFractionDigits: 0,
                                              // minimumFractionDigits: 2,
                                              // maximumFractionDigits: 2,
                                            }).format(
                                              Math.floor(i?.prices[0]?.price)
                                            )
                                          : 0}
                                        {selectedSign === "SAR" && (
                                          <span className="ml-1">
                                            {selectedSign}
                                          </span>
                                        )}
                                      </span>
                                    )}
                                  </p>

                                  {/* Bottom Text */}
                                  <div className="flex justify-end">
                                    <span
                                      className={`xl:text-xs 2xl:text-sm sm:max-md:text-xs text-coalColor ${
                                        i?.code === "Enterprise" && "text-white"
                                      }`}
                                      style={{ fontFamily: "Silka" }}
                                    >
                                      {selectedType === "year"
                                        ? t("plans.per_year")
                                        : t("plans.per_month")}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="p-5">
                              <div
                                className="text-coalColor xl:text-xs 2xl:text-sm mt-5 md:h-20"
                                style={{ fontFamily: "Silka Light" }}
                              >
                                {i?.code == "free" && (
                                  <>
                                    <p>
                                      {t("plans.run")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        1 {t("plans.active_test")}
                                      </b>
                                    </p>
                                    <p>
                                      {t("plans.send_test_to")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        5 {t("plans.candidates")}
                                      </b>
                                    </p>
                                    <p>
                                      {t("plans.managed_by")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        1 {t("plans.account_user")}
                                      </b>
                                    </p>
                                  </>
                                )}
                                {i?.code == "pro" && (
                                  <>
                                    <p>
                                      {t("plans.run")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        5 {t("plans.active_tests")}
                                      </b>
                                    </p>
                                    <p>
                                      {t("plans.send_test_to")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        {t("plans.unlimited_candidates")}
                                      </b>
                                    </p>
                                    <p>
                                      {t("plans.managed_by")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        5 {t("plans.account_users")}
                                      </b>
                                    </p>
                                    <p>
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        {t("plans.organizations_max_25")}
                                      </b>
                                    </p>
                                  </>
                                )}
                                {i?.code == "Enterprise" && (
                                  <>
                                    <p>
                                      {t("plans.run")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        {t("plans.unlimited_active_tests")}
                                      </b>
                                    </p>
                                    <p>
                                      {t("plans.send_test_to")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        {t("plans.unlimited_candidates")}
                                      </b>
                                    </p>
                                    <p>
                                      {t("plans.managed_by")}
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        {t("plans.unlimited_account_users")}
                                      </b>
                                    </p>
                                    <p>
                                      <b style={{ fontFamily: "Silka Bold" }}>
                                        {" "}
                                        {t("plans.organizations_25_250")}
                                      </b>
                                    </p>
                                  </>
                                )}
                              </div>
                              <div
                                className="mt-6 px-2 md:h-[27rem]"
                                style={{ fontFamily: "Silka" }}
                              >
                                <div
                                  className={
                                    i?.code == "free"
                                      ? styles["html-content"]
                                      : i?.code == "pro"
                                      ? styles2["html-content"]
                                      : styles3["html-content"]
                                  }
                                >
                                  {ReactHtmlParser(i?.description)}
                                </div>
                                {/* Add text for Enterprise plan - inside fixed height container */}
                              </div>
                              {getText(i) !== "" && (
                                <div className="md:h-5 xl:mt-10 2xl:mt-0 relative">
                                  {discountedPackages.includes(i?.code) &&
                                    myCouponPrice > 0 && (
                                      <div className="md:absolute md:-top-32 2xl:-top-28 left-0 right-0 flex flex-col items-center justify-center">
                                        <span
                                          className="bg-primaryGreen py-1 px-6 rounded-lg text-sm text-center inline-block shadow"
                                          style={{ fontFamily: "Silka" }}
                                        >
                                          {t("plans.discount_message", {
                                            discount: myCouponPrice,
                                          })}
                                        </span>
                                        <span
                                          className="mt-1 text-sm text-coalColor"
                                          style={{ fontFamily: "Silka" }}
                                        >
                                          {t("plans.discounted_price", {
                                            price: (
                                              i?.prices?.[0]?.price -
                                              (i?.prices?.[0]?.price *
                                                myCouponPrice) /
                                                100
                                            ).toFixed(2),
                                            sign: selectedSign,
                                          })}
                                        </span>
                                      </div>
                                    )}
                                  {i?.code === "Enterprise" && (
                                    <p
                                      className="md:absolute sm:mt-5 md:mt-0 md:-top-20 2xl:-top-10 px-[4rem] 2xl:px-[6rem] left-0 right-0 text-xs text-coalColor text-center"
                                      style={{ fontFamily: "Silka" }}
                                    >
                                      {t("plans.enterprise_contact")}{" "}
                                      <span
                                        className="underline cursor-pointer"
                                        onClick={
                                          handleOpenEmailInboxButtonClick
                                        }
                                      >
                                        {t("plans.contact_us")}
                                      </span>{" "}
                                      {t("plans.for_pro_subscription")}
                                    </p>
                                  )}
                                  {selectedType === "year" ? (
                                    <p className="italic text-gray-500 text-xs text-center mt-2">
                                      {t("plans.year_commitment_upfront", {
                                        price: `${
                                          selectedSign !== "SAR"
                                            ? selectedSign
                                            : ""
                                        }${
                                          i.prices[0]?.price
                                            ? Intl.NumberFormat("en-US", {
                                                style: "decimal",
                                                minimumFractionDigits: 0,
                                              }).format(i.prices[0]?.price)
                                            : 0
                                        }${
                                          selectedSign === "SAR"
                                            ? ` ${selectedSign}`
                                            : ""
                                        }`,
                                      })}
                                    </p>
                                  ) : (
                                    <>
                                      {getText(i) !== t("plans.explore") ? (
                                        <p className="italic text-gray-500 text-xs text-center mt-2">
                                          {t("plans.year_commitment_upfront", {
                                            price: `${
                                              selectedSign !== "SAR"
                                                ? selectedSign
                                                : ""
                                            }${
                                              i.prices[0]?.price
                                                ? Intl.NumberFormat("en-US", {
                                                    style: "decimal",
                                                    minimumFractionDigits: 0,
                                                  }).format(i.prices[0]?.price)
                                                : 0
                                            }${
                                              selectedSign === "SAR"
                                                ? ` ${selectedSign}`
                                                : ""
                                            }`,
                                          })}
                                        </p>
                                      ) : (
                                        <p className="italic text-gray-500 text-xs text-center mt-2"></p>
                                      )}
                                    </>
                                  )}
                                </div>
                              )}
                              <div className="flex flex-col">
                                {getText(i) && (
                                  <div className="relative group">
                                    <button
                                      type="button"
                                      style={{ fontFamily: "Silka" }}
                                      className={`text-black flex mt-5 focus:outline-none text-sm  rounded-md px-4 2xl:px-8 py-4 text-center justify-center w-full transition-all
                                        ${
                                          (i?.code === packageCode &&
                                            packageCode !== "free") ||
                                          (i?.code === packageCode &&
                                            packageCode === "free" &&
                                            subscriptionData?.downgradeDetails
                                              ?.addOnId)
                                            ? "bg-white text-gray-400 border border-gray-400"
                                            : subscriptionData?.package
                                                ?.name ===
                                                t("plans.free_forever") &&
                                              dataUser?.userAddOns?.length ===
                                                0 &&
                                              !is_free_user
                                            ? "bg-coalColor text-white hover:bg-coalColor/90"
                                            : getButtonVariant(
                                                i,
                                                subscriptionData,
                                                dataUser?.userAddOns?.length
                                              )
                                        }


      ${
        subscriptionData?.package?.name === t("plans.free_forever") &&
        dataUser?.userAddOns?.length === 0 &&
        !is_free_user
          ? "cursor-pointer"
          : isButtonDisabled(
              i,
              subscriptionData,
              dataUser?.userAddOns?.length,
              is_free_user
            )
          ? "cursor-not-allowed"
          : ""
      }`}
                                      disabled={
                                        (i?.code === packageCode &&
                                          packageCode !== "free") ||
                                        (i?.code === packageCode &&
                                          packageCode === "free" &&
                                          subscriptionData?.downgradeDetails
                                            ?.addOnId) ||
                                        (subscriptionData?.package?.name ===
                                          t("plans.free_forever") &&
                                        dataUser?.userAddOns?.length === 0 &&
                                        !is_free_user
                                          ? false
                                          : isButtonDisabled(
                                              i,
                                              subscriptionData,
                                              dataUser?.userAddOns?.length,
                                              is_free_user
                                            )
                                          ? true
                                          : null)
                                        // subscriptionData?.package?.name ===
                                        // "Free forever"
                                        //   ? (dataUser?.userAddOns?.length !==
                                        //       0 &&
                                        //       isButtonDisabled(
                                        //         i,
                                        //         subscriptionData
                                        //       )) ||
                                        //     is_free_user
                                        //   : isButtonDisabled(
                                        //       i,
                                        //       subscriptionData
                                        //     )
                                      }
                                      aria-disabled={isButtonDisabled(
                                        i,
                                        subscriptionData
                                      )}
                                      onClick={() => {
                                        console.log(
                                          subscriptionData?.currency,
                                          "subscriptionData?.currency"
                                        );

                                        if (
                                          subscriptionData?.currency &&
                                          (subscriptionData?.currency !==
                                            selecteditem ||
                                            subscriptionData?.currency ===
                                              selectedSign)
                                        ) {
                                          setNoContentNodal(true);
                                        } else {
                                          setNoContentNodal(false);
                                          if (
                                            getText(i) === t("plans.downgrade")
                                          ) {
                                            setConfirmationModal(false);
                                            setFreeCheck("");
                                            setFreePackage(false);
                                            setPackageDetails({
                                              id: i?.id,
                                              selectedItem: selecteditem,
                                              selectedType: selectedType,
                                              stripeProductID:
                                                i?.stripeProductID,
                                              stripePriceID: i?.stripePriceID,
                                              stripeYearlyPriceID:
                                                i?.stripeYearlyPriceID,
                                            });
                                            handleDowngradeModal(i?.name);
                                          } else if (
                                            getText(i) ===
                                            t("plans.downgrade_with_data_addon")
                                          ) {
                                            setConfirmationModal(false);
                                            setFreeCheck("");
                                            setFreePackage(true);
                                            setPurchaseAddon(true);
                                            setPackageDetails({
                                              id: i?.id,
                                              selectedItem: selecteditem,
                                              selectedType: selectedType,
                                              stripeProductID:
                                                i?.stripeProductID,
                                              stripePriceID: i?.stripePriceID,
                                              stripeYearlyPriceID:
                                                i?.stripeYearlyPriceID,
                                            });
                                            handleDowngradeModal(
                                              i?.name,
                                              getText(i)
                                            );
                                            // setFreeCheck("free");
                                            // setPackageDetails({
                                            //   id: i?.id,
                                            //   selectedItem: selecteditem,
                                            //   selectedType: selectedType,
                                            //   stripeProductID: i?.stripeProductID,
                                            //   stripePriceID: i?.stripePriceID,
                                            //   stripeYearlyPriceID:
                                            //     i?.stripeYearlyPriceID,
                                            // });
                                            // handlePlanSelection(i);
                                          } else if (
                                            getText(i) ===
                                              t("plans.free_forever") &&
                                            dataUser?.userAddOns?.length === 0
                                          ) {
                                            setFreeCheck("free");
                                            setOnlyAddonPurchase(true);
                                            setPackageDetails({
                                              id: i?.id,
                                              selectedItem: selecteditem,
                                              selectedType: selectedType,
                                              stripeProductID:
                                                i?.stripeProductID,
                                              stripePriceID: i?.stripePriceID,
                                              stripeYearlyPriceID:
                                                i?.stripeYearlyPriceID,
                                            });
                                            handlePlanSelection(i);
                                          } else {
                                            setFreeCheck("");
                                            handlePlanSelection(i);
                                          }
                                        }
                                      }}
                                      aria-label={getButtonAriaLabel(i)}
                                    >
                                      {shouldDisplayDowngradeMessage(
                                        i,
                                        packageCode
                                      ) ? (
                                        <>
                                          {packageCode !== "free" ? (
                                            <span className="text-xs 2xl:text-lg">
                                              {t(
                                                "plans.downgrade_effective_on",
                                                {
                                                  date: moment(
                                                    subscriptionData.commitmentExpiry
                                                  )
                                                    .utcOffset(0)
                                                    .format("MMM Do, YYYY"),
                                                }
                                              )}
                                            </span>
                                          ) : !subscriptionData
                                              ?.downgradeDetails?.addOnId ? (
                                            t("plans.purchase_data_addon")
                                          ) : (
                                            <span className="text-xs 2xl:text-lg">
                                              {t(
                                                "plans.downgrade_effective_on",
                                                {
                                                  date: moment(
                                                    subscriptionData.commitmentExpiry
                                                  )
                                                    .utcOffset(0)
                                                    .format("MMM Do, YYYY"),
                                                }
                                              )}
                                            </span>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          {renderButtonContent(i) ===
                                          t("plans.explore") ? (
                                            <>
                                              {dataUser?.userAddOns?.length >
                                              0 ? (
                                                <span className="text-xs 2xl:text-lg">
                                                  {t(
                                                    "plans.downgraded_with_data_addon"
                                                  )}
                                                </span>
                                              ) : (
                                                <>
                                                  {is_free_user ? (
                                                    <span className="text-xs 2xl:text-lg">
                                                      {t("plans.explore")}
                                                    </span>
                                                  ) : (
                                                    <span className="text-xs 2xl:text-lg">
                                                      {t(
                                                        "plans.purchase_data_addon"
                                                      )}
                                                    </span>
                                                  )}
                                                </>
                                              )}
                                            </>
                                          ) : renderButtonContent(i) ===
                                            t(
                                              "plans.downgrade_with_data_addon"
                                            ) ? (
                                            <span className="text-xs 2xl:text-lg">
                                              {t(
                                                "plans.downgrade_with_data_addon"
                                              )}
                                            </span>
                                          ) : (
                                            <span className="text-xs 2xl:text-lg">
                                              {renderButtonContent(i)}
                                            </span>
                                          )}
                                        </>
                                      )}
                                    </button>
                                  </div>
                                )}

                                {getText(i) ===
                                  t("plans.downgrade_with_data_addon") ||
                                getText(i) === t("plans.free_forever") ? (
                                  <div
                                    className="flex justify-between text-[10px] 2xl:text-sm mt-2 text-gray-500"
                                    style={{ fontFamily: "Silka" }}
                                  >
                                    {subscriptionData?.package?.id === 1 &&
                                      !subscriptionData?.downgradeDetails
                                        ?.addOnId &&
                                      !dataUser?.userAddOns?.length && (
                                        <span className="text-black font-bold">
                                          {t(
                                            "plans.downgraded_without_data_addon"
                                          )}
                                        </span>
                                      )}
                                    {is_free_user === true ? (
                                      <p></p>
                                    ) : (
                                      <p
                                        className="hover:text-coalColor cursor-pointer"
                                        onClick={() => {
                                          if (
                                            subscriptionData?.downgradeDetails
                                              ?.packageId !== 1
                                          ) {
                                            setConfirmationModal(false);
                                            setFreeCheck("");
                                            setPurchaseAddon(false);
                                            setFreePackage(false);
                                            setPackageDetails({
                                              id: i?.id,
                                              selectedItem: selecteditem,
                                              selectedType: selectedType,
                                              stripeProductID:
                                                i?.stripeProductID,
                                              stripePriceID: i?.stripePriceID,
                                              stripeYearlyPriceID:
                                                i?.stripeYearlyPriceID,
                                            });
                                            handleDowngradeModal(i?.name);
                                          }
                                        }}
                                      >
                                        {subscriptionData?.downgradeDetails
                                          ?.packageId === 1 &&
                                        !subscriptionData?.downgradeDetails
                                          ?.addOnId ? (
                                          <span className="text-black font-bold">
                                            {t("plans.downgrade_effective_on", {
                                              date: moment(
                                                subscriptionData.commitmentExpiry
                                              )
                                                .utcOffset(0)
                                                .format("MMM Do, YYYY"),
                                            })}
                                          </span>
                                        ) : (
                                          <>
                                            {subscriptionData?.package?.code !==
                                              "free" &&
                                              subscriptionData?.downgradeDetails
                                                ?.packageId !== 1 &&
                                              t(
                                                "plans.downgrade_without_data_addon"
                                              )}
                                          </>
                                        )}
                                      </p>
                                    )}
                                    {dataUser?.userAddOns?.length === 0 ||
                                    dataUser === 0 ||
                                    is_free_user ? (
                                      <>
                                        {!immediateUpgrade && (
                                          <p
                                            className="hover:text-coalColor cursor-pointer"
                                            onClick={() => {
                                              setConfirmationModal(true);
                                              setwhatdataaddon(true);
                                              setNoButtons("yes");
                                              setGeneralModal(true);
                                            }}
                                          >
                                            {subscriptionData?.downgradeDetails
                                              ?.addOnId
                                              ? ""
                                              : t("plans.what_is_data_addon")}
                                          </p>
                                        )}
                                      </>
                                    ) : (
                                      <p></p>
                                    )}
                                  </div>
                                ) : (
                                  <>
                                    {getText(i) !== "" && (
                                      <hr className="w-full mt-5 bg-gray-500 border-1" />
                                    )}
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                  <p className="mx-auto my-20" style={{ fontFamily: "Silka" }}>
                    <b>{t("plans.fair_usage_statement")}</b>{" "}
                    {t("plans.fair_usage_text")}{" "}
                    <b
                      className="cursor-pointer"
                      onClick={handleOpenEmailInboxButtonClick}
                    >
                      {t("plans.contact_us")}
                    </b>{" "}
                    {t("plans.recruiting_agency_pricing")}
                  </p>
                </div>
                <div className="bg-white mb-10 flex md:w-[93%] text-black mx-auto sm:px-0 md:px-4 py-3 rounded-lg shadow fade-in-image">
                  <table className="text-left md:w-full">
                    <thead>
                      <tr>
                        <th className="w-[70%]"></th>
                        {data?.data?.map((subscription, inx) => (
                          <th
                            key={inx}
                            className={`w-[10%] py-3 text-left px-4`}
                          >
                            {subscription?.name === "Free forever"
                              ? t("plans.explore")
                              : subscription?.name === "Starter"
                              ? t("plans.starter")
                              : subscription?.name === "Pro"
                              ? t("plans.pro")
                              : subscription?.name}
                          </th>
                        ))}
                      </tr>
                      <tr className="">
                        <th className="w-[70%]"></th>
                        {data?.data?.map((_, subIndex) => (
                          <th
                            key={`check-${subIndex}`}
                            colSpan="1"
                            className="w-1/6 px-4"
                          ></th>
                        ))}
                      </tr>
                    </thead>
                    {featureData?.map((category, index) => (
                      <React.Fragment key={`category-${index}`}>
                        <thead>
                          <tr className="hover:bg-[#DBD9C0]">
                            <th
                              className="w-[70%] text-coalColor px-5 py-3 border-b"
                              colSpan="1"
                              style={{ fontFamily: "Archia Bold" }}
                            >
                              {category?.category_name}
                            </th>
                            {data?.data?.map((_, subIndex) => (
                              <th
                                key={`sub-${subIndex}`}
                                colSpan="1"
                                className="w-1/6 px-4 border-b"
                                style={{ fontFamily: "Archia Bold" }}
                              ></th>
                            ))}
                          </tr>
                        </thead>
                        <tbody style={{ fontFamily: "Silka" }}>
                          {category?.features?.map((feature, featureIndex) => (
                            <tr
                              key={`feature-${featureIndex}`}
                              className="hover:bg-[#DBD9C0]"
                            >
                              <td className="w-[70%] sm:text-sm md:text-base text-black px-5 py-3">
                                {feature?.name}
                              </td>
                              <td className="w-1/6 px-4 py-3">
                                {feature?.isFree ? <GiCheckMark /> : null}
                              </td>
                              <td className="w-1/6 px-4 py-3">
                                {feature?.isPro ? <GiCheckMark /> : null}
                              </td>
                              <td className="w-1/6 px-4 py-3">
                                {feature?.isEnterprise ? <GiCheckMark /> : null}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </React.Fragment>
                    ))}
                  </table>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
