{"version": 3, "file": "PhoneInput.js", "names": ["createPhoneInput", "defaultMetadata", "PhoneInput", "ref", "inputComponent", "metadata", "rest", "InputBasic", "PhoneTextInput", "React", "forwardRef", "propTypes", "PropTypes", "elementType", "metadataType"], "sources": ["../../source/react-native/PhoneInput.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport PhoneTextInput from './PhoneTextInput.js'\r\nimport PhoneInput_ from '../PhoneInput.js'\r\nimport InputBasic from '../InputBasic.js'\r\n\r\nimport { metadata as metadataType } from '../PropTypes.js'\r\n\r\n/**\r\n * This is an _experimental_ React Native component.\r\n * Feedback thread: https://github.com/catamphetamine/react-phone-number-input/issues/296\r\n */\r\nexport function createPhoneInput(defaultMetadata) {\r\n\tlet PhoneInput = ({\r\n\t\tinputComponent,\r\n\t\tmetadata = defaultMetadata,\r\n\t\t...rest\r\n\t}, ref) => (\r\n\t\t<PhoneInput_\r\n\t\t\t{...rest}\r\n\t\t\tref={ref}\r\n\t\t\tmetadata={metadata}\r\n\t\t\tComponent={InputBasic}\r\n\t\t\tinputComponent={PhoneTextInput}\r\n\t\t\tTextInputComponent={inputComponent}\r\n\t\t/>\r\n\t)\r\n\r\n\tPhoneInput = React.forwardRef(PhoneInput)\r\n\r\n\tPhoneInput.propTypes = {\r\n\t\t/**\r\n\t\t * Allows specifying a custom input field component,\r\n\t\t * like a \"Material UI\" input field or something.\r\n\t\t */\r\n\t\tinputComponent: PropTypes.elementType,\r\n\r\n\t\t/**\r\n\t\t * `libphonenumber-js` metadata.\r\n\t\t */\r\n\t\tmetadata: metadataType\r\n\t}\r\n\r\n\treturn PhoneInput\r\n}\r\n\r\nexport default createPhoneInput()"], "mappings": ";;;;;;;;AAAA;;AACA;;AAEA;;AACA;;AACA;;AAEA;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACO,SAASA,gBAAT,CAA0BC,eAA1B,EAA2C;EACjD,IAAIC,UAAU,GAAG,0BAIdC,GAJc;IAAA,IAChBC,cADgB,QAChBA,cADgB;IAAA,yBAEhBC,QAFgB;IAAA,IAEhBA,QAFgB,8BAELJ,eAFK;IAAA,IAGbK,IAHa;;IAAA,oBAKhB,gCAAC,sBAAD,eACKA,IADL;MAEC,GAAG,EAAEH,GAFN;MAGC,QAAQ,EAAEE,QAHX;MAIC,SAAS,EAAEE,sBAJZ;MAKC,cAAc,EAAEC,0BALjB;MAMC,kBAAkB,EAAEJ;IANrB,GALgB;EAAA,CAAjB;;EAeAF,UAAU,gBAAGO,iBAAA,CAAMC,UAAN,CAAiBR,UAAjB,CAAb;EAEAA,UAAU,CAACS,SAAX,GAAuB;IACtB;AACF;AACA;AACA;IACEP,cAAc,EAAEQ,qBAAA,CAAUC,WALJ;;IAOtB;AACF;AACA;IACER,QAAQ,EAAES;EAVY,CAAvB;EAaA,OAAOZ,UAAP;AACA;;eAEcF,gBAAgB,E"}