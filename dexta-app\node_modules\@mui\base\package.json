{"name": "@mui/base", "version": "5.0.0-alpha.97", "private": false, "author": "MUI Team", "description": "A library of headless ('unstyled') React UI components and low-level hooks.", "main": "./node/index.js", "keywords": ["react", "react-component", "mui", "unstyled", "a11y"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-base"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/base/getting-started/overview/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "dependencies": {"@babel/runtime": "^7.18.9", "@emotion/is-prop-valid": "^1.2.0", "@mui/types": "^7.2.0", "@mui/utils": "^5.10.3", "@popperjs/core": "^2.11.6", "clsx": "^1.2.1", "prop-types": "^15.8.1", "react-is": "^18.2.0"}, "sideEffects": false, "publishConfig": {"access": "public"}, "engines": {"node": ">=12.0.0"}, "module": "./index.js", "types": "./index.d.ts"}