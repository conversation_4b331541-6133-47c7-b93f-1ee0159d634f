{"author": "<PERSON>", "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "2.2.1", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c"}, "devDependencies": {"is-mergeable-object": "1.1.0", "is-plain-object": "^2.0.4", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "12.0.1", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.3.12"}, "license": "MIT", "dependencies": {}}