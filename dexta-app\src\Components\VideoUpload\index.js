import React, { useState } from "react";

const VideoUpload = ({ files, setFiles }) => {
  const handleDrop = (e) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files);
    setFiles((prevFiles) => [...prevFiles, ...droppedFiles]);
  };

  const handleFileChange = (e) => {
    const selectedFiles = Array.from(e.target.files);
    setFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
  };

  return (
    <article
      className="relative flex flex-col bg-white rounded-md"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
    >
      <input
        id="hidden-input"
        type="file"
        multiple
        className="hidden"
        onChange={handleFileChange}
      />
      {files.length === 0 && (
        <label className="text-sm xl:w-full 2xl:w-full py-3 relative mx-auto flex justify-center cursor-pointer font-medium">
          <p
            className="text-center text-[#767676]"
            style={{ fontFamily: "Silka" }}
          >
            Upload Your Company Welcome Video
          </p>
        </label>
      )}
    </article>
  );
};

export default VideoUpload;
