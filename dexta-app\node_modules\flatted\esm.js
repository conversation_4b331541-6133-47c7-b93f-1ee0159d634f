/*! (c) 2020 <PERSON> */
const{parse:t,stringify:e}=JSON,{keys:n}=Object,o=String,c={},r=(t,e)=>e,s=t=>t instanceof o?o(t):t,l=(t,e)=>"string"==typeof e?new o(e):e,a=(t,e,r,s)=>{const l=[];for(let a=n(r),{length:f}=a,i=0;i<f;i++){const n=a[i],f=r[n];if(f instanceof o){const o=t[f];"object"!=typeof o||e.has(o)?r[n]=s.call(r,n,o):(e.add(o),r[n]=c,l.push({k:n,a:[t,e,o,s]}))}else r[n]!==c&&(r[n]=s.call(r,n,f))}for(let{length:t}=l,e=0;e<t;e++){const{k:t,a:n}=l[e];r[t]=s.call(r,t,a.apply(null,n))}return r},f=(t,e,n)=>{const c=o(e.push(n)-1);return t.set(n,c),c},i=(e,n)=>{const o=t(e,l).map(s),c=o[0],f=n||r,i="object"==typeof c&&c?a(o,new Set,c,f):c;return f.call({"":i},"",i)},p=(t,n,o)=>{const c=n&&"object"==typeof n?(t,e)=>""===t||-1<n.indexOf(t)?e:void 0:n||r,s=new Map,l=[],a=[];let i=+f(s,l,c.call({"":t},"",t)),p=!i;for(;i<l.length;)p=!0,a[i]=e(l[i++],u,o);return"["+a.join(",")+"]";function u(t,e){if(p)return p=!p,e;const n=c.call(this,t,e);switch(typeof n){case"object":if(null===n)return n;case"string":return s.get(n)||f(s,l,n)}return n}},u=e=>t(p(e)),g=t=>i(e(t));export{g as fromJSON,i as parse,p as stringify,u as toJSON};
