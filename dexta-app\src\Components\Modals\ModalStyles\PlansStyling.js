// Style variants
export const getButtonVariant = (i, subscriptionData, dataaddons) => {
  const isCurrentPlan = subscriptionData?.package?.name === i?.name;
  return isCurrentPlan
    ? "bg-white text-gray-400 border border-gray-400"
    : "bg-coalColor text-white hover:bg-coalColor/90";
};

// Disabled state
export const isButtonDisabled = (i, subscriptionData, dataaddons) => {
  const isSameInterval =
    subscriptionData?.package?.prices[0]?.interval === i?.prices[0]?.interval;

  return (
    i?.subscribeBy &&
    subscriptionData?.package?.name === i?.name &&
    isSameInterval
  );
};
