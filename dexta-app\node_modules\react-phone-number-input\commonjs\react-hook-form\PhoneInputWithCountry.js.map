{"version": 3, "file": "PhoneInputWithCountry.js", "names": ["createPhoneInput", "defaultMetadata", "PhoneInputWithCountry", "ref", "metadata", "rest", "PhoneInputWithCountry_", "React", "forwardRef", "propTypes", "metadataType"], "sources": ["../../source/react-hook-form/PhoneInputWithCountry.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport ReactHookFormInput from './ReactHookFormInput.js'\r\nimport PhoneInputWithCountry_ from '../PhoneInputWithCountryDefault.js'\r\n\r\nimport { metadata as metadataType } from '../PropTypes.js'\r\n\r\nexport function createPhoneInput(defaultMetadata) {\r\n  let PhoneInputWithCountry = ({\r\n    metadata = defaultMetadata,\r\n    ...rest\r\n  }, ref) => {\r\n    return (\r\n      <ReactHookFormInput\r\n        {...rest}\r\n        ref={ref}\r\n        metadata={metadata}\r\n        Component={PhoneInputWithCountry_}\r\n      />\r\n    )\r\n  }\r\n\r\n  PhoneInputWithCountry = React.forwardRef(PhoneInputWithCountry)\r\n\r\n  PhoneInputWithCountry.propTypes = {\r\n    metadata: metadataType\r\n  }\r\n\r\n  return PhoneInputWithCountry\r\n}\r\n\r\nexport default createPhoneInput()"], "mappings": ";;;;;;;;AAAA;;AACA;;AAEA;;AACA;;AAEA;;;;;;;;;;;;AAEO,SAASA,gBAAT,CAA0BC,eAA1B,EAA2C;EAChD,IAAIC,qBAAqB,GAAG,qCAGzBC,GAHyB,EAGjB;IAAA,yBAFTC,QAES;IAAA,IAFTA,QAES,8BAFEH,eAEF;IAAA,IADNI,IACM;;IACT,oBACE,gCAAC,8BAAD,eACMA,IADN;MAEE,GAAG,EAAEF,GAFP;MAGE,QAAQ,EAAEC,QAHZ;MAIE,SAAS,EAAEE;IAJb,GADF;EAQD,CAZD;;EAcAJ,qBAAqB,gBAAGK,iBAAA,CAAMC,UAAN,CAAiBN,qBAAjB,CAAxB;EAEAA,qBAAqB,CAACO,SAAtB,GAAkC;IAChCL,QAAQ,EAAEM;EADsB,CAAlC;EAIA,OAAOR,qBAAP;AACD;;eAEcF,gBAAgB,E"}