"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 3H5v12.93l7 4.66 7-4.67V3zm-9 13-4-4 1.41-1.41 2.58 2.58 6.59-6.59L18 8l-8 8z",
  opacity: ".3"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 1H5c-1.1 0-1.99.9-1.99 2L3 15.93c0 .69.35 1.3.88 1.66L12 23l8.11-5.41c.53-.36.88-.97.88-1.66L21 3c0-1.1-.9-2-2-2zm-7 19.6-7-4.66V3h14v12.93l-7 4.67zm-2.01-7.42-2.58-2.59L6 12l4 4 8-8-1.42-1.42z"
}, "1")], 'BeenhereTwoTone');
exports.default = _default;