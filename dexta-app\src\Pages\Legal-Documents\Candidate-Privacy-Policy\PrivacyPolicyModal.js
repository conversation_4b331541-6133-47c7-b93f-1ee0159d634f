import React, { useEffect, useRef } from "react";
import Scrollbars from "react-custom-scrollbars";
import { useNavigate } from "react-router-dom";
import closeIcon from "../../../Dexta_assets/closeModal.png";
import CustomButton from "../../../Components/CustomButton/CustomButton";
import { Fragment, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { useTranslation } from "react-i18next";

const PrivacyPolicyModal = ({
  privacyModal,
  setPrivacyModal,
  privacy,
  setPrivacy,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const cancelButtonRef = useRef(null);
  useEffect(() => {
    window.scroll(0, 0);
  }, []);

  const closeTab = () => {
    setPrivacyModal(false);
  };

  const handlePath = () => {
    window.location = "https://dexta.io/";
  };

  return (
    <Transition.Root show={privacyModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-40"
        initialFocus={cancelButtonRef}
        onClose={() => {
          setPrivacyModal(false);
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enterTo="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-100"
          leaveFrom="opacity-100 translate-y-0 sm:scale-100"
          leaveTo="opacity-0 translate-y-2 sm:translate-y-0 sm:scale-95"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-40 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel
                className={`relative transform overflow-hidden rounded-lg bg-bodyColor text-left shadow-xl transition-all w-full`}
              >
                <div className="bg-bodyColor px-4 pb-4 pt-5 sm:p-6">
                  <div className="">
                    <div className="mt-3 text-center ">
                      <div className="bg-bodyColor md:py-10">
                        <img
                          src={closeIcon}
                          className="absolute top-3 right-5 z-20 w-6 h-6 cursor-pointer"
                          onClick={closeTab}
                        />
                        <div className="max-w-3xl text-left mx-auto bg-bodyColor shadow-md pb-20 md:h-[850px] overflow-hidden md:rounded-lg">
                          <div className="bg-[#252E3A] p-8">
                            <h2
                              className="text-white text-center text-2xl"
                              style={{ fontFamily: "Archia Bold" }}
                            >
                              {t("privacy_policy_modal.title")}
                            </h2>
                            <p
                              className="text-white text-[11px] text-center"
                              style={{ fontFamily: "Silka" }}
                            >
                              {t("privacy_policy_modal.version")}
                            </p>
                          </div>

                          <div className="overflow-y-auto h-[calc(850px-120px)]">
                            <Scrollbars
                              style={{
                                width: "100%",
                                height: "100%",
                                overflowX: "hidden",
                              }}
                            >
                              <div className="p-8">
                                <p
                                  className=""
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.introduction_title")}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.introduction")}
                                </p>
                                <p
                                  className=" mt-6 "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.info_title")}
                                </p>
                                <span>
                                  <p
                                    className=" mt-2  "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t("privacy_policy_modal.info_subtitle")}
                                  </p>
                                  <p
                                    className="mb-4  text-xs"
                                    style={{ fontFamily: "Silka" }}
                                  >
                                    {t("privacy_policy_modal.info_description")}
                                  </p>
                                  <ul className="list-disc pl-5 pl-10 text-xs">
                                    <li
                                      style={{ fontFamily: "Silka" }}
                                      className=" text-xs"
                                    >
                                      {t(
                                        "privacy_policy_modal.info_list.personal"
                                      )}
                                    </li>
                                    <li
                                      style={{ fontFamily: "Silka" }}
                                      className=" text-xs"
                                    >
                                      {t(
                                        "privacy_policy_modal.info_list.professional"
                                      )}
                                    </li>
                                    <li
                                      style={{ fontFamily: "Silka" }}
                                      className=" text-xs"
                                    >
                                      {t("privacy_policy_modal.info_list.test")}
                                    </li>
                                    <li
                                      style={{ fontFamily: "Silka" }}
                                      className=" text-xs"
                                    >
                                      {t(
                                        "privacy_policy_modal.info_list.usage"
                                      )}
                                    </li>
                                  </ul>
                                </span>
                                <p
                                  className="mt-6  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.purposes_title")}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.service_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.service_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.communication_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.communication_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.monitoring_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.monitoring_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.optimization_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.optimization_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.suppliers_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.suppliers_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.easy_access_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.easy_access_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.statistics_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.statistics_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.development_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.development_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.purposes.benchmarks_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.purposes.benchmarks_text"
                                  )}
                                </p>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.legal_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.legal_intro")}
                                </p>
                                <p
                                  className="mb-4  text-xs "
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.legal.consent_bold"
                                    )}
                                  </span>
                                  {t("privacy_policy_modal.legal.consent_text")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.legal.contract_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.legal.contract_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.legal.legal_obligation_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.legal.legal_obligation_text"
                                  )}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <span
                                    className="mb-4 "
                                    style={{ fontFamily: "Archia Bold" }}
                                  >
                                    {t(
                                      "privacy_policy_modal.legal.legitimate_bold"
                                    )}
                                  </span>
                                  {t(
                                    "privacy_policy_modal.legal.legitimate_text"
                                  )}
                                </p>
                                <p
                                  className="mt-2 "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.sharing_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.sharing_intro")}
                                </p>
                                <ul className="list-disc pl-5 pl-10 text-xs">
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.sharing_list.employers"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.sharing_list.providers"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t("privacy_policy_modal.sharing_list.law")}
                                  </li>
                                </ul>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.rights_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.rights_intro")}
                                </p>
                                <ul className="list-disc pl-5 pl-10 text-xs">
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.access"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.rectification"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.erasure"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.restrict"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.object"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.portability"
                                    )}
                                  </li>
                                  <li
                                    style={{ fontFamily: "Silka" }}
                                    className=" text-xs"
                                  >
                                    {t(
                                      "privacy_policy_modal.rights_list.automated"
                                    )}
                                  </li>
                                </ul>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.rights_note")}
                                </p>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t(
                                    "privacy_policy_modal.international_title"
                                  )}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.international")}
                                </p>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.security_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.security")}
                                </p>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.retention_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.retention")}
                                </p>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.changes_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.changes")}
                                </p>
                                <p
                                  className="mt-2  "
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("privacy_policy_modal.contact_title")}
                                </p>
                                <p
                                  className="mb-4  text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.contact_intro")}
                                </p>
                                <p
                                  className="text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.company")}
                                </p>
                                <p
                                  className="text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.address")}
                                </p>
                                <p
                                  className="text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.email")}
                                </p>
                                <p
                                  className="mt-2 text-xs"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {t("privacy_policy_modal.last_update")}
                                </p>
                              </div>
                              <div
                                className="w-1/5 mx-auto"
                                style={{ fontFamily: "Silka" }}
                              >
                                <CustomButton
                                  label={t("privacy_policy_modal.accept")}
                                  iconWidth={5}
                                  iconHeight={5}
                                  textSize="font-medium"
                                  hoverBgColor="#252E3A"
                                  hoverTextColor="#FFFFFF"
                                  bgColor="#C0FF06"
                                  onClickButton={() => {
                                    closeTab();
                                    setPrivacy(true);
                                  }}
                                  widthButton="w-full"
                                  borderCustom="border border-coalColor text-coalColor"
                                />
                              </div>
                            </Scrollbars>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default PrivacyPolicyModal;
