{"name": "regexp.prototype.flags", "version": "1.5.1", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/RegExp.prototype.flags.git"}, "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "set-function-name": "^2.0.0"}, "devDependencies": {"@es-shims/api": "^2.4.2", "@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "available-regexp-flags": "^1.0.2", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has": "^1.0.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "tape": "^5.6.6"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}