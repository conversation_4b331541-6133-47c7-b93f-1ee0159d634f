import React, { useEffect, useRef } from "react";
import Chart from "chart.js/auto";
import "./chart.css";

const ScoreDistributionChart = ({
  xArray,
  yArray,
  myScore,
  titleGraph,
  xAxisText,
  yAxisText,
  myDatasetText,
}) => {
  const chartContainer = useRef(null);
  useEffect(() => {
    if (chartContainer && chartContainer.current) {
      const ctx = chartContainer.current.getContext("2d");
      const sortedData = xArray
        ?.map((score, index) => ({
          score,
          candidates: yArray[index],
        }))
        .sort((a, b) => a.score - b.score);

      const sortedScores = sortedData?.map((data) => data.score);
      const sortedCandidates = sortedData?.map((data) => data.candidates);

      const data = {
        labels: sortedScores,
        datasets: [
          {
            label: `${myDatasetText}`,
            data: [
              { x: myScore, y: 2 },
              { x: myScore, y: 110 },
            ],
            borderColor: "#FF5812",
            pointBackgroundColor: "#FF5812",
            borderWidth: 4,
            tension: 0,
            fill: true,
            hidden: false,
            borderDash: [5, 5],
            pointRadius: 4,
            pointHoverRadius: 4,
            skipTooltip: true,
          },
          {
            label: `${yAxisText}`,
            data: sortedCandidates,
            pointBackgroundColor: "#FFB500",
            borderColor: "#FFB500",
            pointRadius: 5,
            pointHoverRadius: 5,
            borderWidth: 4,
            tension: 0.6,
          },
        ],
      };

      const options = {
        scales: {
          x: {
            type: "linear",
            min: 0,
            max: 100,
            title: {
              display: true,
              text: `${xAxisText}`,
              font: {
                size: 16,
                weight: "bold",
                family: "Silka",
              },
              color: "black",
            },
            ticks: {
              stepSize: 10,
              font: {
                family: "Silka",
              },
              color: "black",
            },
          },
          y: {
            type: "linear",
            min: 0,
            max: 100,
            ticks: {
              stepSize: 10,
              font: {
                family: "Silka",
              },
              color: "black",
            },
            title: {
              display: true,
              text: `${yAxisText}`,
              font: {
                size: 16,
                weight: "bold",
                family: "Silka",
              },
              color: "black",
            },
            grid: {
              display: true,
              drawOnChartArea: false,
              lineWidth: 1,
              color: "#ddd",
              tick: {
                length: 0,
              },
            },
          },
        },
        plugins: {
          title: {
            display: true,
            text: `${titleGraph}`,
            font: {
              size: 18,
              family: "Archia Semibold",
            },
            color: "black",
          },
          tooltip: {
            callbacks: {
              title: (context) => {
                return "";
              },
              label: (tooltipItem, data) => {
                if (tooltipItem?.datasetIndex === 0) {
                  return `${myDatasetText}` + ":" + tooltipItem?.label;
                } else if (tooltipItem?.datasetIndex === 1) {
                  return [
                    `${yAxisText} : ${tooltipItem?.formattedValue}`,
                    `${xAxisText} : ${tooltipItem?.label}`,
                  ];
                } else {
                  return "";
                }
              },
            },
            displayColors: false,
            label: false,
            backgroundColor: "#FF5812",
          },
          legend: {
            display: true,
            position: "bottom",
            labels: {
              usePointStyle: true,
              pointStyle: "circle",
              radius: 10,
              borderWidth: 2,
              font: {
                size: 14,
                family: "Silka",
              },
              color: "black",
            },
            padding: 20,
            margin: {
              left: 20,
            },
          },
          datalabels: {
            display: true,
            color: "black",
            font: {
              size: 12,
            },
            formatter: function (value, context) {
              // Show quartile labels for x values of 25, 50, 75
              if (
                context.datasetIndex === 0 &&
                [25, 50, 75].includes(context.dataset.data[context.dataIndex].x)
              ) {
                return (
                  "Q" + (context.dataset.data[context.dataIndex].x / 25 + 1)
                );
              } else {
                return ""; // Hide data labels for other points
              }
            },
          },
        },
      };

      const chart = new Chart(ctx, {
        type: "line",
        data: data,
        options: options,
      });
      return () => chart.destroy();
    }
  }, [myScore]);

  return <canvas ref={chartContainer} />;
};

export default ScoreDistributionChart;
