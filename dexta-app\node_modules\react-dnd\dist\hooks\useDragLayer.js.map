{"version": 3, "sources": ["../../src/hooks/useDragLayer.ts"], "sourcesContent": ["import { useEffect } from 'react'\n\nimport type { DragLayerMonitor } from '../types/index.js'\nimport { useCollector } from './useCollector.js'\nimport { useDragDropManager } from './useDragDropManager.js'\n\n/**\n * useDragLayer Hook\n * @param collector The property collector\n */\nexport function useDragLayer<CollectedProps, DragObject = any>(\n\tcollect: (monitor: DragLayerMonitor<DragObject>) => CollectedProps,\n): CollectedProps {\n\tconst dragDropManager = useDragDropManager()\n\tconst monitor = dragDropManager.getMonitor()\n\tconst [collected, updateCollected] = useCollector(monitor, collect)\n\n\tuseEffect(() => monitor.subscribeToOffsetChange(updateCollected))\n\tuseEffect(() => monitor.subscribeToStateChange(updateCollected))\n\treturn collected\n}\n"], "names": ["useEffect", "useCollector", "useDragDropManager", "useDragLayer", "collect", "dragDropManager", "monitor", "getMonitor", "collected", "updateCollected", "subscribeToOffsetChange", "subscribeToStateChange"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO,CAAA;AAGjC,SAASC,YAAY,QAAQ,mBAAmB,CAAA;AAChD,SAASC,kBAAkB,QAAQ,yBAAyB,CAAA;AAE5D;;;GAGG,CACH,OAAO,SAASC,YAAY,CAC3BC,OAAkE,EACjD;IACjB,MAAMC,eAAe,GAAGH,kBAAkB,EAAE;IAC5C,MAAMI,OAAO,GAAGD,eAAe,CAACE,UAAU,EAAE;IAC5C,MAAM,CAACC,SAAS,EAAEC,eAAe,CAAC,GAAGR,YAAY,CAACK,OAAO,EAAEF,OAAO,CAAC;IAEnEJ,SAAS,CAAC,IAAMM,OAAO,CAACI,uBAAuB,CAACD,eAAe,CAAC;IAAA,CAAC;IACjET,SAAS,CAAC,IAAMM,OAAO,CAACK,sBAAsB,CAACF,eAAe,CAAC;IAAA,CAAC;IAChE,OAAOD,SAAS,CAAA;CAChB"}