# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

# [3.3.0](https://github.com/ikatyang-collab/cjk-regex/compare/v3.2.1...v3.3.0) (2025-03-10)

### Features

- add symbols in Katakana block ([#190](https://github.com/ikatyang-collab/cjk-regex/issues/190)) ([c66a3d1](https://github.com/ikatyang-collab/cjk-regex/commit/c66a3d1812c572695e9d436e93ed6c3d8a34940b))

## [3.2.1](https://github.com/ikatyang-collab/cjk-regex/compare/v3.2.0...v3.2.1) (2025-03-08)

# [3.2.0](https://github.com/ikatyang/cjk-regex/compare/v3.1.0...v3.2.0) (2025-03-07)

### Bug Fixes

- add `types` to `exports` ([#186](https://github.com/ikatyang/cjk-regex/issues/186)) ([9f4befa](https://github.com/ikatyang/cjk-regex/commit/9f4befa4ef46ef5a1068b7ec46cfa63b18a1aac1))

### Features

- upgrade `unicode-regex` to support Unicode 16 ([#191](https://github.com/ikatyang/cjk-regex/issues/191)) ([5e5757e](https://github.com/ikatyang/cjk-regex/commit/5e5757e5dd36ad21133c1401133694e1576395ab))

# [3.1.0](https://github.com/ikatyang/cjk-regex/compare/v3.0.0...v3.1.0) (2023-10-21)

### Features

- recognize more CJK punctuations ([#178](https://github.com/ikatyang/cjk-regex/issues/178)) ([547c31a](https://github.com/ikatyang/cjk-regex/commit/547c31a2ec00fb36dfa7c7ed0b1cbf51bb097eb0))

# [3.0.0](https://github.com/ikatyang/cjk-regex/compare/v2.0.1...v3.0.0) (2023-07-09)

### Build System

- update infra ([#183](https://github.com/ikatyang/cjk-regex/issues/183)) ([b89b15d](https://github.com/ikatyang/cjk-regex/commit/b89b15ded8f95d03e3ec4b9ada82c889f07e4608))

### BREAKING CHANGES

- this package is now pure ESM

<a name="2.0.1"></a>

## [2.0.1](https://github.com/ikatyang/cjk-regex/compare/v2.0.0...v2.0.1) (2021-01-07)

### Bug Fixes

- treat IDC as CJK punctuation ([#165](https://github.com/ikatyang/cjk-regex/issues/165)) ([a09c4b7](https://github.com/ikatyang/cjk-regex/commit/a09c4b7))

<a name="2.0.0"></a>

# [2.0.0](https://github.com/ikatyang/cjk-regex/compare/v1.0.2...v2.0.0) (2018-03-22)

### Features

- rewrite to meet w3c typography ([#68](https://github.com/ikatyang/cjk-regex/issues/68)) ([d1ca3f6](https://github.com/ikatyang/cjk-regex/commit/d1ca3f6))

### BREAKING CHANGES

- rename `.characters()` with `.letters()`
- functions are now returning a [Charset](https://github.com/ikatyang/regexp-util#charset)

<a name="1.0.2"></a>

## [1.0.2](https://github.com/ikatyang/cjk-regex/compare/v1.0.1...v1.0.2) (2017-10-14)

### Documentations

- **readme:** fix example ([2b272f4](https://github.com/ikatyang/cjk-regex/commit/2b272f4))
- **description:** replace texts with text ([204b480](https://github.com/ikatyang/cjk-regex/commit/204b480))

<a name="1.0.1"></a>

## [1.0.1](https://github.com/ikatyang/cjk-regex/compare/v1.0.0...v1.0.1) (2017-10-13)

<a name="1.0.0"></a>

# 1.0.0 (2017-10-13)

### Features

- initial implementation ([6cbda70](https://github.com/ikatyang/cjk-regex/commit/6cbda70))
