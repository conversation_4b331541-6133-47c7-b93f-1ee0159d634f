{"name": "intro.js-react", "version": "1.0.0", "description": "Intro.js React Wrapper", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.mts", "default": "./dist/esm/index.mjs"}, "default": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "CJS=true babel --ignore '**/*.test.js' --out-dir dist/cjs --out-file-extension .cjs src && cp dist/cjs/index.cjs dist/cjs/index.js && cp index.d.ts dist/cjs/index.d.cts", "build:esm": "babel --ignore '**/*.test.js' --out-dir dist/esm --out-file-extension .mjs src && cp dist/esm/index.mjs dist/esm/index.js && cp index.d.ts dist/esm/index.d.mts", "build:watch": "npm run build:esm -- --watch", "lint": "eslint src", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage && open coverage/index.html", "test:ci": "jest --coverage", "precommit": "lint-staged", "prepublish": "npm run build"}, "devDependencies": {"@babel/cli": "7.21.0", "@babel/core": "^7.21.4", "@babel/preset-env": "7.21.4", "@babel/preset-react": "7.18.6", "@types/intro.js": "3.0.1", "@types/react": "17.0.4", "babel-eslint": "^7.2.3", "babel-jest": "26.0.1", "babel-plugin-add-import-extension": "^1.6.0", "coveralls": "^3.0.1", "enzyme": "^2.8.2", "eslint": "7.0.0", "eslint-config-airbnb": "^15.0.1", "eslint-config-prettier": "^2.1.1", "eslint-plugin-import": "^2.3.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-jsx-a11y": "^5.0.3", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-react": "^7.0.1", "husky": "^0.13.4", "intro.js": "^2.9.3", "jest": "26.0.1", "lint-staged": "^3.6.0", "prettier": "^1.7.0", "prop-types": "^15.5.10", "react": "^15.5.4", "react-dom": "^15.5.4", "react-test-renderer": "^15.5.4", "rimraf": "^2.6.1"}, "peerDependencies": {"intro.js": ">=2.5.0", "react": ">=0.14.0"}, "repository": {"type": "git", "url": "git+https://github.com/HiDeoo/intro.js-react.git"}, "keywords": ["intro.js", "react", "wrapper"], "author": "HiDeoo", "license": "MIT", "bugs": {"url": "https://github.com/HiDeoo/intro.js-react/issues"}, "homepage": "https://github.com/HiDeoo/intro.js-react#readme", "lint-staged": {"*.js": ["prettier --write", "git add"]}, "jest": {"coverageReporters": ["html", "lcov"]}, "files": ["dist", "index.d.ts"]}