"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 8c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v2h6V8zm-8 0c0-.55-.45-1-1-1H6c-.55 0-1 .45-1 1v2h6V8zm8 4H5c-.55 0-1 .45-1 1v2h16v-2c0-.55-.45-1-1-1z",
  opacity: ".3"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6C4.35 5 3 6.35 3 8v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM13 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2h-6V8zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm15 7H4v-2c0-.55.45-1 1-1h14c.55 0 1 .45 1 1v2z"
}, "1")], 'BedTwoTone');
exports.default = _default;