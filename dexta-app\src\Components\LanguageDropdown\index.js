import React, { useEffect, useState, useRef } from "react";
import { IoIosArrowDown } from "react-icons/io";
import { BiGlobeAlt } from "react-icons/bi";
import { IoCheckmarkCircleOutline } from "react-icons/io5";
import { AiOutlineGlobal } from "react-icons/ai";

const languages = [
  {
    code: "global",
    name: "Global",
    flag: "🌐",
  },
  {
    code: "ar",
    name: "العربية",
    flag: "🇸🇦",
  },
];
export const LanguageDropdown = ({ textSize, iconSize }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
  const dropdownRef = useRef(null);
  const [ipAddress, setIpAddress] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchIP = async () => {
      try {
        // Check if redirection is disabled via URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const isRedirectDisabled =
          urlParams.get("isRedirectDisabled") === "true";

        if (isRedirectDisabled) {
          console.log("Redirection is disabled via URL parameter");
          return; // Skip the redirection logic
        }

        const ipResponse = await fetch("https://api.ipify.org?format=json");
        if (!ipResponse.ok)
          throw new Error(`IP fetch failed: ${ipResponse.status}`);
        const ipData = await ipResponse.json();
        const ip = ipData.ip;

        const geoResponse = await fetch(`https://ipwho.is/${ip}`);
        if (!geoResponse.ok)
          throw new Error(`Geo fetch failed: ${geoResponse.status}`);
        const geoData = await geoResponse.json();

        if (geoData.success) {
          console.log("Country:", geoData.country);
          if (geoData.country === "Saudi Arabia") {
            if (window.location.href.includes("ksa")) return;
            else {
              window.location.href = "https://ksa.app.dexta.io";
            }
          } else {
            if (window.location.href.includes("ksa")) {
              window.location.href = "https://app.dexta.io";
            }
          }
        } else {
          throw new Error("Failed to fetch geo data.");
        }
      } catch (error) {
        console.error(error);
      }
    };

    fetchIP();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const toggleDropdown = () => setIsOpen(!isOpen);
  const selectLanguage = (language) => {
    setSelectedLanguage(language);
    setIsOpen(false);
  };

  console.log(
    window.location.href.includes("ksa"),
    window.location.href,
    "checking logs"
  );
  useEffect(() => {
    if (window.location.href.includes("ksa")) {
      setSelectedLanguage(languages[1]);
    } else {
      setSelectedLanguage(languages[0]);
    }
  }, [window.location.pathname]);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className="flex items-center justify-center gap-1 my-auto px-2 py-2 rounded-lg hover:bg-gray-100 transition-colors"
        onClick={toggleDropdown}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <p className={`${selectedLanguage.code === "ar" && "my-auto"} mr-2`}>
          {selectedLanguage.flag}
        </p>
        <span
          className={`${
            textSize ? textSize : "text-[13px]"
          } flex items-center leading-none mr-1`}
          style={{ fontFamily: "Archia Semibold" }}
        >
          {selectedLanguage.name}
        </span>
        <IoIosArrowDown
          size={16}
          className={`text-gray-500 flex-shrink-0 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
          <div className="py-1">
            {languages.map((language) => (
              <button
                key={language.code}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => {
                  if (language.code === "ar") {
                    window.location.href = "https://ksa.app.dexta.io";
                  } else {
                    window.location.href = "https://app.dexta.io";
                  }
                }}
                style={{ fontFamily: "Silka" }}
              >
                <span className="mr-2">{language.flag}</span>
                <span className="flex-grow text-left">{language.name}</span>
                {selectedLanguage.code === language.code && (
                  <IoCheckmarkCircleOutline
                    size={16}
                    className="text-blue-500"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
