{"version": 3, "file": "PhoneInput.js", "names": ["createPhoneInput", "defaultMetadata", "PhoneInput", "ref", "metadata", "rest", "PhoneInput_", "React", "forwardRef", "propTypes", "metadataType"], "sources": ["../../source/react-hook-form/PhoneInput.js"], "sourcesContent": ["import React from 'react'\r\n\r\nimport ReactHookFormInput from './ReactHookFormInput.js'\r\nimport PhoneInput_ from '../PhoneInputBrowser.js'\r\n\r\nimport { metadata as metadataType } from '../PropTypes.js'\r\n\r\nexport function createPhoneInput(defaultMetadata) {\r\n  let PhoneInput = ({\r\n    metadata = defaultMetadata,\r\n    ...rest\r\n  }, ref) => {\r\n    return (\r\n      <ReactHookFormInput\r\n        {...rest}\r\n        ref={ref}\r\n        metadata={metadata}\r\n        Component={PhoneInput_}\r\n      />\r\n    )\r\n  }\r\n\r\n  PhoneInput = React.forwardRef(PhoneInput)\r\n\r\n  PhoneInput.propTypes = {\r\n    metadata: metadataType\r\n  }\r\n\r\n  return PhoneInput\r\n}\r\n\r\nexport default createPhoneInput()"], "mappings": ";;;;;;;;AAAA;;AAEA;;AACA;;AAEA;;;;;;;;;;;;AAEO,SAASA,gBAAT,CAA0BC,eAA1B,EAA2C;EAChD,IAAIC,UAAU,GAAG,0BAGdC,GAHc,EAGN;IAAA,yBAFTC,QAES;IAAA,IAFTA,QAES,8BAFEH,eAEF;IAAA,IADNI,IACM;;IACT,oBACE,gCAAC,8BAAD,eACMA,IADN;MAEE,GAAG,EAAEF,GAFP;MAGE,QAAQ,EAAEC,QAHZ;MAIE,SAAS,EAAEE;IAJb,GADF;EAQD,CAZD;;EAcAJ,UAAU,gBAAGK,iBAAA,CAAMC,UAAN,CAAiBN,UAAjB,CAAb;EAEAA,UAAU,CAACO,SAAX,GAAuB;IACrBL,QAAQ,EAAEM;EADW,CAAvB;EAIA,OAAOR,UAAP;AACD;;eAEcF,gBAAgB,E"}