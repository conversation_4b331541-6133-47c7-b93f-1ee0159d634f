{"name": "ip-address", "description": "A library for parsing IPv4 and IPv6 IP addresses in node and the browser.", "keywords": ["ipv6", "ipv4", "browser", "validation"], "version": "6.4.0", "author": "<PERSON> <<EMAIL>> (https://beaugunderson.com/)", "license": "MIT", "main": "ip-address.js", "scripts": {"docs": "documentation build --github --output docs --format html ./ip-address.js", "release": "release-it", "test": "mocha -R spec"}, "engines": {"node": ">= 0.10"}, "files": ["ip-address.js", "ip-address-globals.js", "lib/**/*"], "repository": {"type": "git", "url": "git://github.com/beaugunderson/ip-address.git"}, "dependencies": {"jsbn": "1.1.0", "lodash.find": "4.6.0", "lodash.max": "4.0.1", "lodash.merge": "4.6.2", "lodash.padstart": "4.6.1", "lodash.repeat": "4.1.0", "sprintf-js": "1.1.2"}, "devDependencies": {"browserify": "^16.5.2", "chai": "^4.2.0", "codecov.io": "^0.1.6", "documentation": "^13.0.2", "istanbul": "^0.4.5", "mocha": "^8.1.3", "mochify": "^6.6.0", "release-it": "^14.0.2"}}