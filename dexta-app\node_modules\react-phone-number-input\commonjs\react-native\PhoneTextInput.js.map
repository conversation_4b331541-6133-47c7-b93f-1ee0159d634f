{"version": 3, "file": "PhoneTextInput.js", "names": ["PhoneTextInput", "ref", "onChange", "autoCompleteType", "TextInputComponent", "TextInput", "rest", "onChangeText", "useCallback", "value", "preventDefault", "defaultPrevented", "target", "React", "forwardRef", "propTypes", "PropTypes", "string", "func", "isRequired", "elementType"], "sources": ["../../source/react-native/PhoneTextInput.js"], "sourcesContent": ["import React, { useCallback } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport { TextInput } from 'react-native'\r\n\r\n/**\r\n * This is an _experimental_ React Native component.\r\n * Feedback thread: https://github.com/catamphetamine/react-phone-number-input/issues/296\r\n */\r\nfunction PhoneTextInput({\r\n  onChange,\r\n  // By default, shows phone number suggestion(s) when the user focuses the input field.\r\n  autoCompleteType = 'tel',\r\n  // By default, uses the default React Native `TextInput` component.\r\n  TextInputComponent = TextInput,\r\n  ...rest\r\n}, ref) {\r\n  // Instead of `onChangeText(value: string)` it could use\r\n  // `onChange(nativeEvent: Event)` and get `value` from `nativeEvent.text`.\r\n  const onChangeText = useCallback((value) => {\r\n    onChange({\r\n      preventDefault() { this.defaultPrevented = true },\r\n      target: { value }\r\n    })\r\n  }, [onChange])\r\n\r\n  // React Native `<TextInput/>` supports properties:\r\n  // * `placeholder: string?`\r\n  // * `autoFocus: boolean?`\r\n  // * `value: string?`\r\n  // plus the ones mentioned below:\r\n  return (\r\n    <TextInputComponent\r\n      ref={ref}\r\n      autoCompleteType={autoCompleteType}\r\n      keyboardType=\"phone-pad\"\r\n      onChangeText={onChangeText}\r\n      {...rest}\r\n    />\r\n  )\r\n}\r\n\r\nPhoneTextInput = React.forwardRef(PhoneTextInput)\r\n\r\nPhoneTextInput.propTypes = {\r\n  /**\r\n   * The input field `value: string`.\r\n   */\r\n  value: PropTypes.string,\r\n\r\n  /**\r\n   * A function of `event: Event`.\r\n   * Updates the `value: string` property.\r\n   */\r\n  onChange: PropTypes.func.isRequired,\r\n\r\n  /**\r\n   * The standard `autoCompleteType` property of a React Native `<TextInput/>`.\r\n   */\r\n  autoCompleteType: PropTypes.string,\r\n\r\n  /**\r\n   * The input field component.\r\n   */\r\n  TextInputComponent: PropTypes.elementType\r\n}\r\n\r\nexport default PhoneTextInput\r\n"], "mappings": ";;;;;;;;;AAAA;;AACA;;AACA;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA,SAASA,cAAT,OAOGC,GAPH,EAOQ;EAAA,IANNC,QAMM,QANNA,QAMM;EAAA,iCAJNC,gBAIM;EAAA,IAJNA,gBAIM,sCAJa,KAIb;EAAA,iCAFNC,kBAEM;EAAA,IAFNA,kBAEM,sCAFeC,sBAEf;EAAA,IADHC,IACG;;EACN;EACA;EACA,IAAMC,YAAY,GAAG,IAAAC,kBAAA,EAAY,UAACC,KAAD,EAAW;IAC1CP,QAAQ,CAAC;MACPQ,cADO,4BACU;QAAE,KAAKC,gBAAL,GAAwB,IAAxB;MAA8B,CAD1C;MAEPC,MAAM,EAAE;QAAEH,KAAK,EAALA;MAAF;IAFD,CAAD,CAAR;EAID,CALoB,EAKlB,CAACP,QAAD,CALkB,CAArB,CAHM,CAUN;EACA;EACA;EACA;EACA;;EACA,oBACE,gCAAC,kBAAD;IACE,GAAG,EAAED,GADP;IAEE,gBAAgB,EAAEE,gBAFpB;IAGE,YAAY,EAAC,WAHf;IAIE,YAAY,EAAEI;EAJhB,GAKMD,IALN,EADF;AASD;;AAEDN,cAAc,gBAAGa,iBAAA,CAAMC,UAAN,CAAiBd,cAAjB,CAAjB;AAEAA,cAAc,CAACe,SAAf,GAA2B;EACzB;AACF;AACA;EACEN,KAAK,EAAEO,qBAAA,CAAUC,MAJQ;;EAMzB;AACF;AACA;AACA;EACEf,QAAQ,EAAEc,qBAAA,CAAUE,IAAV,CAAeC,UAVA;;EAYzB;AACF;AACA;EACEhB,gBAAgB,EAAEa,qBAAA,CAAUC,MAfH;;EAiBzB;AACF;AACA;EACEb,kBAAkB,EAAEY,qBAAA,CAAUI;AApBL,CAA3B;eAuBepB,c"}