
name: standard-ci

on:
  pull_request:
    paths-ignore:
      - '**.md'
  push:
    branches:
      - master
    paths-ignore:
      - '**.md'

jobs:
  test:
    strategy:
      matrix:
        node: [14, 16, 18]
        os: [ubuntu-latest, windows-latest, macos-latest]
    name: Node v${{ matrix.node }} - ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v3

    - uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Test
      run: npm test

    - name: Lint
      run: npm run lint

    - name: Build
      run: npm run build
