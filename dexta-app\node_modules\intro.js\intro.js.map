{"version": 3, "file": "intro.js", "sources": ["../src/util/cookie.ts", "../src/core/dontShowAgain.ts", "../src/util/stamp.ts", "../src/core/DOMEvent.ts", "../src/util/isFunction.ts", "../src/util/addClass.ts", "../src/util/getPropValue.ts", "../src/util/setShowElement.ts", "../src/util/scrollParentToElement.ts", "../src/util/getScrollParent.ts", "../src/util/getWindowSize.ts", "../src/util/scrollTo.ts", "../src/util/elementInViewport.ts", "../src/util/setAnchorAsButton.ts", "../src/util/isFixed.ts", "../src/util/getOffset.ts", "../src/util/removeClass.ts", "../src/util/setStyle.ts", "../src/core/setHelperLayerPosition.ts", "../src/util/checkRight.ts", "../src/util/checkLeft.ts", "../src/util/removeEntry.ts", "../src/core/placeTooltip.ts", "../src/core/removeShowElement.ts", "../src/util/createElement.ts", "../src/util/appendChild.ts", "../src/core/showElement.ts", "../src/core/steps.ts", "../src/core/onKeyDown.ts", "../src/util/cloneObject.ts", "../src/util/debounce.ts", "../src/core/hint.ts", "../src/core/fetchIntroSteps.ts", "../src/core/refresh.ts", "../src/core/onResize.ts", "../src/util/removeChild.ts", "../src/core/exitIntro.ts", "../src/core/addOverlayLayer.ts", "../src/core/introForElement.ts", "../src/option.ts", "../src/intro.ts", "../src/index.ts"], "sourcesContent": ["export function setCookie(name: string, value: string, days?: number) {\n  const cookie: {\n    [name: string]: string | undefined;\n    path: string;\n    expires: string | undefined;\n  } = { [name]: value, path: \"/\", expires: undefined };\n\n  if (days) {\n    let date = new Date();\n    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);\n    cookie.expires = date.toUTCString();\n  }\n\n  let arr = [];\n  for (let key in cookie) {\n    arr.push(`${key}=${cookie[key]}`);\n  }\n\n  document.cookie = arr.join(\"; \");\n\n  return getCookie(name);\n}\n\nexport function getAllCookies() {\n  let cookie: { [name: string]: string } = {};\n\n  document.cookie.split(\";\").forEach((el) => {\n    let [k, v] = el.split(\"=\");\n    cookie[k.trim()] = v;\n  });\n\n  return cookie;\n}\n\nexport function getCookie(name: string) {\n  return getAllCookies()[name];\n}\n\nexport function deleteCookie(name: string) {\n  setCookie(name, \"\", -1);\n}\n", "import { IntroJs } from \"../intro\";\nimport { deleteCookie, getCookie, setCookie } from \"../util/cookie\";\n\nconst dontShowAgainCookieValue = \"true\";\n\n/**\n * Set the \"Don't show again\" state\n *\n * @api private\n */\nexport function setDontShowAgain(intro: IntroJs, dontShowAgain: boolean) {\n  if (dontShowAgain) {\n    setCookie(\n      intro._options.dontShowAgainCookie,\n      dontShowAgainCookieValue,\n      intro._options.dontShowAgainCookieDays\n    );\n  } else {\n    deleteCookie(intro._options.dontShowAgainCookie);\n  }\n}\n\n/**\n * Get the \"Don't show again\" state from cookies\n *\n * @api private\n */\nexport function getDontShowAgain(intro: IntroJs): boolean {\n  const dontShowCookie = getCookie(intro._options.dontShowAgainCookie);\n  return dontShowCookie !== \"\" && dontShowCookie === dontShowAgainCookieValue;\n}\n", "/**\n * Mark any object with an incrementing number\n * used for keeping track of objects\n *\n * @param Object obj   Any object or DOM Element\n * @param String key\n * @return Object\n */\nconst stamp = (() => {\n  const keys: {\n    [key: string]: number;\n  } = {};\n  return function stamp<T>(obj: T, key = \"introjs-stamp\"): number {\n    // each group increments from 0\n    keys[key] = keys[key] || 0;\n\n    // stamp only once per object\n    // @ts-ignore\n    if (obj[key] === undefined) {\n      // increment key for each new object\n      // @ts-ignore\n      obj[key] = keys[key]++;\n    }\n\n    // @ts-ignore\n    return obj[key];\n  };\n})();\n\nexport default stamp;\n", "import { IntroJs } from \"../intro\";\nimport stamp from \"../util/stamp\";\n\n/**\n * DOMEvent Handles all DOM events\n *\n * methods:\n *\n * on - add event handler\n * off - remove event\n */\n\nclass DOMEvent {\n  private readonly events_key: string = \"introjs_event\";\n\n  /**\n   * Gets a unique ID for an event listener\n   */\n  private _id(type: string, listener: Function, context: IntroJs) {\n    return type + stamp(listener) + (context ? `_${stamp(context)}` : \"\");\n  }\n\n  /**\n   * Adds event listener\n   */\n  public on(\n    obj: EventTarget,\n    type: string,\n    listener: (\n      context: IntroJs | EventTarget,\n      e: Event\n    ) => void | undefined | string | Promise<string | void>,\n    context: IntroJs,\n    useCapture: boolean\n  ) {\n    const id = this._id(type, listener, context);\n    const handler = (e: Event) => listener(context || obj, e || window.event);\n\n    if (\"addEventListener\" in obj) {\n      obj.addEventListener(type, handler, useCapture);\n    } else if (\"attachEvent\" in obj) {\n      // @ts-ignore\n      obj.attachEvent(`on${type}`, handler);\n    }\n\n    // @ts-ignore\n    obj[this.events_key] = obj[this.events_key] || {};\n    // @ts-ignore\n    obj[this.events_key][id] = handler;\n  }\n\n  /**\n   * Removes event listener\n   */\n  public off(\n    obj: EventTarget,\n    type: string,\n    listener: (\n      context: IntroJs | EventTarget,\n      e: Event\n    ) => void | undefined | string | Promise<string | void>,\n    context: IntroJs,\n    useCapture: boolean\n  ) {\n    const id = this._id(type, listener, context);\n    // @ts-ignore\n    const handler = obj[this.events_key] && obj[this.events_key][id];\n\n    if (!handler) {\n      return;\n    }\n\n    if (\"removeEventListener\" in obj) {\n      obj.removeEventListener(type, handler, useCapture);\n    } else if (\"detachEvent\" in obj) {\n      // @ts-ignore\n      obj.detachEvent(`on${type}`, handler);\n    }\n\n    // @ts-ignore\n    obj[this.events_key][id] = null;\n  }\n}\n\nexport default new DOMEvent();\n", "// Returns true if the given parameter is a function\nexport default (x: any): x is Function => typeof x === \"function\";\n", "/**\n * Append a class to an element\n * @api private\n */\nexport default function addClass(element: HTMLElement, className: string) {\n  if (element instanceof SVGElement) {\n    // svg\n    const pre = element.getAttribute(\"class\") || \"\";\n\n    if (!pre.match(className)) {\n      // check if element doesn't already have className\n      element.setAttribute(\"class\", `${pre} ${className}`);\n    }\n  } else {\n    if (element.classList !== undefined) {\n      // check for modern classList property\n      const classes = className.split(\" \");\n      for (const cls of classes) {\n        element.classList.add(cls);\n      }\n    } else if (!element.className.match(className)) {\n      // check if element doesn't already have className\n      element.className += ` ${className}`;\n    }\n  }\n}\n", "/**\n * Get an element CSS property on the page\n * Thanks to JavaScript Kit: http://www.javascriptkit.com/dhtmltutors/dhtmlcascade4.shtml\n *\n * @api private\n * @returns string property value\n */\nexport default function getPropValue(\n  element: HTMLElement,\n  propName: string\n): string {\n  let propValue = \"\";\n  if (\"currentStyle\" in element) {\n    //IE\n    // @ts-ignore\n    propValue = element.currentStyle[propName];\n  } else if (document.defaultView && document.defaultView.getComputedStyle) {\n    //Others\n    propValue = document.defaultView\n      .getComputedStyle(element, null)\n      .getPropertyValue(propName);\n  }\n\n  //Prevent exception in IE\n  if (propValue && propValue.toLowerCase) {\n    return propValue.toLowerCase();\n  } else {\n    return propValue;\n  }\n}\n", "import addClass from \"./addClass\";\nimport getPropValue from \"./getPropValue\";\n\n/**\n * To set the show element\n * This function set a relative (in most cases) position and changes the z-index\n *\n * @api private\n */\nexport default function setShowElement(targetElement: HTMLElement) {\n  addClass(targetElement, \"introjs-showElement\");\n\n  const currentElementPosition = getPropValue(targetElement, \"position\");\n  if (\n    currentElementPosition !== \"absolute\" &&\n    currentElementPosition !== \"relative\" &&\n    currentElementPosition !== \"sticky\" &&\n    currentElementPosition !== \"fixed\"\n  ) {\n    //change to new intro item\n    addClass(targetElement, \"introjs-relativePosition\");\n  }\n}\n", "import getScrollParent from \"./getScrollParent\";\n\n/**\n * scroll a scrollable element to a child element\n */\nexport default function scrollParentToElement(\n  scrollToElement: boolean,\n  targetElement: HTMLElement\n) {\n  if (!scrollToElement) return;\n\n  const parent = getScrollParent(targetElement);\n\n  if (parent === document.body) return;\n\n  parent.scrollTop = targetElement.offsetTop - parent.offsetTop;\n}\n", "/**\n * Find the nearest scrollable parent\n * copied from https://stackoverflow.com/questions/35939886/find-first-scrollable-parent\n */\nexport default function getScrollParent(element: HTMLElement): HTMLElement {\n  let style = window.getComputedStyle(element);\n  const excludeStaticParent = style.position === \"absolute\";\n  const overflowRegex = /(auto|scroll)/;\n\n  if (style.position === \"fixed\") return document.body;\n\n  for (\n    let parent: HTMLElement | null = element;\n    (parent = parent.parentElement);\n\n  ) {\n    style = window.getComputedStyle(parent);\n    if (excludeStaticParent && style.position === \"static\") {\n      continue;\n    }\n    if (overflowRegex.test(style.overflow + style.overflowY + style.overflowX))\n      return parent;\n  }\n\n  return document.body;\n}\n", "/**\n * Provides a cross-browser way to get the screen dimensions\n * via: http://stackoverflow.com/questions/5864467/internet-explorer-innerheight\n *\n * @api private\n */\nexport default function getWinSize(): { width: number; height: number } {\n  if (window.innerWidth !== undefined) {\n    return { width: window.innerWidth, height: window.innerHeight };\n  } else {\n    const D = document.documentElement;\n    return { width: D.clientWidth, height: D.clientHeight };\n  }\n}\n", "import elementInViewport from \"./elementInViewport\";\nimport getWindowSize from \"./getWindowSize\";\nimport { ScrollTo } from \"../core/steps\";\n\n/**\n * To change the scroll of `window` after highlighting an element\n *\n * @api private\n */\nexport default function scrollTo(\n  scrollToElement: boolean,\n  scrollTo: ScrollTo,\n  scrollPadding: number,\n  targetElement: HTMLElement,\n  tooltipLayer: HTMLElement\n) {\n  if (scrollTo === \"off\") return;\n  let rect: DOMRect;\n\n  if (!scrollToElement) return;\n\n  if (scrollTo === \"tooltip\") {\n    rect = tooltipLayer.getBoundingClientRect();\n  } else {\n    rect = targetElement.getBoundingClientRect();\n  }\n\n  if (!elementInViewport(targetElement)) {\n    const winHeight = getWindowSize().height;\n    const top = rect.bottom - (rect.bottom - rect.top);\n\n    // TODO (afshinm): do we need scroll padding now?\n    // I have changed the scroll option and now it scrolls the window to\n    // the center of the target element or tooltip.\n\n    if (top < 0 || targetElement.clientHeight > winHeight) {\n      window.scrollBy(\n        0,\n        rect.top - (winHeight / 2 - rect.height / 2) - scrollPadding\n      ); // 30px padding from edge to look nice\n\n      //Scroll down\n    } else {\n      window.scrollBy(\n        0,\n        rect.top - (winHeight / 2 - rect.height / 2) + scrollPadding\n      ); // 30px padding from edge to look nice\n    }\n  }\n}\n", "/**\n * Check to see if the element is in the viewport or not\n * http://stackoverflow.com/questions/123999/how-to-tell-if-a-dom-element-is-visible-in-the-current-viewport\n *\n * @api private\n */\nexport default function elementInViewport(el: HTMLElement): boolean {\n  const rect = el.getBoundingClientRect();\n\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom + 80 <= window.innerHeight && // add 80 to get the text right\n    rect.right <= window.innerWidth\n  );\n}\n", "/**\n * Setting anchors to behave like buttons\n *\n * @api private\n */\nexport default function setAnchorAsButton(anchor: HTMLElement) {\n  anchor.setAttribute(\"role\", \"button\");\n  anchor.tabIndex = 0;\n}\n", "import getPropValue from \"./getPropValue\";\n\n/**\n * Checks to see if target element (or parents) position is fixed or not\n *\n * @api private\n */\nexport default function isFixed(element: HTMLElement): boolean {\n  const parent = element.parentElement;\n\n  if (!parent || parent.nodeName === \"HTML\") {\n    return false;\n  }\n\n  if (getPropValue(element, \"position\") === \"fixed\") {\n    return true;\n  }\n\n  return isFixed(parent);\n}\n", "import getPropValue from \"./getPropValue\";\nimport isFixed from \"./isFixed\";\n\n/**\n * Get an element position on the page relative to another element (or body)\n * Thanks to `meouw`: http://stackoverflow.com/a/442474/375966\n *\n * @api private\n * @returns Element's position info\n */\nexport default function getOffset(\n  element: HTMLElement,\n  relativeEl?: HTMLElement\n): { width: number; height: number; left: number; top: number } {\n  const body = document.body;\n  const docEl = document.documentElement;\n  const scrollTop = window.pageYOffset || docEl.scrollTop || body.scrollTop;\n  const scrollLeft = window.pageXOffset || docEl.scrollLeft || body.scrollLeft;\n\n  relativeEl = relativeEl || body;\n\n  const x = element.getBoundingClientRect();\n  const xr = relativeEl.getBoundingClientRect();\n  const relativeElPosition = getPropValue(relativeEl, \"position\");\n\n  let obj = {\n    width: x.width,\n    height: x.height,\n  };\n\n  if (\n    (relativeEl.tagName.toLowerCase() !== \"body\" &&\n      relativeElPosition === \"relative\") ||\n    relativeElPosition === \"sticky\"\n  ) {\n    // when the container of our target element is _not_ body and has either \"relative\" or \"sticky\" position, we should not\n    // consider the scroll position but we need to include the relative x/y of the container element\n    return Object.assign(obj, {\n      top: x.top - xr.top,\n      left: x.left - xr.left,\n    });\n  } else {\n    if (isFixed(element)) {\n      return Object.assign(obj, {\n        top: x.top,\n        left: x.left,\n      });\n    } else {\n      return Object.assign(obj, {\n        top: x.top + scrollTop,\n        left: x.left + scrollLeft,\n      });\n    }\n  }\n}\n", "/**\n * Remove a class from an element\n *\n * @api private\n */\nexport default function removeClass(\n  element: HTMLElement,\n  classNameRegex: RegExp | string\n) {\n  if (element instanceof SVGElement) {\n    const pre = element.getAttribute(\"class\") || \"\";\n\n    element.setAttribute(\n      \"class\",\n      pre.replace(classNameRegex, \"\").replace(/^\\s+|\\s+$/g, \"\")\n    );\n  } else {\n    element.className = element.className\n      .replace(classNameRegex, \"\")\n      .replace(/^\\s+|\\s+$/g, \"\");\n  }\n}\n", "/**\n * Sets the style of an DOM element\n */\nexport default function setStyle(\n  element: HTMLElement,\n  style: string | { [key: string]: string | number }\n) {\n  let cssText = \"\";\n\n  if (element.style.cssText) {\n    cssText += element.style.cssText;\n  }\n\n  if (typeof style === \"string\") {\n    cssText += style;\n  } else {\n    for (const rule in style) {\n      cssText += `${rule}:${style[rule]};`;\n    }\n  }\n\n  element.style.cssText = cssText;\n}\n", "import getOffset from \"../util/getOffset\";\nimport isFixed from \"../util/isFixed\";\nimport addClass from \"../util/addClass\";\nimport removeClass from \"../util/removeClass\";\nimport setStyle from \"../util/setStyle\";\nimport { IntroJs } from \"../intro\";\nimport { HintStep, IntroStep } from \"./steps\";\n\n/**\n * Update the position of the helper layer on the screen\n *\n * @api private\n */\nexport default function setHelperLayerPosition(\n  intro: IntroJs,\n  step: IntroStep | HintStep,\n  helperLayer: HTMLElement\n) {\n  if (!helperLayer || !step) return;\n\n  const elementPosition = getOffset(\n    step.element as HTMLElement,\n    intro._targetElement\n  );\n  let widthHeightPadding = intro._options.helperElementPadding;\n\n  // If the target element is fixed, the tooltip should be fixed as well.\n  // Otherwise, remove a fixed class that may be left over from the previous\n  // step.\n  if (step.element instanceof Element && isFixed(step.element)) {\n    addClass(helperLayer, \"introjs-fixedTooltip\");\n  } else {\n    removeClass(helperLayer, \"introjs-fixedTooltip\");\n  }\n\n  if (step.position === \"floating\") {\n    widthHeightPadding = 0;\n  }\n\n  //set new position to helper layer\n  setStyle(helperLayer, {\n    width: `${elementPosition.width + widthHeightPadding}px`,\n    height: `${elementPosition.height + widthHeightPadding}px`,\n    top: `${elementPosition.top - widthHeightPadding / 2}px`,\n    left: `${elementPosition.left - widthHeightPadding / 2}px`,\n  });\n}\n", "/**\n * Set tooltip left so it doesn't go off the right side of the window\n *\n * @return boolean true, if tooltipLayerStyleLeft is ok.  false, otherwise.\n */\nexport default function checkRight(\n  targetOffset: {\n    top: number;\n    left: number;\n    width: number;\n    height: number;\n  },\n  tooltipLayerStyleLeft: number,\n  tooltipOffset: {\n    top: number;\n    left: number;\n    width: number;\n    height: number;\n  },\n  windowSize: {\n    width: number;\n    height: number;\n  },\n  tooltipLayer: HTMLElement\n): boolean {\n  if (\n    targetOffset.left + tooltipLayerStyleLeft + tooltipOffset.width >\n    windowSize.width\n  ) {\n    // off the right side of the window\n    tooltipLayer.style.left = `${\n      windowSize.width - tooltipOffset.width - targetOffset.left\n    }px`;\n\n    return false;\n  }\n\n  tooltipLayer.style.left = `${tooltipLayerStyleLeft}px`;\n  return true;\n}\n", "/**\n * Set tooltip right so it doesn't go off the left side of the window\n *\n * @return boolean true, if tooltipLayerStyleRight is ok. false, otherwise.\n */\nexport default function checkLeft(\n  targetOffset: {\n    top: number;\n    left: number;\n    width: number;\n    height: number;\n  },\n  tooltipLayerStyleRight: number,\n  tooltipOffset: {\n    top: number;\n    left: number;\n    width: number;\n    height: number;\n  },\n  tooltipLayer: HTMLElement\n): boolean {\n  if (\n    targetOffset.left +\n      targetOffset.width -\n      tooltipLayerStyleRight -\n      tooltipOffset.width <\n    0\n  ) {\n    // off the left side of the window\n    tooltipLayer.style.left = `${-targetOffset.left}px`;\n    return false;\n  }\n  tooltipLayer.style.right = `${tooltipLayerStyleRight}px`;\n  return true;\n}\n", "/**\n * Remove an entry from a string array if it's there, does nothing if it isn't there.\n */\nexport default function removeEntry<K>(stringArray: K[], stringToRemove: K) {\n  if (stringArray.includes(stringToRemove)) {\n    stringArray.splice(stringArray.indexOf(stringToRemove), 1);\n  }\n}\n", "import getOffset from \"../util/getOffset\";\nimport getWindowSize from \"../util/getWindowSize\";\nimport addClass from \"../util/addClass\";\nimport checkRight from \"../util/checkRight\";\nimport checkLeft from \"../util/checkLeft\";\nimport removeEntry from \"../util/removeEntry\";\nimport { HintStep, IntroStep, TooltipPosition } from \"./steps\";\nimport { IntroJs } from \"../intro\";\n\n/**\n * auto-determine alignment\n */\nfunction _determineAutoAlignment(\n  offsetLeft: number,\n  tooltipWidth: number,\n  windowWidth: number,\n  desiredAlignment: TooltipPosition[]\n): TooltipPosition | null {\n  const halfTooltipWidth = tooltipWidth / 2;\n  const winWidth = Math.min(windowWidth, window.screen.width);\n\n  // valid left must be at least a tooltipWidth\n  // away from right side\n  if (winWidth - offsetLeft < tooltipWidth) {\n    removeEntry<TooltipPosition>(desiredAlignment, \"top-left-aligned\");\n    removeEntry<TooltipPosition>(desiredAlignment, \"bottom-left-aligned\");\n  }\n\n  // valid middle must be at least half\n  // width away from both sides\n  if (\n    offsetLeft < halfTooltipWidth ||\n    winWidth - offsetLeft < halfTooltipWidth\n  ) {\n    removeEntry<TooltipPosition>(desiredAlignment, \"top-middle-aligned\");\n    removeEntry<TooltipPosition>(desiredAlignment, \"bottom-middle-aligned\");\n  }\n\n  // valid right must be at least a tooltipWidth\n  // width away from left side\n  if (offsetLeft < tooltipWidth) {\n    removeEntry<TooltipPosition>(desiredAlignment, \"top-right-aligned\");\n    removeEntry<TooltipPosition>(desiredAlignment, \"bottom-right-aligned\");\n  }\n\n  if (desiredAlignment.length) {\n    return desiredAlignment[0];\n  }\n\n  return null;\n}\n\n/**\n * Determines the position of the tooltip based on the position precedence and availability\n * of screen space.\n */\nfunction _determineAutoPosition(\n  positionPrecedence: TooltipPosition[],\n  targetElement: HTMLElement,\n  tooltipLayer: HTMLElement,\n  desiredTooltipPosition: TooltipPosition\n): TooltipPosition {\n  // Take a clone of position precedence. These will be the available\n  const possiblePositions = positionPrecedence.slice();\n\n  const windowSize = getWindowSize();\n  const tooltipHeight = getOffset(tooltipLayer).height + 10;\n  const tooltipWidth = getOffset(tooltipLayer).width + 20;\n  const targetElementRect = targetElement.getBoundingClientRect();\n\n  // If we check all the possible areas, and there are no valid places for the tooltip, the element\n  // must take up most of the screen real estate. Show the tooltip floating in the middle of the screen.\n  let calculatedPosition: TooltipPosition = \"floating\";\n\n  /*\n   * auto determine position\n   */\n\n  // Check for space below\n  if (targetElementRect.bottom + tooltipHeight > windowSize.height) {\n    removeEntry<TooltipPosition>(possiblePositions, \"bottom\");\n  }\n\n  // Check for space above\n  if (targetElementRect.top - tooltipHeight < 0) {\n    removeEntry<TooltipPosition>(possiblePositions, \"top\");\n  }\n\n  // Check for space to the right\n  if (targetElementRect.right + tooltipWidth > windowSize.width) {\n    removeEntry<TooltipPosition>(possiblePositions, \"right\");\n  }\n\n  // Check for space to the left\n  if (targetElementRect.left - tooltipWidth < 0) {\n    removeEntry<TooltipPosition>(possiblePositions, \"left\");\n  }\n\n  // strip alignment from position\n  if (desiredTooltipPosition) {\n    // ex: \"bottom-right-aligned\"\n    // should return 'bottom'\n    desiredTooltipPosition = desiredTooltipPosition.split(\n      \"-\"\n    )[0] as TooltipPosition;\n  }\n\n  if (possiblePositions.length) {\n    // Pick the first valid position, in order\n    calculatedPosition = possiblePositions[0];\n\n    if (possiblePositions.includes(desiredTooltipPosition)) {\n      // If the requested position is in the list, choose that\n      calculatedPosition = desiredTooltipPosition;\n    }\n  }\n\n  // only \"top\" and \"bottom\" positions have optional alignments\n  if (calculatedPosition === \"top\" || calculatedPosition === \"bottom\") {\n    let defaultAlignment: TooltipPosition;\n    let desiredAlignment: TooltipPosition[] = [];\n\n    if (calculatedPosition === \"top\") {\n      // if screen width is too small\n      // for ANY alignment, middle is\n      // probably the best for visibility\n      defaultAlignment = \"top-middle-aligned\";\n\n      desiredAlignment = [\n        \"top-left-aligned\",\n        \"top-middle-aligned\",\n        \"top-right-aligned\",\n      ];\n    } else {\n      defaultAlignment = \"bottom-middle-aligned\";\n\n      desiredAlignment = [\n        \"bottom-left-aligned\",\n        \"bottom-middle-aligned\",\n        \"bottom-right-aligned\",\n      ];\n    }\n\n    calculatedPosition =\n      _determineAutoAlignment(\n        targetElementRect.left,\n        tooltipWidth,\n        windowSize.width,\n        desiredAlignment\n      ) || defaultAlignment;\n  }\n\n  return calculatedPosition;\n}\n\n/**\n * Render tooltip box in the page\n *\n * @api private\n */\nexport default function placeTooltip(\n  intro: IntroJs,\n  currentStep: IntroStep | HintStep,\n  tooltipLayer: HTMLElement,\n  arrowLayer: HTMLElement,\n  hintMode: boolean = false\n) {\n  if (!currentStep) return;\n\n  let tooltipCssClass = \"\";\n  let tooltipOffset: {\n    top: number;\n    left: number;\n    width: number;\n    height: number;\n  };\n  let targetOffset: {\n    top: number;\n    left: number;\n    width: number;\n    height: number;\n  };\n  let windowSize: { width: number; height: number };\n  let currentTooltipPosition: TooltipPosition;\n\n  //reset the old style\n  tooltipLayer.style.top = \"\";\n  tooltipLayer.style.right = \"\";\n  tooltipLayer.style.bottom = \"\";\n  tooltipLayer.style.left = \"\";\n  tooltipLayer.style.marginLeft = \"\";\n  tooltipLayer.style.marginTop = \"\";\n\n  arrowLayer.style.display = \"inherit\";\n\n  //if we have a custom css class for each step\n  if (typeof currentStep.tooltipClass === \"string\") {\n    tooltipCssClass = currentStep.tooltipClass;\n  } else {\n    tooltipCssClass = intro._options.tooltipClass;\n  }\n\n  tooltipLayer.className = [\"introjs-tooltip\", tooltipCssClass]\n    .filter(Boolean)\n    .join(\" \");\n\n  tooltipLayer.setAttribute(\"role\", \"dialog\");\n\n  currentTooltipPosition = currentStep.position;\n\n  // Floating is always valid, no point in calculating\n  if (currentTooltipPosition !== \"floating\" && intro._options.autoPosition) {\n    currentTooltipPosition = _determineAutoPosition(\n      intro._options.positionPrecedence,\n      currentStep.element as HTMLElement,\n      tooltipLayer,\n      currentTooltipPosition\n    );\n  }\n\n  let tooltipLayerStyleLeft: number;\n  targetOffset = getOffset(currentStep.element as HTMLElement);\n  tooltipOffset = getOffset(tooltipLayer);\n  windowSize = getWindowSize();\n\n  addClass(tooltipLayer, `introjs-${currentTooltipPosition}`);\n\n  let tooltipLayerStyleLeftRight =\n    targetOffset.width / 2 - tooltipOffset.width / 2;\n\n  switch (currentTooltipPosition) {\n    case \"top-right-aligned\":\n      arrowLayer.className = \"introjs-arrow bottom-right\";\n\n      let tooltipLayerStyleRight = 0;\n      checkLeft(\n        targetOffset,\n        tooltipLayerStyleRight,\n        tooltipOffset,\n        tooltipLayer\n      );\n      tooltipLayer.style.bottom = `${targetOffset.height + 20}px`;\n      break;\n\n    case \"top-middle-aligned\":\n      arrowLayer.className = \"introjs-arrow bottom-middle\";\n\n      // a fix for middle aligned hints\n      if (hintMode) {\n        tooltipLayerStyleLeftRight += 5;\n      }\n\n      if (\n        checkLeft(\n          targetOffset,\n          tooltipLayerStyleLeftRight,\n          tooltipOffset,\n          tooltipLayer\n        )\n      ) {\n        tooltipLayer.style.right = \"\";\n        checkRight(\n          targetOffset,\n          tooltipLayerStyleLeftRight,\n          tooltipOffset,\n          windowSize,\n          tooltipLayer\n        );\n      }\n      tooltipLayer.style.bottom = `${targetOffset.height + 20}px`;\n      break;\n\n    case \"top-left-aligned\":\n    // top-left-aligned is the same as the default top\n    case \"top\":\n      arrowLayer.className = \"introjs-arrow bottom\";\n\n      tooltipLayerStyleLeft = hintMode ? 0 : 15;\n\n      checkRight(\n        targetOffset,\n        tooltipLayerStyleLeft,\n        tooltipOffset,\n        windowSize,\n        tooltipLayer\n      );\n      tooltipLayer.style.bottom = `${targetOffset.height + 20}px`;\n      break;\n    case \"right\":\n      tooltipLayer.style.left = `${targetOffset.width + 20}px`;\n      if (targetOffset.top + tooltipOffset.height > windowSize.height) {\n        // In this case, right would have fallen below the bottom of the screen.\n        // Modify so that the bottom of the tooltip connects with the target\n        arrowLayer.className = \"introjs-arrow left-bottom\";\n        tooltipLayer.style.top = `-${\n          tooltipOffset.height - targetOffset.height - 20\n        }px`;\n      } else {\n        arrowLayer.className = \"introjs-arrow left\";\n      }\n      break;\n    case \"left\":\n      if (!hintMode && intro._options.showStepNumbers === true) {\n        tooltipLayer.style.top = \"15px\";\n      }\n\n      if (targetOffset.top + tooltipOffset.height > windowSize.height) {\n        // In this case, left would have fallen below the bottom of the screen.\n        // Modify so that the bottom of the tooltip connects with the target\n        tooltipLayer.style.top = `-${\n          tooltipOffset.height - targetOffset.height - 20\n        }px`;\n        arrowLayer.className = \"introjs-arrow right-bottom\";\n      } else {\n        arrowLayer.className = \"introjs-arrow right\";\n      }\n      tooltipLayer.style.right = `${targetOffset.width + 20}px`;\n\n      break;\n    case \"floating\":\n      arrowLayer.style.display = \"none\";\n\n      //we have to adjust the top and left of layer manually for intro items without element\n      tooltipLayer.style.left = \"50%\";\n      tooltipLayer.style.top = \"50%\";\n      tooltipLayer.style.marginLeft = `-${tooltipOffset.width / 2}px`;\n      tooltipLayer.style.marginTop = `-${tooltipOffset.height / 2}px`;\n\n      break;\n    case \"bottom-right-aligned\":\n      arrowLayer.className = \"introjs-arrow top-right\";\n\n      tooltipLayerStyleRight = 0;\n      checkLeft(\n        targetOffset,\n        tooltipLayerStyleRight,\n        tooltipOffset,\n        tooltipLayer\n      );\n      tooltipLayer.style.top = `${targetOffset.height + 20}px`;\n      break;\n\n    case \"bottom-middle-aligned\":\n      arrowLayer.className = \"introjs-arrow top-middle\";\n\n      // a fix for middle aligned hints\n      if (hintMode) {\n        tooltipLayerStyleLeftRight += 5;\n      }\n\n      if (\n        checkLeft(\n          targetOffset,\n          tooltipLayerStyleLeftRight,\n          tooltipOffset,\n          tooltipLayer\n        )\n      ) {\n        tooltipLayer.style.right = \"\";\n        checkRight(\n          targetOffset,\n          tooltipLayerStyleLeftRight,\n          tooltipOffset,\n          windowSize,\n          tooltipLayer\n        );\n      }\n      tooltipLayer.style.top = `${targetOffset.height + 20}px`;\n      break;\n\n    // case 'bottom-left-aligned':\n    // Bottom-left-aligned is the same as the default bottom\n    // case 'bottom':\n    // Bottom going to follow the default behavior\n    default:\n      arrowLayer.className = \"introjs-arrow top\";\n\n      tooltipLayerStyleLeft = 0;\n      checkRight(\n        targetOffset,\n        tooltipLayerStyleLeft,\n        tooltipOffset,\n        windowSize,\n        tooltipLayer\n      );\n      tooltipLayer.style.top = `${targetOffset.height + 20}px`;\n  }\n}\n", "import removeClass from \"../util/removeClass\";\n\n/**\n * To remove all show element(s)\n *\n * @api private\n */\nexport default function removeShowElement() {\n  const elms = Array.from(\n    document.querySelectorAll<HTMLElement>(\".introjs-showElement\")\n  );\n\n  for (const elm of elms) {\n    removeClass(elm, /introjs-[a-zA-Z]+/g);\n  }\n}\n", "import setStyle from \"./setStyle\";\n\n/**\n * Create a DOM element with various attributes\n */\nexport default function _createElement<K extends keyof HTMLElementTagNameMap>(\n  tagName: K,\n  attrs?: { [key: string]: string | Function }\n): HTMLElementTagNameMap[K] {\n  let element = document.createElement<K>(tagName);\n\n  attrs = attrs || {};\n\n  // regex for matching attributes that need to be set with setAttribute\n  const setAttRegex = /^(?:role|data-|aria-)/;\n\n  for (const k in attrs) {\n    let v = attrs[k];\n\n    if (k === \"style\" && typeof v !== \"function\") {\n      setStyle(element, v);\n    } else if (typeof v === \"string\" && k.match(setAttRegex)) {\n      element.setAttribute(k, v);\n    } else {\n      // @ts-ignore\n      element[k] = v;\n    }\n  }\n\n  return element;\n}\n", "import setStyle from \"./setStyle\";\n\n/**\n * Appends `element` to `parentElement`\n */\nexport default function appendChild(\n  parentElement: HTMLElement,\n  element: HTMLElement,\n  animate: boolean = false\n) {\n  if (animate) {\n    const existingOpacity = element.style.opacity || \"1\";\n\n    setStyle(element, {\n      opacity: \"0\",\n    });\n\n    window.setTimeout(() => {\n      setStyle(element, {\n        opacity: existingOpacity,\n      });\n    }, 10);\n  }\n\n  parentElement.appendChild(element);\n}\n", "import setShowElement from \"../util/setShowElement\";\nimport scrollParentToElement from \"../util/scrollParentToElement\";\nimport addClass from \"../util/addClass\";\nimport scrollTo from \"../util/scrollTo\";\nimport exitIntro from \"./exitIntro\";\nimport setAnchorAsButton from \"../util/setAnchorAsButton\";\nimport { IntroStep, nextStep, previousStep } from \"./steps\";\nimport setHelperLayerPosition from \"./setHelperLayerPosition\";\nimport placeTooltip from \"./placeTooltip\";\nimport removeShowElement from \"./removeShowElement\";\nimport createElement from \"../util/createElement\";\nimport setStyle from \"../util/setStyle\";\nimport appendChild from \"../util/appendChild\";\nimport { IntroJs } from \"../intro\";\nimport isFunction from \"../util/isFunction\";\n\n/**\n * Gets the current progress percentage\n *\n * @api private\n * @returns current progress percentage\n */\nfunction _getProgress(currentStep: number, introItemsLength: number) {\n  // Steps are 0 indexed\n  return ((currentStep + 1) / introItemsLength) * 100;\n}\n\n/**\n * Add disableinteraction layer and adjust the size and position of the layer\n *\n * @api private\n */\nfunction _disableInteraction(intro: IntroJs, step: IntroStep) {\n  let disableInteractionLayer = document.querySelector<HTMLElement>(\n    \".introjs-disableInteraction\"\n  );\n\n  if (disableInteractionLayer === null) {\n    disableInteractionLayer = createElement(\"div\", {\n      className: \"introjs-disableInteraction\",\n    });\n\n    intro._targetElement.appendChild(disableInteractionLayer);\n  }\n\n  setHelperLayerPosition(intro, step, disableInteractionLayer);\n}\n\n/**\n * Creates the bullets layer\n * @private\n */\nfunction _createBullets(intro: IntroJs, targetElement: IntroStep): HTMLElement {\n  const bulletsLayer = createElement(\"div\", {\n    className: \"introjs-bullets\",\n  });\n\n  if (intro._options.showBullets === false) {\n    bulletsLayer.style.display = \"none\";\n  }\n\n  const ulContainer = createElement(\"ul\");\n  ulContainer.setAttribute(\"role\", \"tablist\");\n\n  const anchorClick = function (this: HTMLElement) {\n    const stepNumber = this.getAttribute(\"data-step-number\");\n    if (stepNumber == null) return;\n\n    intro.goToStep(parseInt(stepNumber, 10));\n  };\n\n  for (let i = 0; i < intro._introItems.length; i++) {\n    const { step } = intro._introItems[i];\n\n    const innerLi = createElement(\"li\");\n    const anchorLink = createElement(\"a\");\n\n    innerLi.setAttribute(\"role\", \"presentation\");\n    anchorLink.setAttribute(\"role\", \"tab\");\n\n    anchorLink.onclick = anchorClick;\n\n    if (i === targetElement.step - 1) {\n      anchorLink.className = \"active\";\n    }\n\n    setAnchorAsButton(anchorLink);\n    anchorLink.innerHTML = \"&nbsp;\";\n    anchorLink.setAttribute(\"data-step-number\", step.toString());\n\n    innerLi.appendChild(anchorLink);\n    ulContainer.appendChild(innerLi);\n  }\n\n  bulletsLayer.appendChild(ulContainer);\n\n  return bulletsLayer;\n}\n\n/**\n * Deletes and recreates the bullets layer\n * @private\n */\nexport function _recreateBullets(intro: IntroJs, targetElement: IntroStep) {\n  if (intro._options.showBullets) {\n    const existing = document.querySelector(\".introjs-bullets\");\n\n    if (existing && existing.parentNode) {\n      existing.parentNode.replaceChild(\n        _createBullets(intro, targetElement),\n        existing\n      );\n    }\n  }\n}\n\n/**\n * Updates the bullets\n */\nfunction _updateBullets(\n  showBullets: boolean,\n  oldReferenceLayer: HTMLElement,\n  targetElement: IntroStep\n) {\n  if (showBullets) {\n    const oldRefActiveBullet = oldReferenceLayer.querySelector(\n      \".introjs-bullets li > a.active\"\n    );\n\n    const oldRefBulletStepNumber = oldReferenceLayer.querySelector(\n      `.introjs-bullets li > a[data-step-number=\"${targetElement.step}\"]`\n    );\n\n    if (oldRefActiveBullet && oldRefBulletStepNumber) {\n      oldRefActiveBullet.className = \"\";\n      oldRefBulletStepNumber.className = \"active\";\n    }\n  }\n}\n\n/**\n * Creates the progress-bar layer and elements\n * @private\n */\nfunction _createProgressBar(intro: IntroJs) {\n  const progressLayer = createElement(\"div\");\n\n  progressLayer.className = \"introjs-progress\";\n\n  if (intro._options.showProgress === false) {\n    progressLayer.style.display = \"none\";\n  }\n\n  const progressBar = createElement(\"div\", {\n    className: \"introjs-progressbar\",\n  });\n\n  if (intro._options.progressBarAdditionalClass) {\n    progressBar.className += \" \" + intro._options.progressBarAdditionalClass;\n  }\n\n  const progress = _getProgress(intro._currentStep, intro._introItems.length);\n  progressBar.setAttribute(\"role\", \"progress\");\n  progressBar.setAttribute(\"aria-valuemin\", \"0\");\n  progressBar.setAttribute(\"aria-valuemax\", \"100\");\n  progressBar.setAttribute(\"aria-valuenow\", progress.toString());\n  progressBar.style.cssText = `width:${progress}%;`;\n\n  progressLayer.appendChild(progressBar);\n\n  return progressLayer;\n}\n\n/**\n * Updates an existing progress bar variables\n * @private\n */\nexport function _updateProgressBar(\n  oldReferenceLayer: HTMLElement,\n  currentStep: number,\n  introItemsLength: number\n) {\n  const progressBar = oldReferenceLayer.querySelector<HTMLElement>(\n    \".introjs-progress .introjs-progressbar\"\n  );\n\n  if (!progressBar) return;\n\n  const progress = _getProgress(currentStep, introItemsLength);\n\n  progressBar.style.cssText = `width:${progress}%;`;\n  progressBar.setAttribute(\"aria-valuenow\", progress.toString());\n}\n\n/**\n * Show an element on the page\n *\n * @api private\n */\nexport default async function _showElement(\n  intro: IntroJs,\n  targetElement: IntroStep\n) {\n  if (isFunction(intro._introChangeCallback)) {\n    await intro._introChangeCallback.call(intro, targetElement.element);\n  }\n\n  const oldHelperLayer = document.querySelector<HTMLElement>(\n    \".introjs-helperLayer\"\n  );\n  const oldReferenceLayer = document.querySelector<HTMLElement>(\n    \".introjs-tooltipReferenceLayer\"\n  );\n  let highlightClass = \"introjs-helperLayer\";\n  let nextTooltipButton: HTMLElement;\n  let prevTooltipButton: HTMLElement;\n  let skipTooltipButton: HTMLElement;\n\n  //check for a current step highlight class\n  if (typeof targetElement.highlightClass === \"string\") {\n    highlightClass += ` ${targetElement.highlightClass}`;\n  }\n  //check for options highlight class\n  if (typeof intro._options.highlightClass === \"string\") {\n    highlightClass += ` ${intro._options.highlightClass}`;\n  }\n\n  if (oldHelperLayer !== null && oldReferenceLayer !== null) {\n    const oldHelperNumberLayer = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-helperNumberLayer\"\n    );\n    const oldTooltipLayer = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-tooltiptext\"\n    ) as HTMLElement;\n    const oldTooltipTitleLayer = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-tooltip-title\"\n    ) as HTMLElement;\n    const oldArrowLayer = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-arrow\"\n    ) as HTMLElement;\n    const oldTooltipContainer = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-tooltip\"\n    ) as HTMLElement;\n\n    skipTooltipButton = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-skipbutton\"\n    ) as HTMLElement;\n    prevTooltipButton = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-prevbutton\"\n    ) as HTMLElement;\n    nextTooltipButton = oldReferenceLayer.querySelector<HTMLElement>(\n      \".introjs-nextbutton\"\n    ) as HTMLElement;\n\n    //update or reset the helper highlight class\n    oldHelperLayer.className = highlightClass;\n    //hide the tooltip\n    oldTooltipContainer.style.opacity = \"0\";\n    oldTooltipContainer.style.display = \"none\";\n\n    // if the target element is within a scrollable element\n    scrollParentToElement(\n      intro._options.scrollToElement,\n      targetElement.element as HTMLElement\n    );\n\n    // set new position to helper layer\n    setHelperLayerPosition(intro, targetElement, oldHelperLayer);\n    setHelperLayerPosition(intro, targetElement, oldReferenceLayer);\n\n    //remove old classes if the element still exist\n    removeShowElement();\n\n    //we should wait until the CSS3 transition is competed (it's 0.3 sec) to prevent incorrect `height` and `width` calculation\n    if (intro._lastShowElementTimer) {\n      window.clearTimeout(intro._lastShowElementTimer);\n    }\n\n    intro._lastShowElementTimer = window.setTimeout(() => {\n      // set current step to the label\n      if (oldHelperNumberLayer !== null) {\n        oldHelperNumberLayer.innerHTML = `${targetElement.step} ${intro._options.stepNumbersOfLabel} ${intro._introItems.length}`;\n      }\n\n      // set current tooltip text\n      oldTooltipLayer.innerHTML = targetElement.intro || \"\";\n\n      // set current tooltip title\n      oldTooltipTitleLayer.innerHTML = targetElement.title || \"\";\n\n      //set the tooltip position\n      oldTooltipContainer.style.display = \"block\";\n      placeTooltip(intro, targetElement, oldTooltipContainer, oldArrowLayer);\n\n      //change active bullet\n      _updateBullets(\n        intro._options.showBullets,\n        oldReferenceLayer,\n        targetElement\n      );\n\n      _updateProgressBar(\n        oldReferenceLayer,\n        intro._currentStep,\n        intro._introItems.length\n      );\n\n      //show the tooltip\n      oldTooltipContainer.style.opacity = \"1\";\n\n      //reset button focus\n      if (\n        typeof nextTooltipButton !== \"undefined\" &&\n        nextTooltipButton !== null &&\n        /introjs-donebutton/gi.test(nextTooltipButton.className)\n      ) {\n        // skip button is now \"done\" button\n        nextTooltipButton.focus();\n      } else if (\n        typeof nextTooltipButton !== \"undefined\" &&\n        nextTooltipButton !== null\n      ) {\n        //still in the tour, focus on next\n        nextTooltipButton.focus();\n      }\n\n      // change the scroll of the window, if needed\n      scrollTo(\n        intro._options.scrollToElement,\n        targetElement.scrollTo,\n        intro._options.scrollPadding,\n        targetElement.element as HTMLElement,\n        oldTooltipLayer\n      );\n    }, 350);\n\n    // end of old element if-else condition\n  } else {\n    const helperLayer = createElement(\"div\", {\n      className: highlightClass,\n    });\n    const referenceLayer = createElement(\"div\", {\n      className: \"introjs-tooltipReferenceLayer\",\n    });\n    const arrowLayer = createElement(\"div\", {\n      className: \"introjs-arrow\",\n    });\n    const tooltipLayer = createElement(\"div\", {\n      className: \"introjs-tooltip\",\n    });\n    const tooltipTextLayer = createElement(\"div\", {\n      className: \"introjs-tooltiptext\",\n    });\n    const tooltipHeaderLayer = createElement(\"div\", {\n      className: \"introjs-tooltip-header\",\n    });\n    const tooltipTitleLayer = createElement(\"h1\", {\n      className: \"introjs-tooltip-title\",\n    });\n\n    const buttonsLayer = createElement(\"div\");\n\n    setStyle(helperLayer, {\n      \"box-shadow\": `0 0 1px 2px rgba(33, 33, 33, 0.8), rgba(33, 33, 33, ${intro._options.overlayOpacity.toString()}) 0 0 0 5000px`,\n    });\n\n    // target is within a scrollable element\n    scrollParentToElement(\n      intro._options.scrollToElement,\n      targetElement.element as HTMLElement\n    );\n\n    //set new position to helper layer\n    setHelperLayerPosition(intro, targetElement, helperLayer);\n    setHelperLayerPosition(intro, targetElement, referenceLayer);\n\n    //add helper layer to target element\n    appendChild(intro._targetElement, helperLayer, true);\n    appendChild(intro._targetElement, referenceLayer);\n\n    tooltipTextLayer.innerHTML = targetElement.intro;\n    tooltipTitleLayer.innerHTML = targetElement.title;\n\n    buttonsLayer.className = \"introjs-tooltipbuttons\";\n    if (intro._options.showButtons === false) {\n      buttonsLayer.style.display = \"none\";\n    }\n\n    tooltipHeaderLayer.appendChild(tooltipTitleLayer);\n    tooltipLayer.appendChild(tooltipHeaderLayer);\n    tooltipLayer.appendChild(tooltipTextLayer);\n\n    // \"Do not show again\" checkbox\n    if (intro._options.dontShowAgain) {\n      const dontShowAgainWrapper = createElement(\"div\", {\n        className: \"introjs-dontShowAgain\",\n      });\n      const dontShowAgainCheckbox = createElement(\"input\", {\n        type: \"checkbox\",\n        id: \"introjs-dontShowAgain\",\n        name: \"introjs-dontShowAgain\",\n      });\n      dontShowAgainCheckbox.onchange = (e) => {\n        intro.setDontShowAgain((<HTMLInputElement>e.target).checked);\n      };\n      const dontShowAgainCheckboxLabel = createElement(\"label\", {\n        htmlFor: \"introjs-dontShowAgain\",\n      });\n      dontShowAgainCheckboxLabel.innerText = intro._options.dontShowAgainLabel;\n      dontShowAgainWrapper.appendChild(dontShowAgainCheckbox);\n      dontShowAgainWrapper.appendChild(dontShowAgainCheckboxLabel);\n\n      tooltipLayer.appendChild(dontShowAgainWrapper);\n    }\n\n    tooltipLayer.appendChild(_createBullets(intro, targetElement));\n    tooltipLayer.appendChild(_createProgressBar(intro));\n\n    // add helper layer number\n    const helperNumberLayer = createElement(\"div\");\n\n    if (intro._options.showStepNumbers === true) {\n      helperNumberLayer.className = \"introjs-helperNumberLayer\";\n      helperNumberLayer.innerHTML = `${targetElement.step} ${intro._options.stepNumbersOfLabel} ${intro._introItems.length}`;\n      tooltipLayer.appendChild(helperNumberLayer);\n    }\n\n    tooltipLayer.appendChild(arrowLayer);\n    referenceLayer.appendChild(tooltipLayer);\n\n    //next button\n    nextTooltipButton = createElement(\"a\");\n\n    nextTooltipButton.onclick = async () => {\n      if (intro._introItems.length - 1 !== intro._currentStep) {\n        await nextStep(intro);\n      } else if (/introjs-donebutton/gi.test(nextTooltipButton.className)) {\n        if (isFunction(intro._introCompleteCallback)) {\n          await intro._introCompleteCallback.call(\n            intro,\n            intro._currentStep,\n            \"done\"\n          );\n        }\n\n        await exitIntro(intro, intro._targetElement);\n      }\n    };\n\n    setAnchorAsButton(nextTooltipButton);\n    nextTooltipButton.innerHTML = intro._options.nextLabel;\n\n    //previous button\n    prevTooltipButton = createElement(\"a\");\n\n    prevTooltipButton.onclick = async () => {\n      if (intro._currentStep > 0) {\n        await previousStep(intro);\n      }\n    };\n\n    setAnchorAsButton(prevTooltipButton);\n    prevTooltipButton.innerHTML = intro._options.prevLabel;\n\n    //skip button\n    skipTooltipButton = createElement(\"a\", {\n      className: \"introjs-skipbutton\",\n    });\n\n    setAnchorAsButton(skipTooltipButton);\n    skipTooltipButton.innerHTML = intro._options.skipLabel;\n\n    skipTooltipButton.onclick = async () => {\n      if (\n        intro._introItems.length - 1 === intro._currentStep &&\n        isFunction(intro._introCompleteCallback)\n      ) {\n        await intro._introCompleteCallback.call(\n          intro,\n          intro._currentStep,\n          \"skip\"\n        );\n      }\n\n      if (isFunction(intro._introSkipCallback)) {\n        await intro._introSkipCallback.call(intro, intro._currentStep);\n      }\n\n      await exitIntro(intro, intro._targetElement);\n    };\n\n    tooltipHeaderLayer.appendChild(skipTooltipButton);\n\n    // in order to prevent displaying previous button always\n    if (intro._introItems.length > 1) {\n      buttonsLayer.appendChild(prevTooltipButton);\n    }\n\n    // we always need the next button because this\n    // button changes to \"Done\" in the last step of the tour\n    buttonsLayer.appendChild(nextTooltipButton);\n    tooltipLayer.appendChild(buttonsLayer);\n\n    // set proper position\n    placeTooltip(intro, targetElement, tooltipLayer, arrowLayer);\n\n    // change the scroll of the window, if needed\n    scrollTo(\n      intro._options.scrollToElement,\n      targetElement.scrollTo,\n      intro._options.scrollPadding,\n      targetElement.element as HTMLElement,\n      tooltipLayer\n    );\n\n    //end of new element if-else condition\n  }\n\n  // removing previous disable interaction layer\n  const disableInteractionLayer = intro._targetElement.querySelector(\n    \".introjs-disableInteraction\"\n  );\n  if (disableInteractionLayer && disableInteractionLayer.parentNode) {\n    disableInteractionLayer.parentNode.removeChild(disableInteractionLayer);\n  }\n\n  //disable interaction\n  if (targetElement.disableInteraction) {\n    _disableInteraction(intro, targetElement);\n  }\n\n  // when it's the first step of tour\n  if (intro._currentStep === 0 && intro._introItems.length > 1) {\n    if (\n      typeof nextTooltipButton !== \"undefined\" &&\n      nextTooltipButton !== null\n    ) {\n      nextTooltipButton.className = `${intro._options.buttonClass} introjs-nextbutton`;\n      nextTooltipButton.innerHTML = intro._options.nextLabel;\n    }\n\n    if (intro._options.hidePrev === true) {\n      if (\n        typeof prevTooltipButton !== \"undefined\" &&\n        prevTooltipButton !== null\n      ) {\n        prevTooltipButton.className = `${intro._options.buttonClass} introjs-prevbutton introjs-hidden`;\n      }\n      if (\n        typeof nextTooltipButton !== \"undefined\" &&\n        nextTooltipButton !== null\n      ) {\n        addClass(nextTooltipButton, \"introjs-fullbutton\");\n      }\n    } else {\n      if (\n        typeof prevTooltipButton !== \"undefined\" &&\n        prevTooltipButton !== null\n      ) {\n        prevTooltipButton.className = `${intro._options.buttonClass} introjs-prevbutton introjs-disabled`;\n      }\n    }\n  } else if (\n    intro._introItems.length - 1 === intro._currentStep ||\n    intro._introItems.length === 1\n  ) {\n    // last step of tour\n    if (\n      typeof prevTooltipButton !== \"undefined\" &&\n      prevTooltipButton !== null\n    ) {\n      prevTooltipButton.className = `${intro._options.buttonClass} introjs-prevbutton`;\n    }\n\n    if (intro._options.hideNext === true) {\n      if (\n        typeof nextTooltipButton !== \"undefined\" &&\n        nextTooltipButton !== null\n      ) {\n        nextTooltipButton.className = `${intro._options.buttonClass} introjs-nextbutton introjs-hidden`;\n      }\n      if (\n        typeof prevTooltipButton !== \"undefined\" &&\n        prevTooltipButton !== null\n      ) {\n        addClass(prevTooltipButton, \"introjs-fullbutton\");\n      }\n    } else {\n      if (\n        typeof nextTooltipButton !== \"undefined\" &&\n        nextTooltipButton !== null\n      ) {\n        if (intro._options.nextToDone === true) {\n          nextTooltipButton.innerHTML = intro._options.doneLabel;\n          addClass(\n            nextTooltipButton,\n            `${intro._options.buttonClass} introjs-nextbutton introjs-donebutton`\n          );\n        } else {\n          nextTooltipButton.className = `${intro._options.buttonClass} introjs-nextbutton introjs-disabled`;\n        }\n      }\n    }\n  } else {\n    // steps between start and end\n    if (\n      typeof prevTooltipButton !== \"undefined\" &&\n      prevTooltipButton !== null\n    ) {\n      prevTooltipButton.className = `${intro._options.buttonClass} introjs-prevbutton`;\n    }\n    if (\n      typeof nextTooltipButton !== \"undefined\" &&\n      nextTooltipButton !== null\n    ) {\n      nextTooltipButton.className = `${intro._options.buttonClass} introjs-nextbutton`;\n      nextTooltipButton.innerHTML = intro._options.nextLabel;\n    }\n  }\n\n  if (typeof prevTooltipButton !== \"undefined\" && prevTooltipButton !== null) {\n    prevTooltipButton.setAttribute(\"role\", \"button\");\n  }\n  if (typeof nextTooltipButton !== \"undefined\" && nextTooltipButton !== null) {\n    nextTooltipButton.setAttribute(\"role\", \"button\");\n  }\n  if (typeof skipTooltipButton !== \"undefined\" && skipTooltipButton !== null) {\n    skipTooltipButton.setAttribute(\"role\", \"button\");\n  }\n\n  //Set focus on \"next\" button, so that hitting Enter always moves you onto the next step\n  if (typeof nextTooltipButton !== \"undefined\" && nextTooltipButton !== null) {\n    nextTooltipButton.focus();\n  }\n\n  setShowElement(targetElement.element as HTMLElement);\n\n  if (isFunction(intro._introAfterChangeCallback)) {\n    await intro._introAfterChangeCallback.call(intro, targetElement.element);\n  }\n}\n", "import isFunction from \"../util/isFunction\";\nimport exitIntro from \"./exitIntro\";\nimport showElement from \"./showElement\";\nimport { IntroJs } from \"../intro\";\n\nexport type ScrollTo = \"off\" | \"element\" | \"tooltip\";\n\nexport type TooltipPosition =\n  | \"floating\"\n  | \"top\"\n  | \"bottom\"\n  | \"left\"\n  | \"right\"\n  | \"top-right-aligned\"\n  | \"top-left-aligned\"\n  | \"top-middle-aligned\"\n  | \"bottom-right-aligned\"\n  | \"bottom-left-aligned\"\n  | \"bottom-middle-aligned\";\n\nexport type HintPosition =\n  | \"top-left\"\n  | \"top-right\"\n  | \"top-middle\"\n  | \"bottom-left\"\n  | \"bottom-right\"\n  | \"bottom-middle\"\n  | \"middle-left\"\n  | \"middle-right\"\n  | \"middle-middle\";\n\nexport type IntroStep = {\n  step: number;\n  title: string;\n  intro: string;\n  tooltipClass?: string;\n  highlightClass?: string;\n  element?: HTMLElement | string | null;\n  position: TooltipPosition;\n  scrollTo: ScrollTo;\n  disableInteraction?: boolean;\n};\n\nexport type HintStep = {\n  element?: HTMLElement | string | null;\n  tooltipClass?: string;\n  position: TooltipPosition;\n  hint?: string;\n  hintTargetElement?: HTMLElement;\n  hintAnimation?: boolean;\n  hintPosition: HintPosition;\n};\n\n/**\n * Go to specific step of introduction\n *\n * @api private\n */\nexport async function goToStep(intro: IntroJs, step: number) {\n  //because steps starts with zero\n  intro._currentStep = step - 2;\n  if (typeof intro._introItems !== \"undefined\") {\n    await nextStep(intro);\n  }\n}\n\n/**\n * Go to the specific step of introduction with the explicit [data-step] number\n *\n * @api private\n */\nexport async function goToStepNumber(intro: IntroJs, step: number) {\n  intro._currentStepNumber = step;\n  if (typeof intro._introItems !== \"undefined\") {\n    await nextStep(intro);\n  }\n}\n\n/**\n * Go to next step on intro\n *\n * @api private\n */\nexport async function nextStep(intro: IntroJs) {\n  intro._direction = \"forward\";\n\n  if (typeof intro._currentStepNumber !== \"undefined\") {\n    for (let i = 0; i < intro._introItems.length; i++) {\n      const item = intro._introItems[i];\n      if (item.step === intro._currentStepNumber) {\n        intro._currentStep = i - 1;\n        intro._currentStepNumber = undefined;\n      }\n    }\n  }\n\n  if (intro._currentStep === -1) {\n    intro._currentStep = 0;\n  } else {\n    ++intro._currentStep;\n  }\n\n  const nextStep = intro._introItems[intro._currentStep];\n  let continueStep = true;\n\n  if (isFunction(intro._introBeforeChangeCallback)) {\n    continueStep = await intro._introBeforeChangeCallback.call(\n      intro,\n      nextStep && (nextStep.element as HTMLElement),\n      intro._currentStep,\n      intro._direction\n    );\n  }\n\n  // if `onbeforechange` returned `false`, stop displaying the element\n  if (continueStep === false) {\n    --intro._currentStep;\n    return false;\n  }\n\n  if (intro._introItems.length <= intro._currentStep) {\n    // end of the intro\n    // check if any callback is defined\n    if (isFunction(intro._introCompleteCallback)) {\n      await intro._introCompleteCallback.call(intro, intro._currentStep, \"end\");\n    }\n\n    await exitIntro(intro, intro._targetElement);\n\n    return false;\n  }\n\n  await showElement(intro, nextStep);\n\n  return true;\n}\n\n/**\n * Go to previous step on intro\n *\n * @api private\n */\nexport async function previousStep(intro: IntroJs) {\n  intro._direction = \"backward\";\n\n  if (intro._currentStep <= 0) {\n    return false;\n  }\n\n  --intro._currentStep;\n\n  const nextStep = intro._introItems[intro._currentStep];\n  let continueStep = true;\n\n  if (isFunction(intro._introBeforeChangeCallback)) {\n    continueStep = await intro._introBeforeChangeCallback.call(\n      intro,\n      nextStep && (nextStep.element as HTMLElement),\n      intro._currentStep,\n      intro._direction\n    );\n  }\n\n  // if `onbeforechange` returned `false`, stop displaying the element\n  if (continueStep === false) {\n    ++intro._currentStep;\n    return false;\n  }\n\n  await showElement(intro, nextStep);\n\n  return true;\n}\n", "import { nextStep, previousStep } from \"./steps\";\nimport exitIntro from \"./exitIntro\";\nimport { IntroJs } from \"../intro\";\nimport isFunction from \"../util/isFunction\";\n\n/**\n * on keyCode:\n * https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/keyCode\n * This feature has been removed from the Web standards.\n * Though some browsers may still support it, it is in\n * the process of being dropped.\n * Instead, you should use KeyboardEvent.code,\n * if it's implemented.\n *\n * j<PERSON><PERSON><PERSON>'s approach is to test for\n *   (1) e.which, then\n *   (2) e.charCode, then\n *   (3) e.keyCode\n * https://github.com/jquery/jquery/blob/a6b0705294d336ae2f63f7276de0da1195495363/src/event.js#L638\n */\nexport default async function onKeyDown(intro: IntroJs, e: KeyboardEvent) {\n  let code = e.code === undefined ? e.which : e.code;\n\n  // if e.which is null\n  if (code === null) {\n    code = e.charCode === null ? e.keyCode : e.charCode;\n  }\n\n  if ((code === \"Escape\" || code === 27) && intro._options.exitOnEsc === true) {\n    //escape key pressed, exit the intro\n    //check if exit callback is defined\n    await exitIntro(intro, intro._targetElement);\n  } else if (code === \"ArrowLeft\" || code === 37) {\n    //left arrow\n    await previousStep(intro);\n  } else if (code === \"ArrowRight\" || code === 39) {\n    //right arrow\n    await nextStep(intro);\n  } else if (code === \"Enter\" || code === \"NumpadEnter\" || code === 13) {\n    //srcElement === ie\n    const target = (e.target || e.srcElement) as HTMLElement;\n    if (target && target.className.match(\"introjs-prevbutton\")) {\n      //user hit enter while focusing on previous button\n      await previousStep(intro);\n    } else if (target && target.className.match(\"introjs-skipbutton\")) {\n      //user hit enter while focusing on skip button\n      if (\n        intro._introItems.length - 1 === intro._currentStep &&\n        isFunction(intro._introCompleteCallback)\n      ) {\n        await intro._introCompleteCallback.call(\n          intro,\n          intro._currentStep,\n          \"skip\"\n        );\n      }\n\n      await exitIntro(intro, intro._targetElement);\n    } else if (target && target.getAttribute(\"data-step-number\")) {\n      // user hit enter while focusing on step bullet\n      target.click();\n    } else {\n      //default behavior for responding to enter\n      await nextStep(intro);\n    }\n\n    //prevent default behaviour on hitting Enter, to prevent steps being skipped in some browsers\n    if (e.preventDefault) {\n      e.preventDefault();\n    } else {\n      e.returnValue = false;\n    }\n  }\n}\n", "/**\n * Makes a copy of an object\n * @api private\n */\nexport default function cloneObject<T>(source: T): T {\n  if (source === null || typeof source !== \"object\" || \"nodeType\" in source) {\n    return source;\n  }\n\n  const temp = {} as T;\n\n  for (const key in source) {\n    // @ts-ignore:next-line\n    if (\"jQuery\" in window && source[key] instanceof window.jQuery) {\n      temp[key] = source[key];\n    } else {\n      temp[key] = cloneObject(source[key]);\n    }\n  }\n  return temp;\n}\n", "export default function debounce(\n  func: Function,\n  timeout: number\n): (...args: any) => void {\n  let timer: number;\n\n  return (...args) => {\n    window.clearTimeout(timer);\n\n    timer = window.setTimeout(() => {\n      func(args);\n    }, timeout);\n  };\n}\n", "import addClass from \"../util/addClass\";\nimport removeClass from \"../util/removeClass\";\nimport isFixed from \"../util/isFixed\";\nimport getOffset from \"../util/getOffset\";\nimport cloneObject from \"../util/cloneObject\";\nimport DOMEvent from \"./DOMEvent\";\nimport setAnchorAsButton from \"../util/setAnchorAsButton\";\nimport setHelperLayerPosition from \"./setHelperLayerPosition\";\nimport placeTooltip from \"./placeTooltip\";\nimport createElement from \"../util/createElement\";\nimport debounce from \"../util/debounce\";\nimport { HintPosition, HintStep, TooltipPosition } from \"./steps\";\nimport { IntroJs } from \"../intro\";\nimport isFunction from \"../util/isFunction\";\n\n/**\n * Get a queryselector within the hint wrapper\n */\nexport function hintQuerySelectorAll(selector: string): HTMLElement[] {\n  const hintsWrapper = document.querySelector(\".introjs-hints\");\n  return hintsWrapper\n    ? Array.from(hintsWrapper.querySelectorAll(selector))\n    : [];\n}\n\n/**\n * Hide a hint\n *\n * @api private\n */\nexport async function hideHint(intro: IntroJs, stepId: number) {\n  const hint = hintQuerySelectorAll(`.introjs-hint[data-step=\"${stepId}\"]`)[0];\n\n  removeHintTooltip();\n\n  if (hint) {\n    addClass(hint, \"introjs-hidehint\");\n  }\n\n  // call the callback function (if any)\n  if (isFunction(intro._hintCloseCallback)) {\n    await intro._hintCloseCallback.call(intro, stepId);\n  }\n}\n\n/**\n * Hide all hints\n *\n * @api private\n */\nexport async function hideHints(intro: IntroJs) {\n  const hints = hintQuerySelectorAll(\".introjs-hint\");\n\n  for (const hint of hints) {\n    const step = hint.getAttribute(\"data-step\");\n    if (!step) continue;\n\n    await hideHint(intro, parseInt(step, 10));\n  }\n}\n\n/**\n * Show all hints\n *\n * @api private\n */\nexport async function showHints(intro: IntroJs) {\n  const hints = hintQuerySelectorAll(\".introjs-hint\");\n\n  if (hints && hints.length) {\n    for (const hint of hints) {\n      const step = hint.getAttribute(\"data-step\");\n      if (!step) continue;\n\n      showHint(parseInt(step, 10));\n    }\n  } else {\n    await populateHints(intro, intro._targetElement);\n  }\n}\n\n/**\n * Show a hint\n *\n * @api private\n */\nexport function showHint(stepId: number) {\n  const hint = hintQuerySelectorAll(`.introjs-hint[data-step=\"${stepId}\"]`)[0];\n\n  if (hint) {\n    removeClass(hint, /introjs-hidehint/g);\n  }\n}\n\n/**\n * Removes all hint elements on the page\n * Useful when you want to destroy the elements and add them again (e.g. a modal or popup)\n *\n * @api private\n */\nexport function removeHints(intro: IntroJs) {\n  const hints = hintQuerySelectorAll(\".introjs-hint\");\n\n  for (const hint of hints) {\n    const step = hint.getAttribute(\"data-step\");\n    if (!step) continue;\n\n    removeHint(parseInt(step, 10));\n  }\n\n  DOMEvent.off(document, \"click\", removeHintTooltip, intro, false);\n  DOMEvent.off(window, \"resize\", reAlignHints, intro, true);\n\n  if (intro._hintsAutoRefreshFunction) {\n    DOMEvent.off(\n      window,\n      \"scroll\",\n      intro._hintsAutoRefreshFunction,\n      intro,\n      true\n    );\n  }\n}\n\n/**\n * Remove one single hint element from the page\n * Useful when you want to destroy the element and add them again (e.g. a modal or popup)\n * Use removeHints if you want to remove all elements.\n *\n * @api private\n */\nexport function removeHint(stepId: number) {\n  const hint = hintQuerySelectorAll(`.introjs-hint[data-step=\"${stepId}\"]`)[0];\n\n  if (hint && hint.parentNode) {\n    hint.parentNode.removeChild(hint);\n  }\n}\n\n/**\n * Add all available hints to the page\n *\n * @api private\n */\nexport async function addHints(intro: IntroJs) {\n  let hintsWrapper = document.querySelector(\".introjs-hints\");\n\n  if (hintsWrapper === null) {\n    hintsWrapper = createElement(\"div\", {\n      className: \"introjs-hints\",\n    });\n  }\n\n  /**\n   * Returns an event handler unique to the hint iteration\n   */\n  const getHintClick = (i: number) => (e: Event) => {\n    const evt = e ? e : window.event;\n\n    if (evt && evt.stopPropagation) {\n      evt.stopPropagation();\n    }\n\n    if (evt && evt.cancelBubble !== null) {\n      evt.cancelBubble = true;\n    }\n\n    showHintDialog(intro, i);\n  };\n\n  for (let i = 0; i < intro._hintItems.length; i++) {\n    const item = intro._hintItems[i];\n\n    // avoid append a hint twice\n    if (document.querySelector(`.introjs-hint[data-step=\"${i}\"]`)) {\n      return;\n    }\n\n    const hint = createElement(\"a\", {\n      className: \"introjs-hint\",\n    });\n    setAnchorAsButton(hint);\n\n    hint.onclick = getHintClick(i);\n\n    if (!item.hintAnimation) {\n      addClass(hint, \"introjs-hint-no-anim\");\n    }\n\n    // hint's position should be fixed if the target element's position is fixed\n    if (isFixed(item.element as HTMLElement)) {\n      addClass(hint, \"introjs-fixedhint\");\n    }\n\n    const hintDot = createElement(\"div\", {\n      className: \"introjs-hint-dot\",\n    });\n\n    const hintPulse = createElement(\"div\", {\n      className: \"introjs-hint-pulse\",\n    });\n\n    hint.appendChild(hintDot);\n    hint.appendChild(hintPulse);\n    hint.setAttribute(\"data-step\", i.toString());\n\n    // we swap the hint element with target element\n    // because _setHelperLayerPosition uses `element` property\n    item.hintTargetElement = item.element as HTMLElement;\n    item.element = hint;\n\n    // align the hint position\n    alignHintPosition(\n      item.hintPosition,\n      hint,\n      item.hintTargetElement as HTMLElement\n    );\n\n    hintsWrapper.appendChild(hint);\n  }\n\n  // adding the hints wrapper\n  document.body.appendChild(hintsWrapper);\n\n  // call the callback function (if any)\n  if (isFunction(intro._hintsAddedCallback)) {\n    await intro._hintsAddedCallback.call(intro);\n  }\n\n  if (intro._options.hintAutoRefreshInterval >= 0) {\n    intro._hintsAutoRefreshFunction = debounce(\n      () => reAlignHints(intro),\n      intro._options.hintAutoRefreshInterval\n    );\n    DOMEvent.on(window, \"scroll\", intro._hintsAutoRefreshFunction, intro, true);\n  }\n}\n\n/**\n * Aligns hint position\n *\n * @api private\n */\nexport function alignHintPosition(\n  position: HintPosition,\n  hintElement: HTMLElement,\n  targetElement?: HTMLElement\n) {\n  if (typeof targetElement === \"undefined\") {\n    return;\n  }\n\n  // get/calculate offset of target element\n  const offset = getOffset(targetElement);\n  const iconWidth = 20;\n  const iconHeight = 20;\n\n  // align the hint element\n  switch (position) {\n    default:\n    case \"top-left\":\n      hintElement.style.left = `${offset.left}px`;\n      hintElement.style.top = `${offset.top}px`;\n      break;\n    case \"top-right\":\n      hintElement.style.left = `${offset.left + offset.width - iconWidth}px`;\n      hintElement.style.top = `${offset.top}px`;\n      break;\n    case \"bottom-left\":\n      hintElement.style.left = `${offset.left}px`;\n      hintElement.style.top = `${offset.top + offset.height - iconHeight}px`;\n      break;\n    case \"bottom-right\":\n      hintElement.style.left = `${offset.left + offset.width - iconWidth}px`;\n      hintElement.style.top = `${offset.top + offset.height - iconHeight}px`;\n      break;\n    case \"middle-left\":\n      hintElement.style.left = `${offset.left}px`;\n      hintElement.style.top = `${\n        offset.top + (offset.height - iconHeight) / 2\n      }px`;\n      break;\n    case \"middle-right\":\n      hintElement.style.left = `${offset.left + offset.width - iconWidth}px`;\n      hintElement.style.top = `${\n        offset.top + (offset.height - iconHeight) / 2\n      }px`;\n      break;\n    case \"middle-middle\":\n      hintElement.style.left = `${\n        offset.left + (offset.width - iconWidth) / 2\n      }px`;\n      hintElement.style.top = `${\n        offset.top + (offset.height - iconHeight) / 2\n      }px`;\n      break;\n    case \"bottom-middle\":\n      hintElement.style.left = `${\n        offset.left + (offset.width - iconWidth) / 2\n      }px`;\n      hintElement.style.top = `${offset.top + offset.height - iconHeight}px`;\n      break;\n    case \"top-middle\":\n      hintElement.style.left = `${\n        offset.left + (offset.width - iconWidth) / 2\n      }px`;\n      hintElement.style.top = `${offset.top}px`;\n      break;\n  }\n}\n\n/**\n * Triggers when user clicks on the hint element\n *\n * @api private\n */\nexport async function showHintDialog(intro: IntroJs, stepId: number) {\n  const hintElement = document.querySelector<HTMLElement>(\n    `.introjs-hint[data-step=\"${stepId}\"]`\n  ) as HTMLElement;\n  const item = intro._hintItems[stepId];\n\n  // call the callback function (if any)\n  if (isFunction(intro._hintClickCallback)) {\n    await intro._hintClickCallback.call(intro, hintElement, item, stepId);\n  }\n\n  // remove all open tooltips\n  const removedStep = removeHintTooltip();\n\n  // to toggle the tooltip\n  if (removedStep !== undefined && parseInt(removedStep, 10) === stepId) {\n    return;\n  }\n\n  const tooltipLayer = createElement(\"div\", {\n    className: \"introjs-tooltip\",\n  });\n  const tooltipTextLayer = createElement(\"div\");\n  const arrowLayer = createElement(\"div\");\n  const referenceLayer = createElement(\"div\");\n\n  tooltipLayer.onclick = (e: Event) => {\n    //IE9 & Other Browsers\n    if (e.stopPropagation) {\n      e.stopPropagation();\n    }\n    //IE8 and Lower\n    else {\n      e.cancelBubble = true;\n    }\n  };\n\n  tooltipTextLayer.className = \"introjs-tooltiptext\";\n\n  const tooltipWrapper = createElement(\"p\");\n  tooltipWrapper.innerHTML = item.hint || \"\";\n  tooltipTextLayer.appendChild(tooltipWrapper);\n\n  if (intro._options.hintShowButton) {\n    const closeButton = createElement(\"a\");\n    closeButton.className = intro._options.buttonClass;\n    closeButton.setAttribute(\"role\", \"button\");\n    closeButton.innerHTML = intro._options.hintButtonLabel;\n    closeButton.onclick = () => hideHint(intro, stepId);\n    tooltipTextLayer.appendChild(closeButton);\n  }\n\n  arrowLayer.className = \"introjs-arrow\";\n  tooltipLayer.appendChild(arrowLayer);\n\n  tooltipLayer.appendChild(tooltipTextLayer);\n\n  const step = hintElement.getAttribute(\"data-step\") || \"\";\n\n  // set current step for _placeTooltip function\n  intro._currentStep = parseInt(step, 10);\n  const currentStep = intro._hintItems[intro._currentStep];\n\n  // align reference layer position\n  referenceLayer.className =\n    \"introjs-tooltipReferenceLayer introjs-hintReference\";\n  referenceLayer.setAttribute(\"data-step\", step);\n  setHelperLayerPosition(intro, currentStep, referenceLayer);\n\n  referenceLayer.appendChild(tooltipLayer);\n  document.body.appendChild(referenceLayer);\n\n  // set proper position\n  placeTooltip(intro, currentStep, tooltipLayer, arrowLayer, true);\n}\n\n/**\n * Removes open hint (tooltip hint)\n *\n * @api private\n */\nexport function removeHintTooltip(): string | undefined {\n  const tooltip = document.querySelector(\".introjs-hintReference\");\n\n  if (tooltip && tooltip.parentNode) {\n    const step = tooltip.getAttribute(\"data-step\");\n    if (!step) return undefined;\n\n    tooltip.parentNode.removeChild(tooltip);\n\n    return step;\n  }\n\n  return undefined;\n}\n\n/**\n * Start parsing hint items\n *\n * @api private\n */\nexport async function populateHints(\n  intro: IntroJs,\n  targetElm: HTMLElement\n): Promise<boolean> {\n  intro._hintItems = [];\n\n  if (intro._options.hints && intro._options.hints.length > 0) {\n    for (const hint of intro._options.hints) {\n      const currentItem = cloneObject(hint);\n\n      if (typeof currentItem.element === \"string\") {\n        //grab the element with given selector from the page\n        currentItem.element = document.querySelector<HTMLElement>(\n          currentItem.element\n        ) as HTMLElement;\n      }\n\n      currentItem.hintPosition =\n        currentItem.hintPosition || intro._options.hintPosition;\n      currentItem.hintAnimation =\n        currentItem.hintAnimation || intro._options.hintAnimation;\n\n      if (currentItem.element !== null) {\n        intro._hintItems.push(currentItem as HintStep);\n      }\n    }\n  } else {\n    const hints = Array.from(\n      targetElm.querySelectorAll<HTMLElement>(\"*[data-hint]\")\n    );\n\n    if (!hints || !hints.length) {\n      return false;\n    }\n\n    //first add intro items with data-step\n    for (const currentElement of hints) {\n      // hint animation\n      let hintAnimationAttr = currentElement.getAttribute(\n        \"data-hint-animation\"\n      );\n\n      let hintAnimation: boolean = intro._options.hintAnimation;\n      if (hintAnimationAttr) {\n        hintAnimation = hintAnimationAttr === \"true\";\n      }\n\n      intro._hintItems.push({\n        element: currentElement,\n        hint: currentElement.getAttribute(\"data-hint\") || \"\",\n        hintPosition: (currentElement.getAttribute(\"data-hint-position\") ||\n          intro._options.hintPosition) as HintPosition,\n        hintAnimation,\n        tooltipClass:\n          currentElement.getAttribute(\"data-tooltip-class\") || undefined,\n        position: (currentElement.getAttribute(\"data-position\") ||\n          intro._options.tooltipPosition) as TooltipPosition,\n      });\n    }\n  }\n\n  await addHints(intro);\n\n  DOMEvent.on(document, \"click\", removeHintTooltip, intro, false);\n  DOMEvent.on(window, \"resize\", reAlignHints, intro, true);\n\n  return true;\n}\n\n/**\n * Re-aligns all hint elements\n *\n * @api private\n */\nexport function reAlignHints(intro: IntroJs) {\n  for (const { hintTargetElement, hintPosition, element } of intro._hintItems) {\n    alignHintPosition(hintPosition, element as HTMLElement, hintTargetElement);\n  }\n}\n", "import { IntroJs } from \"../intro\";\nimport cloneObject from \"../util/cloneObject\";\nimport createElement from \"../util/createElement\";\nimport { IntroStep, ScrollTo, TooltipPosition } from \"./steps\";\n\n/**\n * Finds all Intro steps from the data-* attributes and the options.steps array\n *\n * @api private\n */\nexport default function fetchIntroSteps(\n  intro: IntroJs,\n  targetElm: HTMLElement\n) {\n  const allIntroSteps: HTMLElement[] = Array.from(\n    targetElm.querySelectorAll(\"*[data-intro]\")\n  );\n  let introItems: IntroStep[] = [];\n\n  if (intro._options.steps && intro._options.steps.length) {\n    //use steps passed programmatically\n    for (const step of intro._options.steps) {\n      const currentItem = cloneObject(step);\n\n      //set the step\n      currentItem.step = introItems.length + 1;\n\n      currentItem.title = currentItem.title || \"\";\n\n      //use querySelector function only when developer used CSS selector\n      if (typeof currentItem.element === \"string\") {\n        //grab the element with given selector from the page\n        currentItem.element =\n          document.querySelector<HTMLElement>(currentItem.element) || undefined;\n      }\n\n      //intro without element\n      if (\n        typeof currentItem.element === \"undefined\" ||\n        currentItem.element === null\n      ) {\n        let floatingElementQuery = document.querySelector<HTMLElement>(\n          \".introjsFloatingElement\"\n        );\n\n        if (floatingElementQuery === null) {\n          floatingElementQuery = createElement(\"div\", {\n            className: \"introjsFloatingElement\",\n          });\n\n          document.body.appendChild(floatingElementQuery);\n        }\n\n        currentItem.element = floatingElementQuery;\n        currentItem.position = \"floating\";\n      }\n\n      currentItem.position =\n        currentItem.position ||\n        (intro._options.tooltipPosition as TooltipPosition);\n      currentItem.scrollTo = currentItem.scrollTo || intro._options.scrollTo;\n\n      if (typeof currentItem.disableInteraction === \"undefined\") {\n        currentItem.disableInteraction = intro._options.disableInteraction;\n      }\n\n      if (currentItem.element !== null) {\n        introItems.push(currentItem as IntroStep);\n      }\n    }\n  } else {\n    //use steps from data-* annotations\n    const elmsLength = allIntroSteps.length;\n    let disableInteraction: boolean;\n\n    //if there's no element to intro\n    if (elmsLength < 1) {\n      return [];\n    }\n\n    for (const currentElement of allIntroSteps) {\n      // start intro for groups of elements\n      if (\n        intro._options.group &&\n        currentElement.getAttribute(\"data-intro-group\") !== intro._options.group\n      ) {\n        continue;\n      }\n\n      // skip hidden elements\n      if (currentElement.style.display === \"none\") {\n        continue;\n      }\n\n      const step = parseInt(currentElement.getAttribute(\"data-step\") || \"\", 10);\n\n      disableInteraction = intro._options.disableInteraction;\n      if (currentElement.hasAttribute(\"data-disable-interaction\")) {\n        disableInteraction = !!currentElement.getAttribute(\n          \"data-disable-interaction\"\n        );\n      }\n\n      if (step > 0) {\n        introItems[step - 1] = {\n          step: step,\n          element: currentElement,\n          title: currentElement.getAttribute(\"data-title\") || \"\",\n          intro: currentElement.getAttribute(\"data-intro\") || \"\",\n          tooltipClass:\n            currentElement.getAttribute(\"data-tooltip-class\") || undefined,\n          highlightClass:\n            currentElement.getAttribute(\"data-highlight-class\") || undefined,\n          position: (currentElement.getAttribute(\"data-position\") ||\n            intro._options.tooltipPosition) as TooltipPosition,\n          scrollTo:\n            (currentElement.getAttribute(\"data-scroll-to\") as ScrollTo) ||\n            intro._options.scrollTo,\n          disableInteraction,\n        };\n      }\n    }\n\n    //next add intro items without data-step\n    //todo: we need a cleanup here, two loops are redundant\n    let nextStep = 0;\n\n    for (const currentElement of allIntroSteps) {\n      // start intro for groups of elements\n      if (\n        intro._options.group &&\n        currentElement.getAttribute(\"data-intro-group\") !== intro._options.group\n      ) {\n        continue;\n      }\n\n      if (currentElement.getAttribute(\"data-step\") === null) {\n        while (true) {\n          if (typeof introItems[nextStep] === \"undefined\") {\n            break;\n          } else {\n            nextStep++;\n          }\n        }\n\n        if (currentElement.hasAttribute(\"data-disable-interaction\")) {\n          disableInteraction = !!currentElement.getAttribute(\n            \"data-disable-interaction\"\n          );\n        } else {\n          disableInteraction = intro._options.disableInteraction;\n        }\n\n        introItems[nextStep] = {\n          element: currentElement,\n          title: currentElement.getAttribute(\"data-title\") || \"\",\n          intro: currentElement.getAttribute(\"data-intro\") || \"\",\n          step: nextStep + 1,\n          tooltipClass:\n            currentElement.getAttribute(\"data-tooltip-class\") || undefined,\n          highlightClass:\n            currentElement.getAttribute(\"data-highlight-class\") || undefined,\n          position: (currentElement.getAttribute(\"data-position\") ||\n            intro._options.tooltipPosition) as TooltipPosition,\n          scrollTo:\n            (currentElement.getAttribute(\"data-scroll-to\") as ScrollTo) ||\n            intro._options.scrollTo,\n          disableInteraction,\n        };\n      }\n    }\n  }\n\n  //removing undefined/null elements\n  const tempIntroItems = [];\n  for (let z = 0; z < introItems.length; z++) {\n    if (introItems[z]) {\n      // copy non-falsy values to the end of the array\n      tempIntroItems.push(introItems[z]);\n    }\n  }\n\n  introItems = tempIntroItems;\n\n  //Ok, sort all items with given steps\n  introItems.sort((a, b) => a.step - b.step);\n\n  return introItems;\n}\n", "import { reAlignHints } from \"./hint\";\nimport setHelperLayerPosition from \"./setHelperLayerPosition\";\nimport placeTooltip from \"./placeTooltip\";\nimport fetchIntroSteps from \"./fetchIntroSteps\";\nimport { _recreateBullets, _updateProgressBar } from \"./showElement\";\nimport { IntroJs } from \"../intro\";\n\n/**\n * Update placement of the intro objects on the screen\n * @api private\n */\nexport default function refresh(intro: IntroJs, refreshSteps?: boolean) {\n  const currentStep = intro._currentStep;\n\n  if (currentStep === undefined || currentStep === null || currentStep == -1)\n    return;\n\n  const step = intro._introItems[currentStep];\n\n  const referenceLayer = document.querySelector<HTMLElement>(\n    \".introjs-tooltipReferenceLayer\"\n  ) as HTMLElement;\n  const helperLayer = document.querySelector<HTMLElement>(\n    \".introjs-helperLayer\"\n  ) as HTMLElement;\n  const disableInteractionLayer = document.querySelector<HTMLElement>(\n    \".introjs-disableInteraction\"\n  ) as HTMLElement;\n\n  // re-align intros\n  setHelperLayerPosition(intro, step, helperLayer);\n  setHelperLayerPosition(intro, step, referenceLayer);\n  setHelperLayerPosition(intro, step, disableInteractionLayer);\n\n  if (refreshSteps) {\n    intro._introItems = fetchIntroSteps(intro, intro._targetElement);\n    _recreateBullets(intro, step);\n    _updateProgressBar(referenceLayer, currentStep, intro._introItems.length);\n  }\n\n  // re-align tooltip\n  const oldArrowLayer = document.querySelector<HTMLElement>(\".introjs-arrow\");\n  const oldTooltipContainer =\n    document.querySelector<HTMLElement>(\".introjs-tooltip\");\n\n  if (oldTooltipContainer && oldArrowLayer) {\n    placeTooltip(\n      intro,\n      intro._introItems[currentStep],\n      oldTooltipContainer,\n      oldArrowLayer\n    );\n  }\n\n  //re-align hints\n  reAlignHints(intro);\n\n  return intro;\n}\n", "import { IntroJs } from \"../intro\";\nimport refresh from \"./refresh\";\n\nexport default function onResize(intro: IntroJs) {\n  refresh(intro);\n}\n", "import setStyle from \"./setStyle\";\n\n/**\n * Removes `element` from `parentElement`\n */\nexport default function removeChild(\n  element: HTMLElement | null,\n  animate = false\n) {\n  if (!element || !element.parentElement) return;\n\n  const parentElement = element.parentElement;\n\n  if (animate) {\n    setStyle(element, {\n      opacity: \"0\",\n    });\n\n    window.setTimeout(() => {\n      try {\n        // removeChild(..) throws an exception if the child has already been removed (https://developer.mozilla.org/en-US/docs/Web/API/Node/removeChild)\n        // this try-catch is added to make sure this function doesn't throw an exception if the child has been removed\n        // this scenario can happen when start()/exit() is called multiple times and the helper<PERSON>ayer is removed by the\n        // previous exit() call (note: this is a timeout)\n        parentElement.removeChild(element);\n      } catch (e) {}\n    }, 500);\n  } else {\n    parentElement.removeChild(element);\n  }\n}\n", "import DOMEvent from \"./DOMEvent\";\nimport onKeyDown from \"./onKeyDown\";\nimport onResize from \"./onResize\";\nimport removeShowElement from \"./removeShowElement\";\nimport removeChild from \"../util/removeChild\";\nimport { IntroJs } from \"../intro\";\nimport isFunction from \"../util/isFunction\";\n\n/**\n * Exit from intro\n *\n * @api private\n * @param {Boolean} force - Setting to `true` will skip the result of beforeExit callback\n */\nexport default async function exitIntro(\n  intro: IntroJs,\n  targetElement: HTMLElement,\n  force: boolean = false\n) {\n  let continueExit = true;\n\n  // calling onbeforeexit callback\n  //\n  // If this callback return `false`, it would halt the process\n  if (intro._introBeforeExitCallback !== undefined) {\n    continueExit = await intro._introBeforeExitCallback.call(\n      intro,\n      targetElement\n    );\n  }\n\n  // skip this check if `force` parameter is `true`\n  // otherwise, if `onbeforeexit` returned `false`, don't exit the intro\n  if (!force && continueExit === false) return;\n\n  // remove overlay layers from the page\n  const overlayLayers = Array.from(\n    targetElement.querySelectorAll<HTMLElement>(\".introjs-overlay\")\n  );\n\n  if (overlayLayers && overlayLayers.length) {\n    for (const overlayLayer of overlayLayers) {\n      removeChild(overlayLayer);\n    }\n  }\n\n  //remove all helper layers\n  const helperLayer = targetElement.querySelector<HTMLElement>(\n    \".introjs-helperLayer\"\n  );\n  removeChild(helperLayer, true);\n\n  const referenceLayer = targetElement.querySelector<HTMLElement>(\n    \".introjs-tooltipReferenceLayer\"\n  );\n  removeChild(referenceLayer);\n\n  //remove disableInteractionLayer\n  const disableInteractionLayer = targetElement.querySelector<HTMLElement>(\n    \".introjs-disableInteraction\"\n  );\n  removeChild(disableInteractionLayer);\n\n  //remove intro floating element\n  const floatingElement = document.querySelector<HTMLElement>(\n    \".introjsFloatingElement\"\n  );\n  removeChild(floatingElement);\n\n  removeShowElement();\n\n  //clean listeners\n  DOMEvent.off(window, \"keydown\", onKeyDown, intro, true);\n  DOMEvent.off(window, \"resize\", onResize, intro, true);\n\n  //check if any callback is defined\n  if (isFunction(intro._introExitCallback)) {\n    await intro._introExitCallback.call(intro);\n  }\n\n  // set the step to default\n  intro._currentStep = -1;\n}\n", "import exitIntro from \"./exitIntro\";\nimport createElement from \"../util/createElement\";\nimport setStyle from \"../util/setStyle\";\nimport { IntroJs } from \"../intro\";\n\n/**\n * Add overlay layer to the page\n *\n * @api private\n */\nexport default function addOverlayLayer(\n  intro: IntroJs,\n  targetElm: HTMLElement\n) {\n  const overlayLayer = createElement(\"div\", {\n    className: \"introjs-overlay\",\n  });\n\n  setStyle(overlayLayer, {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n    position: \"fixed\",\n  });\n\n  targetElm.appendChild(overlayLayer);\n\n  if (intro._options.exitOnOverlayClick === true) {\n    setStyle(overlayLayer, {\n      cursor: \"pointer\",\n    });\n\n    overlayLayer.onclick = async () => {\n      await exitIntro(intro, targetElm);\n    };\n  }\n\n  return true;\n}\n", "import addOverlayLayer from \"./addOverlayLayer\";\nimport DOM<PERSON><PERSON> from \"./DOMEvent\";\nimport { nextStep } from \"./steps\";\nimport onKeyDown from \"./onKeyDown\";\nimport onResize from \"./onResize\";\nimport fetchIntroSteps from \"./fetchIntroSteps\";\nimport { IntroJs } from \"../intro\";\nimport isFunction from \"../util/isFunction\";\n\n/**\n * Initiate a new introduction/guide from an element in the page\n *\n * @api private\n */\nexport default async function introForElement(\n  intro: IntroJs,\n  targetElm: HTMLElement\n): Promise<Boolean> {\n  // don't start the tour if the instance is not active\n  if (!intro.isActive()) return false;\n\n  if (isFunction(intro._introStartCallback)) {\n    await intro._introStartCallback.call(intro, targetElm);\n  }\n\n  //set it to the introJs object\n  const steps = fetchIntroSteps(intro, targetElm);\n\n  if (steps.length === 0) {\n    return false;\n  }\n\n  intro._introItems = steps;\n\n  //add overlay layer to the page\n  if (addOverlayLayer(intro, targetElm)) {\n    //then, start the show\n    await nextStep(intro);\n\n    targetElm.addEventListener;\n    if (intro._options.keyboardNavigation) {\n      DOMEvent.on(window, \"keydown\", onKeyDown, intro, true);\n    }\n\n    //for window resize\n    DOMEvent.on(window, \"resize\", onResize, intro, true);\n  }\n\n  return false;\n}\n", "import {\n  HintPosition,\n  HintStep,\n  IntroStep,\n  ScrollTo,\n  TooltipPosition,\n} from \"./core/steps\";\n\nexport interface Options {\n  steps: Partial<IntroStep>[];\n  hints: Partial<HintStep>[];\n  /* Is this tour instance active? Don't show the tour again if this flag is set to false */\n  isActive: boolean;\n  /* Next button label in tooltip box */\n  nextLabel: string;\n  /* Previous button label in tooltip box */\n  prevLabel: string;\n  /* Skip button label in tooltip box */\n  skipLabel: string;\n  /* Done button label in tooltip box */\n  doneLabel: string;\n  /* Hide previous button in the first step? Otherwise, it will be disabled button. */\n  hidePrev: boolean;\n  /* Hide next button in the last step? Otherwise, it will be disabled button (note: this will also hide the \"Done\" button) */\n  hideNext: boolean;\n  /* Change the Next button to Done in the last step of the intro? otherwise, it will render a disabled button */\n  nextToDone: boolean;\n  /* Default tooltip box position */\n  tooltipPosition: string;\n  /* Next CSS class for tooltip boxes */\n  tooltipClass: string;\n  /* Start intro for a group of elements */\n  group: string;\n  /* CSS class that is added to the helperLayer */\n  highlightClass: string;\n  /* Close introduction when pressing Escape button? */\n  exitOnEsc: boolean;\n  /* Close introduction when clicking on overlay layer? */\n  exitOnOverlayClick: boolean;\n  /* Display the pagination detail */\n  showStepNumbers: boolean;\n  /* Pagination \"of\" label */\n  stepNumbersOfLabel: string;\n  /* Let user use keyboard to navigate the tour? */\n  keyboardNavigation: boolean;\n  /* Show tour control buttons? */\n  showButtons: boolean;\n  /* Show tour bullets? */\n  showBullets: boolean;\n  /* Show tour progress? */\n  showProgress: boolean;\n  /* Scroll to highlighted element? */\n  scrollToElement: boolean;\n  /*\n   * Should we scroll the tooltip or target element?\n   * Options are: 'element', 'tooltip' or 'off'\n   */\n  scrollTo: ScrollTo;\n  /* Padding to add after scrolling when element is not in the viewport (in pixels) */\n  scrollPadding: number;\n  /* Set the overlay opacity */\n  overlayOpacity: number;\n  /* To determine the tooltip position automatically based on the window.width/height */\n  autoPosition: boolean;\n  /* Precedence of positions, when auto is enabled */\n  positionPrecedence: TooltipPosition[];\n  /* Disable an interaction with element? */\n  disableInteraction: boolean;\n  /* To display the \"Don't show again\" checkbox in the tour */\n  dontShowAgain: boolean;\n  dontShowAgainLabel: string;\n  /* \"Don't show again\" cookie name and expiry (in days) */\n  dontShowAgainCookie: string;\n  dontShowAgainCookieDays: number;\n  /* Set how much padding to be used around helper element */\n  helperElementPadding: number;\n  /* Default hint position */\n  hintPosition: HintPosition;\n  /* Hint button label */\n  hintButtonLabel: string;\n  /* Display the \"Got it\" button? */\n  hintShowButton: boolean;\n  /* Hints auto-refresh interval in ms (set to -1 to disable) */\n  hintAutoRefreshInterval: number;\n  /* Adding animation to hints? */\n  hintAnimation: boolean;\n  /* additional classes to put on the buttons */\n  buttonClass: string;\n  /* additional classes to put on progress bar */\n  progressBarAdditionalClass: boolean;\n}\n\nexport function getDefaultOptions(): Options {\n  return {\n    steps: [],\n    hints: [],\n    isActive: true,\n    nextLabel: \"Next\",\n    prevLabel: \"Back\",\n    skipLabel: \"×\",\n    doneLabel: \"Done\",\n    hidePrev: false,\n    hideNext: false,\n    nextToDone: true,\n    tooltipPosition: \"bottom\",\n    tooltipClass: \"\",\n    group: \"\",\n    highlightClass: \"\",\n    exitOnEsc: true,\n    exitOnOverlayClick: true,\n    showStepNumbers: false,\n    stepNumbersOfLabel: \"of\",\n    keyboardNavigation: true,\n    showButtons: true,\n    showBullets: true,\n    showProgress: false,\n    scrollToElement: true,\n    scrollTo: \"element\",\n    scrollPadding: 30,\n    overlayOpacity: 0.5,\n    autoPosition: true,\n    positionPrecedence: [\"bottom\", \"top\", \"right\", \"left\"],\n    disableInteraction: false,\n\n    dontShowAgain: false,\n    dontShowAgainLabel: \"Don't show this again\",\n    dontShowAgainCookie: \"introjs-dontShowAgain\",\n    dontShowAgainCookieDays: 365,\n    helperElementPadding: 10,\n\n    hintPosition: \"top-middle\",\n    hintButtonLabel: \"Got it\",\n    hintShowButton: true,\n    hintAutoRefreshInterval: 10,\n    hintAnimation: true,\n    buttonClass: \"introjs-button\",\n    progressBarAdditionalClass: false,\n  };\n}\n\nexport function setOption<K extends keyof Options>(\n  options: Options,\n  key: K,\n  value: Options[K]\n): Options {\n  options[key] = value;\n  return options;\n}\n\nexport function setOptions(\n  options: Options,\n  partialOptions: Partial<Options>\n): Options {\n  for (const [key, value] of Object.entries(partialOptions)) {\n    options = setOption(options, key as keyof Options, value);\n  }\n  return options;\n}\n", "import { getDontShowAgain, setDontShowAgain } from \"./core/dontShowAgain\";\nimport exitIntro from \"./core/exitIntro\";\nimport {\n  hideHint,\n  hideHints,\n  populateHints,\n  removeHint,\n  removeHints,\n  showHint,\n  showHintDialog,\n  showHints,\n} from \"./core/hint\";\nimport introForElement from \"./core/introForElement\";\nimport refresh from \"./core/refresh\";\nimport {\n  HintStep,\n  IntroStep,\n  goToStep,\n  goToStepNumber,\n  nextStep,\n  previousStep,\n} from \"./core/steps\";\nimport { Options, getDefaultOptions, setOption, setOptions } from \"./option\";\nimport isFunction from \"./util/isFunction\";\n\ntype introBeforeChangeCallback = (\n  this: IntroJs,\n  targetElement: HTMLElement,\n  currentStep: number,\n  direction: \"backward\" | \"forward\"\n) => Promise<boolean> | boolean;\ntype introChangeCallback = (\n  this: IntroJs,\n  targetElement: HTMLElement\n) => void | Promise<void>;\ntype introAfterChangeCallback = (\n  this: IntroJs,\n  targetElement: HTMLElement\n) => void | Promise<void>;\ntype introCompleteCallback = (\n  this: IntroJs,\n  currentStep: number,\n  reason: \"skip\" | \"end\" | \"done\"\n) => void | Promise<void>;\ntype introStartCallback = (\n  this: IntroJs,\n  targetElement: HTMLElement\n) => void | Promise<void>;\ntype introExitCallback = (this: IntroJs) => void | Promise<void>;\ntype introSkipCallback = (\n  this: IntroJs,\n  currentStep: number\n) => void | Promise<void>;\ntype introBeforeExitCallback = (\n  this: IntroJs,\n  targetElement: HTMLElement\n) => boolean | Promise<boolean>;\ntype hintsAddedCallback = (this: IntroJs) => void | Promise<void>;\ntype hintClickCallback = (\n  this: IntroJs,\n  hintElement: HTMLElement,\n  item: HintStep,\n  stepId: number\n) => void | Promise<void>;\ntype hintCloseCallback = (\n  this: IntroJs,\n  stepId: number\n) => void | Promise<void>;\n\nexport class IntroJs {\n  public _currentStep: number = -1;\n  public _currentStepNumber: number | undefined;\n  public _direction: \"forward\" | \"backward\";\n  public _targetElement: HTMLElement;\n  public _introItems: IntroStep[] = [];\n  public _hintItems: HintStep[] = [];\n  public _options: Options;\n  public _introBeforeChangeCallback?: introBeforeChangeCallback;\n  public _introChangeCallback?: introChangeCallback;\n  public _introAfterChangeCallback?: introAfterChangeCallback;\n  public _introCompleteCallback?: introCompleteCallback;\n  public _introStartCallback?: introStartCallback;\n  public _introExitCallback?: introExitCallback;\n  public _introSkipCallback?: introSkipCallback;\n  public _introBeforeExitCallback?: introBeforeExitCallback;\n\n  public _hintsAddedCallback?: hintsAddedCallback;\n  public _hintClickCallback?: hintClickCallback;\n  public _hintCloseCallback?: hintCloseCallback;\n\n  public _lastShowElementTimer: number;\n  public _hintsAutoRefreshFunction: (...args: any[]) => void;\n\n  public constructor(targetElement: HTMLElement) {\n    this._targetElement = targetElement;\n    this._options = getDefaultOptions();\n  }\n\n  isActive() {\n    if (this._options.dontShowAgain && getDontShowAgain(this)) {\n      return false;\n    }\n\n    return this._options.isActive;\n  }\n\n  clone() {\n    return new IntroJs(this._targetElement);\n  }\n\n  setOption<K extends keyof Options>(key: K, value: Options[K]) {\n    this._options = setOption(this._options, key, value);\n    return this;\n  }\n\n  setOptions(partialOptions: Partial<Options>) {\n    this._options = setOptions(this._options, partialOptions);\n    return this;\n  }\n\n  async start() {\n    await introForElement(this, this._targetElement);\n    return this;\n  }\n\n  async goToStep(step: number) {\n    await goToStep(this, step);\n    return this;\n  }\n\n  addStep(step: Partial<IntroStep>) {\n    if (!this._options.steps) {\n      this._options.steps = [];\n    }\n\n    this._options.steps.push(step);\n\n    return this;\n  }\n\n  addSteps(steps: Partial<IntroStep>[]) {\n    if (!steps.length) return this;\n\n    for (let index = 0; index < steps.length; index++) {\n      this.addStep(steps[index]);\n    }\n\n    return this;\n  }\n\n  async goToStepNumber(step: number) {\n    await goToStepNumber(this, step);\n    return this;\n  }\n\n  async nextStep() {\n    await nextStep(this);\n    return this;\n  }\n\n  async previousStep() {\n    await previousStep(this);\n    return this;\n  }\n\n  currentStep() {\n    return this._currentStep;\n  }\n\n  async exit(force: boolean) {\n    await exitIntro(this, this._targetElement, force);\n    return this;\n  }\n\n  refresh(refreshSteps?: boolean) {\n    refresh(this, refreshSteps);\n    return this;\n  }\n\n  setDontShowAgain(dontShowAgain: boolean) {\n    setDontShowAgain(this, dontShowAgain);\n    return this;\n  }\n\n  onbeforechange(providedCallback: introBeforeChangeCallback) {\n    if (isFunction(providedCallback)) {\n      this._introBeforeChangeCallback = providedCallback;\n    } else {\n      throw new Error(\n        \"Provided callback for onbeforechange was not a function\"\n      );\n    }\n    return this;\n  }\n\n  onchange(providedCallback: introChangeCallback) {\n    if (isFunction(providedCallback)) {\n      this._introChangeCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onchange was not a function.\");\n    }\n    return this;\n  }\n\n  onafterchange(providedCallback: introAfterChangeCallback) {\n    if (isFunction(providedCallback)) {\n      this._introAfterChangeCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onafterchange was not a function\");\n    }\n    return this;\n  }\n\n  oncomplete(providedCallback: introCompleteCallback) {\n    if (isFunction(providedCallback)) {\n      this._introCompleteCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for oncomplete was not a function.\");\n    }\n    return this;\n  }\n\n  onhintsadded(providedCallback: hintsAddedCallback) {\n    if (isFunction(providedCallback)) {\n      this._hintsAddedCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onhintsadded was not a function.\");\n    }\n    return this;\n  }\n\n  onhintclick(providedCallback: hintClickCallback) {\n    if (isFunction(providedCallback)) {\n      this._hintClickCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onhintclick was not a function.\");\n    }\n    return this;\n  }\n\n  onhintclose(providedCallback: hintCloseCallback) {\n    if (isFunction(providedCallback)) {\n      this._hintCloseCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onhintclose was not a function.\");\n    }\n    return this;\n  }\n\n  onstart(providedCallback: introStartCallback) {\n    if (isFunction(providedCallback)) {\n      this._introStartCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onstart was not a function.\");\n    }\n    return this;\n  }\n\n  onexit(providedCallback: introExitCallback) {\n    if (isFunction(providedCallback)) {\n      this._introExitCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onexit was not a function.\");\n    }\n    return this;\n  }\n\n  onskip(providedCallback: introSkipCallback) {\n    if (isFunction(providedCallback)) {\n      this._introSkipCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onskip was not a function.\");\n    }\n    return this;\n  }\n\n  onbeforeexit(providedCallback: introBeforeExitCallback) {\n    if (isFunction(providedCallback)) {\n      this._introBeforeExitCallback = providedCallback;\n    } else {\n      throw new Error(\"Provided callback for onbeforeexit was not a function.\");\n    }\n    return this;\n  }\n\n  async addHints() {\n    await populateHints(this, this._targetElement);\n    return this;\n  }\n\n  async hideHint(stepId: number) {\n    await hideHint(this, stepId);\n    return this;\n  }\n\n  async hideHints() {\n    await hideHints(this);\n    return this;\n  }\n\n  showHint(stepId: number) {\n    showHint(stepId);\n    return this;\n  }\n\n  async showHints() {\n    await showHints(this);\n    return this;\n  }\n\n  removeHints() {\n    removeHints(this);\n    return this;\n  }\n\n  removeHint(stepId: number) {\n    removeHint(stepId);\n    return this;\n  }\n\n  async showHintDialog(stepId: number) {\n    await showHintDialog(this, stepId);\n    return this;\n  }\n}\n", "import { version } from \"../package.json\";\nimport { IntroJs } from \"./intro\";\nimport stamp from \"./util/stamp\";\n\n/**\n * Create a new IntroJS instance\n *\n * @param targetElm Optional target element to start the tour/hint on\n * @returns\n */\nconst introJs = (targetElm?: string | HTMLElement) => {\n  let instance: IntroJs;\n\n  if (typeof targetElm === \"object\") {\n    instance = new IntroJs(targetElm);\n  } else if (typeof targetElm === \"string\") {\n    //select the target element with query selector\n    const targetElement = document.querySelector<HTMLElement>(targetElm);\n\n    if (targetElement) {\n      instance = new IntroJs(targetElement);\n    } else {\n      throw new Error(\"There is no element with given selector.\");\n    }\n  } else {\n    instance = new IntroJs(document.body);\n  }\n  // add instance to list of _instances\n  // passing group to stamp to increment\n  // from 0 onward somewhat reliably\n  introJs.instances[stamp(instance, \"introjs-instance\")] = instance;\n\n  return instance;\n};\n\n/**\n * Current IntroJs version\n *\n * @property version\n * @type String\n */\nintroJs.version = version;\n\n/**\n * key-val object helper for introJs instances\n *\n * @property instances\n * @type Object\n */\nintroJs.instances = {} as { [key: number]: IntroJs };\n\nexport default introJs;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "name", "value", "days", "_cookie", "cookie", "_defineProperty", "undefined", "date", "Date", "setTime", "getTime", "expires", "toUTCString", "arr", "key", "push", "concat", "document", "join", "<PERSON><PERSON><PERSON><PERSON>", "split", "for<PERSON>ach", "el", "_el$split2", "_slicedToArray", "k", "v", "trim", "dontShowAgainCookieValue", "setDontShowAgain", "intro", "dontShowAgain", "_options", "dontShowAgainCookie", "dontShowAgainCookieDays", "keys", "stamp", "obj", "arguments", "length", "DOMEvent$1", "DOMEvent", "_classCallCheck", "_createClass", "type", "listener", "context", "useCapture", "id", "this", "_id", "handler", "e", "window", "event", "addEventListener", "attachEvent", "events_key", "removeEventListener", "detachEvent", "isFunction", "x", "addClass", "element", "className", "SVGElement", "pre", "getAttribute", "match", "setAttribute", "classList", "_step", "_iterator", "_createForOfIteratorHelper", "s", "n", "done", "cls", "add", "err", "f", "getPropValue", "propName", "propValue", "currentStyle", "defaultView", "getComputedStyle", "getPropertyValue", "toLowerCase", "setShowElement", "targetElement", "currentElementPosition", "scrollParentToElement", "scrollToElement", "parent", "style", "excludeStaticParent", "position", "overflowRegex", "body", "parentElement", "test", "overflow", "overflowY", "overflowX", "getScrollParent", "scrollTop", "offsetTop", "getWinSize", "innerWidth", "width", "height", "innerHeight", "D", "documentElement", "clientWidth", "clientHeight", "scrollTo", "scrollPadding", "tooltipLayer", "rect", "getBoundingClientRect", "top", "left", "bottom", "right", "elementInViewport", "winHeight", "getWindowSize", "scrollBy", "setAnchorAsButton", "anchor", "tabIndex", "isFixed", "nodeName", "getOffset", "relativeEl", "docEl", "pageYOffset", "scrollLeft", "pageXOffset", "xr", "relativeElPosition", "tagName", "Object", "assign", "removeClass", "classNameRegex", "replace", "setStyle", "cssText", "rule", "setHelperLayerPosition", "step", "helper<PERSON>ayer", "elementPosition", "_targetElement", "widthHeightPadding", "helperElementPadding", "Element", "checkRight", "targetOffset", "tooltipLayerStyleLeft", "tooltipOffset", "windowSize", "checkLeft", "tooltipLayerStyleRight", "removeEntry", "stringArray", "stringToRemove", "includes", "splice", "indexOf", "_determineAutoPosition", "positionPrecedence", "desiredTooltipPosition", "possiblePositions", "slice", "tooltipHeight", "tooltipWidth", "targetElementRect", "calculatedPosition", "defaultAlignment", "desiredAlignment", "offsetLeft", "windowWidth", "halfTooltipWidth", "winWidth", "Math", "min", "screen", "_determineAutoAlignment", "placeTooltip", "currentStep", "<PERSON><PERSON><PERSON><PERSON>", "hintMode", "currentTooltipPosition", "tooltipCssClass", "marginLeft", "marginTop", "display", "tooltipClass", "filter", "Boolean", "autoPosition", "tooltipLayerStyleLeftRight", "showStepNumbers", "removeShowElement", "_i", "_elms", "Array", "from", "querySelectorAll", "_createElement", "attrs", "createElement", "setAttRegex", "append<PERSON><PERSON><PERSON>", "animate", "existingOpacity", "opacity", "setTimeout", "_getProgress", "introItemsLength", "_disableInteraction", "disableInteractionLayer", "querySelector", "_createBullets", "<PERSON><PERSON>ayer", "showBullets", "<PERSON><PERSON><PERSON><PERSON>", "anchorClick", "<PERSON><PERSON><PERSON><PERSON>", "goToStep", "parseInt", "i", "_introItems", "innerLi", "anchorLink", "onclick", "innerHTML", "toString", "_updateBullets", "oldReference<PERSON><PERSON>er", "oldRefActiveBullet", "oldRefBulletStepNumber", "_createProgressBar", "progressLayer", "showProgress", "progressBar", "progressBarAdditionalClass", "progress", "_currentStep", "_updateProgressBar", "_showElement", "_x", "_x2", "_showElement2", "apply", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highlightClass", "nextTooltipButton", "prevTooltipButton", "skip<PERSON><PERSON>tipB<PERSON><PERSON>", "oldHelperNumberLayer", "oldTooltipLayer", "oldTooltipTitleLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldTooltipContainer", "<PERSON><PERSON><PERSON><PERSON>", "tooltipTextLayer", "tooltipHeaderLayer", "tooltipTitleLayer", "<PERSON><PERSON>ayer", "dontShowAgainWrapper", "dontShowAgainCheckbox", "dontShowAgainCheckboxLabel", "helper<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wrap", "_context4", "prev", "next", "_introC<PERSON><PERSON><PERSON><PERSON>back", "call", "_lastShowElementTimer", "clearTimeout", "stepNumbersOfLabel", "title", "focus", "overlayOpacity", "showButtons", "onchange", "target", "checked", "htmlFor", "innerText", "dontShowAgainLabel", "_callee", "_context", "nextStep", "_introCompleteCallback", "exitIntro", "stop", "next<PERSON><PERSON><PERSON>", "_callee2", "_context2", "previousStep", "prevLabel", "<PERSON><PERSON><PERSON><PERSON>", "_callee3", "_context3", "_introSkipCallback", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "disableInteraction", "buttonClass", "hide<PERSON><PERSON>v", "hideNext", "nextToDone", "doneLabel", "_introAfter<PERSON>hangeCallback", "_goToStep", "goToStepNumber", "_x3", "_x4", "_goToStepNumber", "_currentStepNumber", "_x5", "_nextStep", "continueStep", "_direction", "_introBeforeChangeCallback", "sent", "abrupt", "showElement", "_x6", "_previousStep", "onKeyDown", "_onKeyDown", "code", "which", "charCode", "keyCode", "exitOnEsc", "srcElement", "click", "preventDefault", "returnValue", "cloneObject", "source", "_typeof", "temp", "j<PERSON><PERSON><PERSON>", "debounce", "func", "timeout", "timer", "_len", "args", "_key", "hintQuerySelectorAll", "selector", "hintsWrapper", "hideHint", "_hideHint", "stepId", "hint", "removeHintTooltip", "_hintCloseCallback", "hideHints", "_hideHints", "hints", "_iterator3", "_step3", "t0", "finish", "showHints", "_showHints", "_iterator4", "_step4", "showHint", "populateHints", "removeHint", "addHints", "_addHints", "getHintClick", "item", "hintDot", "hintPulse", "evt", "stopPropagation", "cancelBubble", "showHintDialog", "_hintItems", "hintAnimation", "hintTargetElement", "alignHintPosition", "hintPosition", "_hintsAddedCallback", "hintAutoRefreshInterval", "_hintsAutoRefreshFunction", "reAlignHints", "on", "hintElement", "offset", "iconWidth", "iconHeight", "_x7", "_showHintDialog", "_callee5", "removedStep", "tooltipWrapper", "closeButton", "_context5", "_hintClickCallback", "hintShowButton", "hintButtonLabel", "tooltip", "_x8", "_x9", "_populateHints", "_callee6", "targetElm", "_iterator5", "_step5", "currentItem", "_hints", "currentElement", "hintAnimationAttr", "_context6", "tooltipPosition", "_step2", "_iterator2", "_step2$value", "fetchIntroSteps", "allIntroSteps", "introItems", "steps", "floatingElementQuery", "group", "hasAttribute", "tempIntroItems", "z", "sort", "a", "b", "refresh", "refreshSteps", "existing", "<PERSON><PERSON><PERSON><PERSON>", "_recreateBullets", "onResize", "_exitIntro", "force", "continueExit", "overlayLayers", "_args", "_introBeforeExitCallback", "off", "_introExitCallback", "addOverlayLayer", "overlayLayer", "exitOnOverlayClick", "cursor", "introForElement", "_introForElement", "isActive", "_introStartCallback", "keyboardNavigation", "setOption", "options", "IntroJs", "_showHintDialog2", "_showHints2", "_hideHints2", "_hideHint2", "_exit", "_previousStep2", "_nextStep2", "_goToStepNumber2", "_goToStep2", "_start", "dontShow<PERSON><PERSON><PERSON>", "partialOptions", "_Object$entries", "entries", "_Object$entries$_i", "setOptions", "index", "addStep", "providedCallback", "Error", "_callee7", "_context7", "_callee8", "_context8", "_callee9", "_context9", "_callee10", "_context10", "removeHints", "_callee11", "_context11", "introJs", "instance", "instances", "version"], "mappings": ";;;;;;;;;spTAAO,SAASA,EAAUC,EAAcC,EAAeC,GAAe,IAAAC,EAC9DC,GAILC,EAAAF,EAAAE,GAAML,EAAOC,GAAKI,EAAAF,EAAQ,OAAA,KAAGE,EAAAF,EAAWG,eAAAA,GAASH,GAElD,GAAID,EAAM,CACR,IAAIK,EAAO,IAAIC,KACfD,EAAKE,QAAQF,EAAKG,UAAmB,GAAPR,EAAY,GAAK,GAAK,KACpDE,EAAOO,QAAUJ,EAAKK,aACxB,CAEA,IAAIC,EAAM,GACV,IAAK,IAAIC,KAAOV,EACdS,EAAIE,KAAIC,GAAAA,OAAIF,EAAG,KAAAE,OAAIZ,EAAOU,KAK5B,OAFAG,SAASb,OAASS,EAAIK,KAAK,MAEpBC,EAAUnB,EACnB,CAaO,SAASmB,EAAUnB,GACxB,OAXII,EAAqC,CAAA,EAEzCa,SAASb,OAAOgB,MAAM,KAAKC,SAAQ,SAACC,GAClC,IAA0BC,EAAAC,EAAbF,EAAGF,MAAM,KAAI,GAArBK,EAACF,EAAA,GAAEG,EAACH,EAAA,GACTnB,EAAOqB,EAAEE,QAAUD,CACrB,IAEOtB,GAIgBJ,GAZlB,IACDI,CAYN,CCjCA,IAAMwB,EAA2B,OAO1B,SAASC,EAAiBC,EAAgBC,GAC3CA,EACFhC,EACE+B,EAAME,SAASC,oBACfL,EACAE,EAAME,SAASE,yBDwBnBnC,ECrBe+B,EAAME,SAASC,oBDqBd,IAAK,ECnBvB,CCZA,IACQE,EADFC,GACED,EAEF,CAAA,EACG,SAAkBE,GAAuC,IAA/BvB,EAAGwB,UAAAC,OAAA,QAAAjC,IAAAgC,UAAA,GAAAA,UAAA,GAAG,gBAarC,OAXAH,EAAKrB,GAAOqB,EAAKrB,IAAQ,OAIRR,IAAb+B,EAAIvB,KAGNuB,EAAIvB,GAAOqB,EAAKrB,MAIXuB,EAAIvB,KC2DA0B,EAAA,IAxED,WAAA,SAAAC,IAAAC,OAAAD,GAAApC,oBAC0B,gBAAe,CAoEpD,OApEoDsC,EAAAF,EAAA,CAAA,CAAA3B,IAAA,MAAAb,MAKrD,SAAY2C,EAAcC,EAAoBC,GAC5C,OAAOF,EAAOR,EAAMS,IAAaC,EAAO9B,IAAAA,OAAOoB,EAAMU,IAAa,GACpE,GAEA,CAAAhC,IAAA,KAAAb,MAGA,SACEoC,EACAO,EACAC,EAIAC,EACAC,GAEA,IAAMC,EAAKC,KAAKC,IAAIN,EAAMC,EAAUC,GAC9BK,EAAU,SAACC,GAAQ,OAAKP,EAASC,GAAWT,EAAKe,GAAKC,OAAOC,MAAM,EAErE,qBAAsBjB,EACxBA,EAAIkB,iBAAiBX,EAAMO,EAASJ,GAC3B,gBAAiBV,GAE1BA,EAAImB,YAAWxC,KAAAA,OAAM4B,GAAQO,GAI/Bd,EAAIY,KAAKQ,YAAcpB,EAAIY,KAAKQ,aAAe,GAE/CpB,EAAIY,KAAKQ,YAAYT,GAAMG,CAC7B,GAEA,CAAArC,IAAA,MAAAb,MAGA,SACEoC,EACAO,EACAC,EAIAC,EACAC,GAEA,IAAMC,EAAKC,KAAKC,IAAIN,EAAMC,EAAUC,GAE9BK,EAAUd,EAAIY,KAAKQ,aAAepB,EAAIY,KAAKQ,YAAYT,GAExDG,IAID,wBAAyBd,EAC3BA,EAAIqB,oBAAoBd,EAAMO,EAASJ,GAC9B,gBAAiBV,GAE1BA,EAAIsB,YAAW3C,KAAAA,OAAM4B,GAAQO,GAI/Bd,EAAIY,KAAKQ,YAAYT,GAAM,KAC7B,KAACP,CAAA,CArEW,ICXdmB,EAAA,SAAgBC,GAAM,MAAiC,mBAANA,CAAgB,ECGlD,SAASC,EAASC,EAAsBC,GACrD,GAAID,aAAmBE,WAAY,CAEjC,IAAMC,EAAMH,EAAQI,aAAa,UAAY,GAExCD,EAAIE,MAAMJ,IAEbD,EAAQM,aAAa,QAAOrD,GAAAA,OAAKkD,EAAGlD,KAAAA,OAAIgD,GAE5C,MACE,QAA0B1D,IAAtByD,EAAQO,UAAyB,CAEnC,IACyBC,EADYC,EAAAC,EAArBT,EAAU5C,MAAM,MACP,IAAzB,IAAAoD,EAAAE,MAAAH,EAAAC,EAAAG,KAAAC,MAA2B,CAAA,IAAhBC,EAAGN,EAAAtE,MACZ8D,EAAQO,UAAUQ,IAAID,EACxB,CAAC,CAAA,MAAAE,GAAAP,EAAApB,EAAA2B,EAAA,CAAA,QAAAP,EAAAQ,GAAA,CACF,MAAWjB,EAAQC,UAAUI,MAAMJ,KAElCD,EAAQC,WAAS,IAAAhD,OAAQgD,GAG/B,CClBe,SAASiB,EACtBlB,EACAmB,GAEA,IAAIC,EAAY,GAahB,MAZI,iBAAkBpB,EAGpBoB,EAAYpB,EAAQqB,aAAaF,GACxBjE,SAASoE,aAAepE,SAASoE,YAAYC,mBAEtDH,EAAYlE,SAASoE,YAClBC,iBAAiBvB,EAAS,MAC1BwB,iBAAiBL,IAIlBC,GAAaA,EAAUK,YAClBL,EAAUK,cAEVL,CAEX,CCpBe,SAASM,EAAeC,GACrC5B,EAAS4B,EAAe,uBAExB,IAAMC,EAAyBV,EAAaS,EAAe,YAE9B,aAA3BC,GAC2B,aAA3BA,GAC2B,WAA3BA,GAC2B,UAA3BA,GAGA7B,EAAS4B,EAAe,2BAE5B,CCjBe,SAASE,EACtBC,EACAH,GAEA,GAAKG,EAAL,CAEA,IAAMC,ECPO,SAAyB/B,GACtC,IAAIgC,EAAQ1C,OAAOiC,iBAAiBvB,GAC9BiC,EAAyC,aAAnBD,EAAME,SAC5BC,EAAgB,gBAEtB,GAAuB,UAAnBH,EAAME,SAAsB,OAAOhF,SAASkF,KAEhD,IACE,IAAIL,EAA6B/B,EAChC+B,EAASA,EAAOM,eAIjB,GADAL,EAAQ1C,OAAOiC,iBAAiBQ,KAC5BE,GAA0C,WAAnBD,EAAME,WAG7BC,EAAcG,KAAKN,EAAMO,SAAWP,EAAMQ,UAAYR,EAAMS,WAC9D,OAAOV,EAGX,OAAO7E,SAASkF,IAClB,CDdiBM,CAAgBf,GAE3BI,IAAW7E,SAASkF,OAExBL,EAAOY,UAAYhB,EAAciB,UAAYb,EAAOa,UAN9B,CAOxB,CEVe,SAASC,IACtB,QAA0BtG,IAAtB+C,OAAOwD,WACT,MAAO,CAAEC,MAAOzD,OAAOwD,WAAYE,OAAQ1D,OAAO2D,aAElD,IAAMC,EAAIhG,SAASiG,gBACnB,MAAO,CAAEJ,MAAOG,EAAEE,YAAaJ,OAAQE,EAAEG,aAE7C,CCJe,SAASC,EACtBxB,EACAwB,EACAC,EACA5B,EACA6B,GAGA,IAAIC,EADJ,GAAiB,QAAbH,IAGCxB,IAGH2B,EADe,YAAbH,EACKE,EAAaE,wBAEb/B,EAAc+B,yBClBV,SAA2BnG,GACxC,IAAMkG,EAAOlG,EAAGmG,wBAEhB,OACED,EAAKE,KAAO,GACZF,EAAKG,MAAQ,GACbH,EAAKI,OAAS,IAAMvE,OAAO2D,aAC3BQ,EAAKK,OAASxE,OAAOwD,UAEzB,CDYOiB,CAAkBpC,KAAgB,CACrC,IAAMqC,EAAYC,IAAgBjB,OACtBS,EAAKI,QAAUJ,EAAKI,OAASJ,EAAKE,KAMpC,GAAKhC,EAAc0B,aAAeW,EAC1C1E,OAAO4E,SACL,EACAT,EAAKE,KAAOK,EAAY,EAAIP,EAAKT,OAAS,GAAKO,GAKjDjE,OAAO4E,SACL,EACAT,EAAKE,KAAOK,EAAY,EAAIP,EAAKT,OAAS,GAAKO,EAGrD,CACF,CE5Ce,SAASY,EAAkBC,GACxCA,EAAO9D,aAAa,OAAQ,UAC5B8D,EAAOC,SAAW,CACpB,CCDe,SAASC,EAAQtE,GAC9B,IAAM+B,EAAS/B,EAAQqC,cAEvB,SAAKN,GAA8B,SAApBA,EAAOwC,YAIoB,UAAtCrD,EAAalB,EAAS,aAInBsE,EAAQvC,GACjB,CCTe,SAASyC,EACtBxE,EACAyE,GAEA,IAAMrC,EAAOlF,SAASkF,KAChBsC,EAAQxH,SAASiG,gBACjBR,EAAYrD,OAAOqF,aAAeD,EAAM/B,WAAaP,EAAKO,UAC1DiC,EAAatF,OAAOuF,aAAeH,EAAME,YAAcxC,EAAKwC,WAElEH,EAAaA,GAAcrC,EAE3B,IAAMtC,EAAIE,EAAQ0D,wBACZoB,EAAKL,EAAWf,wBAChBqB,EAAqB7D,EAAauD,EAAY,YAEhDnG,EAAM,CACRyE,MAAOjD,EAAEiD,MACTC,OAAQlD,EAAEkD,QAGZ,MACwC,SAArCyB,EAAWO,QAAQvD,eACK,aAAvBsD,GACqB,WAAvBA,EAIOE,OAAOC,OAAO5G,EAAK,CACxBqF,IAAK7D,EAAE6D,IAAMmB,EAAGnB,IAChBC,KAAM9D,EAAE8D,KAAOkB,EAAGlB,OAGhBU,EAAQtE,GACHiF,OAAOC,OAAO5G,EAAK,CACxBqF,IAAK7D,EAAE6D,IACPC,KAAM9D,EAAE8D,OAGHqB,OAAOC,OAAO5G,EAAK,CACxBqF,IAAK7D,EAAE6D,IAAMhB,EACbiB,KAAM9D,EAAE8D,KAAOgB,GAIvB,CCjDe,SAASO,EACtBnF,EACAoF,GAEA,GAAIpF,aAAmBE,WAAY,CACjC,IAAMC,EAAMH,EAAQI,aAAa,UAAY,GAE7CJ,EAAQM,aACN,QACAH,EAAIkF,QAAQD,EAAgB,IAAIC,QAAQ,aAAc,IAE1D,MACErF,EAAQC,UAAYD,EAAQC,UACzBoF,QAAQD,EAAgB,IACxBC,QAAQ,aAAc,GAE7B,CClBe,SAASC,EACtBtF,EACAgC,GAEA,IAAIuD,EAAU,GAMd,GAJIvF,EAAQgC,MAAMuD,UAChBA,GAAWvF,EAAQgC,MAAMuD,SAGN,iBAAVvD,EACTuD,GAAWvD,OAEX,IAAK,IAAMwD,KAAQxD,EACjBuD,GAAO,GAAAtI,OAAOuI,EAAI,KAAAvI,OAAI+E,EAAMwD,GAAQ,KAIxCxF,EAAQgC,MAAMuD,QAAUA,CAC1B,CCTe,SAASE,EACtB1H,EACA2H,EACAC,GAEA,GAAKA,GAAgBD,EAArB,CAEA,IAAME,EAAkBpB,EACtBkB,EAAK1F,QACLjC,EAAM8H,gBAEJC,EAAqB/H,EAAME,SAAS8H,qBAKpCL,EAAK1F,mBAAmBgG,SAAW1B,EAAQoB,EAAK1F,SAClDD,EAAS4F,EAAa,wBAEtBR,EAAYQ,EAAa,wBAGL,aAAlBD,EAAKxD,WACP4D,EAAqB,GAIvBR,EAASK,EAAa,CACpB5C,MAAK,GAAA9F,OAAK2I,EAAgB7C,MAAQ+C,EAAsB,MACxD9C,OAAM,GAAA/F,OAAK2I,EAAgB5C,OAAS8C,EAAsB,MAC1DnC,IAAG,GAAA1G,OAAK2I,EAAgBjC,IAAMmC,EAAqB,EAAK,MACxDlC,KAAI,GAAA3G,OAAK2I,EAAgBhC,KAAOkC,EAAqB,EAAC,OA1B7B,CA4B7B,CCzCe,SAASG,EACtBC,EAMAC,EACAC,EAMAC,EAIA7C,GAEA,OACE0C,EAAatC,KAAOuC,EAAwBC,EAAcrD,MAC1DsD,EAAWtD,OAGXS,EAAaxB,MAAM4B,KAAI3G,GAAAA,OACrBoJ,EAAWtD,MAAQqD,EAAcrD,MAAQmD,EAAatC,KACpD,OAEG,IAGTJ,EAAaxB,MAAM4B,QAAI3G,OAAMkJ,EAAyB,OAC/C,EACT,CClCe,SAASG,EACtBJ,EAMAK,EACAH,EAMA5C,GAEA,OACE0C,EAAatC,KACXsC,EAAanD,MACbwD,EACAH,EAAcrD,MAChB,GAGAS,EAAaxB,MAAM4B,KAAI,GAAA3G,QAAOiJ,EAAatC,KAAQ,OAC5C,IAETJ,EAAaxB,MAAM8B,SAAK7G,OAAMsJ,EAA0B,OACjD,EACT,CC/Be,SAASC,EAAeC,EAAkBC,GACnDD,EAAYE,SAASD,IACvBD,EAAYG,OAAOH,EAAYI,QAAQH,GAAiB,EAE5D,CCiDA,SAASI,EACPC,EACApF,EACA6B,EACAwD,GAGA,IAAMC,EAAoBF,EAAmBG,QAEvCb,EAAapC,IACbkD,EAAgB3C,EAAUhB,GAAcR,OAAS,GACjDoE,EAAe5C,EAAUhB,GAAcT,MAAQ,GAC/CsE,EAAoB1F,EAAc+B,wBAIpC4D,EAAsC,WA8C1C,GAvCID,EAAkBxD,OAASsD,EAAgBd,EAAWrD,QACxDwD,EAA6BS,EAAmB,UAI9CI,EAAkB1D,IAAMwD,EAAgB,GAC1CX,EAA6BS,EAAmB,OAI9CI,EAAkBvD,MAAQsD,EAAef,EAAWtD,OACtDyD,EAA6BS,EAAmB,SAI9CI,EAAkBzD,KAAOwD,EAAe,GAC1CZ,EAA6BS,EAAmB,QAI9CD,IAGFA,EAAyBA,EAAuB3J,MAC9C,KACA,IAGA4J,EAAkBzI,SAEpB8I,EAAqBL,EAAkB,GAEnCA,EAAkBN,SAASK,KAE7BM,EAAqBN,IAKE,QAAvBM,GAAuD,WAAvBA,EAAiC,CACnE,IAAIC,EACAC,EAAsC,GAEf,QAAvBF,GAIFC,EAAmB,qBAEnBC,EAAmB,CACjB,mBACA,qBACA,uBAGFD,EAAmB,wBAEnBC,EAAmB,CACjB,sBACA,wBACA,yBAIJF,EAnIJ,SACEG,EACAL,EACAM,EACAF,GAEA,IAAMG,EAAmBP,EAAe,EAClCQ,EAAWC,KAAKC,IAAIJ,EAAapI,OAAOyI,OAAOhF,OA0BrD,OAtBI6E,EAAWH,EAAaL,IAC1BZ,EAA6BgB,EAAkB,oBAC/ChB,EAA6BgB,EAAkB,yBAM/CC,EAAaE,GACbC,EAAWH,EAAaE,KAExBnB,EAA6BgB,EAAkB,sBAC/ChB,EAA6BgB,EAAkB,0BAK7CC,EAAaL,IACfZ,EAA6BgB,EAAkB,qBAC/ChB,EAA6BgB,EAAkB,yBAG7CA,EAAiBhJ,OACZgJ,EAAiB,GAGnB,IACT,CA8FMQ,CACEX,EAAkBzD,KAClBwD,EACAf,EAAWtD,MACXyE,IACGD,CACT,CAEA,OAAOD,CACT,CAOe,SAASW,EACtBlK,EACAmK,EACA1E,EACA2E,GAEA,IADAC,EAAiB7J,UAAAC,OAAA,QAAAjC,IAAAgC,UAAA,IAAAA,UAAA,GAEjB,GAAK2J,EAAL,CAEA,IACI9B,EAMAF,EAMAG,EACAgC,EAdAC,EAAkB,GAiBtB9E,EAAaxB,MAAM2B,IAAM,GACzBH,EAAaxB,MAAM8B,MAAQ,GAC3BN,EAAaxB,MAAM6B,OAAS,GAC5BL,EAAaxB,MAAM4B,KAAO,GAC1BJ,EAAaxB,MAAMuG,WAAa,GAChC/E,EAAaxB,MAAMwG,UAAY,GAE/BL,EAAWnG,MAAMyG,QAAU,UAIzBH,EADsC,iBAA7BJ,EAAYQ,aACHR,EAAYQ,aAEZ3K,EAAME,SAASyK,aAGnClF,EAAavD,UAAY,CAAC,kBAAmBqI,GAC1CK,OAAOC,SACPzL,KAAK,KAERqG,EAAalD,aAAa,OAAQ,UAKH,cAH/B+H,EAAyBH,EAAYhG,WAGQnE,EAAME,SAAS4K,eAC1DR,EAAyBvB,EACvB/I,EAAME,SAAS8I,mBACfmB,EAAYlI,QACZwD,EACA6E,IAKJnC,EAAe1B,EAAU0D,EAAYlI,SACrCoG,EAAgB5B,EAAUhB,GAC1B6C,EAAapC,IAEblE,EAASyD,EAAY,WAAAvG,OAAaoL,IAElC,IAAIS,EACF5C,EAAanD,MAAQ,EAAIqD,EAAcrD,MAAQ,EAEjD,OAAQsF,GACN,IAAK,oBACHF,EAAWlI,UAAY,6BAEvB,IAAIsG,EAAyB,EAC7BD,EACEJ,EACAK,EACAH,EACA5C,GAEFA,EAAaxB,MAAM6B,OAAM5G,GAAAA,OAAMiJ,EAAalD,OAAS,GAAM,MAC3D,MAEF,IAAK,qBACHmF,EAAWlI,UAAY,8BAGnBmI,IACFU,GAA8B,GAI9BxC,EACEJ,EACA4C,EACA1C,EACA5C,KAGFA,EAAaxB,MAAM8B,MAAQ,GAC3BmC,EACEC,EACA4C,EACA1C,EACAC,EACA7C,IAGJA,EAAaxB,MAAM6B,OAAM5G,GAAAA,OAAMiJ,EAAalD,OAAS,GAAM,MAC3D,MAEF,IAAK,mBAEL,IAAK,MACHmF,EAAWlI,UAAY,uBAIvBgG,EACEC,EAHsBkC,EAAW,EAAI,GAKrChC,EACAC,EACA7C,GAEFA,EAAaxB,MAAM6B,OAAM5G,GAAAA,OAAMiJ,EAAalD,OAAS,GAAM,MAC3D,MACF,IAAK,QACHQ,EAAaxB,MAAM4B,KAAI3G,GAAAA,OAAMiJ,EAAanD,MAAQ,GAAM,MACpDmD,EAAavC,IAAMyC,EAAcpD,OAASqD,EAAWrD,QAGvDmF,EAAWlI,UAAY,4BACvBuD,EAAaxB,MAAM2B,IAAG,IAAA1G,OACpBmJ,EAAcpD,OAASkD,EAAalD,OAAS,GAC3C,OAEJmF,EAAWlI,UAAY,qBAEzB,MACF,IAAK,OACEmI,IAA+C,IAAnCrK,EAAME,SAAS8K,kBAC9BvF,EAAaxB,MAAM2B,IAAM,QAGvBuC,EAAavC,IAAMyC,EAAcpD,OAASqD,EAAWrD,QAGvDQ,EAAaxB,MAAM2B,IAAG,IAAA1G,OACpBmJ,EAAcpD,OAASkD,EAAalD,OAAS,GAC3C,MACJmF,EAAWlI,UAAY,8BAEvBkI,EAAWlI,UAAY,sBAEzBuD,EAAaxB,MAAM8B,MAAK7G,GAAAA,OAAMiJ,EAAanD,MAAQ,GAAM,MAEzD,MACF,IAAK,WACHoF,EAAWnG,MAAMyG,QAAU,OAG3BjF,EAAaxB,MAAM4B,KAAO,MAC1BJ,EAAaxB,MAAM2B,IAAM,MACzBH,EAAaxB,MAAMuG,WAAUtL,IAAAA,OAAOmJ,EAAcrD,MAAQ,EAAK,MAC/DS,EAAaxB,MAAMwG,UAASvL,IAAAA,OAAOmJ,EAAcpD,OAAS,EAAK,MAE/D,MACF,IAAK,uBACHmF,EAAWlI,UAAY,0BAGvBqG,EACEJ,EAFFK,EAAyB,EAIvBH,EACA5C,GAEFA,EAAaxB,MAAM2B,IAAG1G,GAAAA,OAAMiJ,EAAalD,OAAS,GAAM,MACxD,MAEF,IAAK,wBACHmF,EAAWlI,UAAY,2BAGnBmI,IACFU,GAA8B,GAI9BxC,EACEJ,EACA4C,EACA1C,EACA5C,KAGFA,EAAaxB,MAAM8B,MAAQ,GAC3BmC,EACEC,EACA4C,EACA1C,EACAC,EACA7C,IAGJA,EAAaxB,MAAM2B,IAAG1G,GAAAA,OAAMiJ,EAAalD,OAAS,GAAM,MACxD,MAMF,QACEmF,EAAWlI,UAAY,oBAGvBgG,EACEC,EAFsB,EAItBE,EACAC,EACA7C,GAEFA,EAAaxB,MAAM2B,IAAG1G,GAAAA,OAAMiJ,EAAalD,OAAS,GAAM,MA1N1C,CA4NpB,CC5Xe,SAASgG,IAKtB,IAJA,IAIAC,EAAA,EAAAC,EAJaC,MAAMC,KACjBlM,SAASmM,iBAA8B,yBAGnBJ,EAAAC,EAAA1K,OAAAyK,IAAE,CACtB9D,EADY+D,EAAAD,GACK,qBACnB,CACF,CCVe,SAASK,EACtBtE,EACAuE,GAEA,IAAIvJ,EAAU9C,SAASsM,cAAiBxE,GAExCuE,EAAQA,GAAS,GAGjB,IAAME,EAAc,wBAEpB,IAAK,IAAM/L,KAAK6L,EAAO,CACrB,IAAI5L,EAAI4L,EAAM7L,GAEJ,UAANA,GAA8B,mBAANC,EAC1B2H,EAAStF,EAASrC,GACI,iBAANA,GAAkBD,EAAE2C,MAAMoJ,GAC1CzJ,EAAQM,aAAa5C,EAAGC,GAGxBqC,EAAQtC,GAAKC,CAEjB,CAEA,OAAOqC,CACT,CCzBe,SAAS0J,EACtBrH,EACArC,GAEA,IADA2J,EAAgBpL,UAAAC,OAAA,QAAAjC,IAAAgC,UAAA,IAAAA,UAAA,GAEhB,GAAIoL,EAAS,CACX,IAAMC,EAAkB5J,EAAQgC,MAAM6H,SAAW,IAEjDvE,EAAStF,EAAS,CAChB6J,QAAS,MAGXvK,OAAOwK,YAAW,WAChBxE,EAAStF,EAAS,CAChB6J,QAASD,GAEZ,GAAE,GACL,CAEAvH,EAAcqH,YAAY1J,EAC5B,CCHA,SAAS+J,EAAa7B,EAAqB8B,GAEzC,OAAS9B,EAAc,GAAK8B,EAAoB,GAClD,CAOA,SAASC,EAAoBlM,EAAgB2H,GAC3C,IAAIwE,EAA0BhN,SAASiN,cACrC,+BAG8B,OAA5BD,IACFA,EAA0BV,EAAc,MAAO,CAC7CvJ,UAAW,+BAGblC,EAAM8H,eAAe6D,YAAYQ,IAGnCzE,EAAuB1H,EAAO2H,EAAMwE,EACtC,CAMA,SAASE,EAAerM,EAAgB4D,GACtC,IAAM0I,EAAeb,EAAc,MAAO,CACxCvJ,UAAW,qBAGsB,IAA/BlC,EAAME,SAASqM,cACjBD,EAAarI,MAAMyG,QAAU,QAG/B,IAAM8B,EAAcf,EAAc,MAClCe,EAAYjK,aAAa,OAAQ,WASjC,IAPA,IAAMkK,EAAc,WAClB,IAAMC,EAAavL,KAAKkB,aAAa,oBACnB,MAAdqK,GAEJ1M,EAAM2M,SAASC,SAASF,EAAY,MAG7BG,EAAI,EAAGA,EAAI7M,EAAM8M,YAAYrM,OAAQoM,IAAK,CACjD,IAAQlF,EAAS3H,EAAM8M,YAAYD,GAA3BlF,KAEFoF,EAAUtB,EAAc,MACxBuB,EAAavB,EAAc,KAEjCsB,EAAQxK,aAAa,OAAQ,gBAC7ByK,EAAWzK,aAAa,OAAQ,OAEhCyK,EAAWC,QAAUR,EAEjBI,IAAMjJ,EAAc+D,KAAO,IAC7BqF,EAAW9K,UAAY,UAGzBkE,EAAkB4G,GAClBA,EAAWE,UAAY,SACvBF,EAAWzK,aAAa,mBAAoBoF,EAAKwF,YAEjDJ,EAAQpB,YAAYqB,GACpBR,EAAYb,YAAYoB,EAC1B,CAIA,OAFAT,EAAaX,YAAYa,GAElBF,CACT,CAsBA,SAASc,EACPb,EACAc,EACAzJ,GAEA,GAAI2I,EAAa,CACf,IAAMe,EAAqBD,EAAkBjB,cAC3C,kCAGImB,EAAyBF,EAAkBjB,cAAa,6CAAAlN,OACf0E,EAAc+D,KAAI,OAG7D2F,GAAsBC,IACxBD,EAAmBpL,UAAY,GAC/BqL,EAAuBrL,UAAY,SAEvC,CACF,CAMA,SAASsL,EAAmBxN,GAC1B,IAAMyN,EAAgBhC,EAAc,OAEpCgC,EAAcvL,UAAY,oBAEU,IAAhClC,EAAME,SAASwN,eACjBD,EAAcxJ,MAAMyG,QAAU,QAGhC,IAAMiD,EAAclC,EAAc,MAAO,CACvCvJ,UAAW,wBAGTlC,EAAME,SAAS0N,6BACjBD,EAAYzL,WAAa,IAAMlC,EAAME,SAAS0N,4BAGhD,IAAMC,EAAW7B,EAAahM,EAAM8N,aAAc9N,EAAM8M,YAAYrM,QASpE,OARAkN,EAAYpL,aAAa,OAAQ,YACjCoL,EAAYpL,aAAa,gBAAiB,KAC1CoL,EAAYpL,aAAa,gBAAiB,OAC1CoL,EAAYpL,aAAa,gBAAiBsL,EAASV,YACnDQ,EAAY1J,MAAMuD,iBAAOtI,OAAY2O,EAAY,MAEjDJ,EAAc9B,YAAYgC,GAEnBF,CACT,CAMO,SAASM,EACdV,EACAlD,EACA8B,GAEA,IAAM0B,EAAcN,EAAkBjB,cACpC,0CAGF,GAAKuB,EAAL,CAEA,IAAME,EAAW7B,EAAa7B,EAAa8B,GAE3C0B,EAAY1J,MAAMuD,iBAAOtI,OAAY2O,EAAY,MACjDF,EAAYpL,aAAa,gBAAiBsL,EAASV,WALjC,CAMpB,CAOA,SAA8Ba,EAAYC,EAAAC,GAAA,OAAAC,EAAAC,MAAAjN,KAAAX,UAAA,CAybzC,SAAA2N,IAAA,OAAAA,EAAAE,EAAAC,IAAAC,MAzbc,SAAAC,EACbxO,EACA4D,GAAwB,IAAA6K,EAAApB,EAAAqB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAtH,EAAAuH,EAAA/E,EAAA3E,EAAA2J,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAxD,EAAA,OAAAmC,IAAAsB,MAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,KAAA,EAAA,IAEpBjO,EAAW9B,EAAMgQ,sBAAqB,CAAAH,EAAAE,KAAA,EAAA,KAAA,CAAA,OAAAF,EAAAE,KAAA,EAClC/P,EAAMgQ,qBAAqBC,KAAKjQ,EAAO4D,EAAc3B,SAAQ,KAAA,EA+ahB,GA5a/CwM,EAAiBtP,SAASiN,cAC9B,wBAEIiB,EAAoBlO,SAASiN,cACjC,kCAEEsC,EAAiB,sBAMuB,iBAAjC9K,EAAc8K,iBACvBA,OAAcxP,OAAQ0E,EAAc8K,iBAGO,iBAAlC1O,EAAME,SAASwO,iBACxBA,GAAc,IAAAxP,OAAQc,EAAME,SAASwO,iBAGhB,OAAnBD,GAAiD,OAAtBpB,GACvByB,EAAuBzB,EAAkBjB,cAC7C,8BAEI2C,EAAkB1B,EAAkBjB,cACxC,wBAEI4C,EAAuB3B,EAAkBjB,cAC7C,0BAEI6C,EAAgB5B,EAAkBjB,cACtC,kBAEI8C,EAAsB7B,EAAkBjB,cAC5C,oBAGFyC,EAAoBxB,EAAkBjB,cACpC,uBAEFwC,EAAoBvB,EAAkBjB,cACpC,uBAEFuC,EAAoBtB,EAAkBjB,cACpC,uBAIFqC,EAAevM,UAAYwM,EAE3BQ,EAAoBjL,MAAM6H,QAAU,IACpCoD,EAAoBjL,MAAMyG,QAAU,OAGpC5G,EACE9D,EAAME,SAAS6D,gBACfH,EAAc3B,SAIhByF,EAAuB1H,EAAO4D,EAAe6K,GAC7C/G,EAAuB1H,EAAO4D,EAAeyJ,GAG7CpC,IAGIjL,EAAMkQ,uBACR3O,OAAO4O,aAAanQ,EAAMkQ,uBAG5BlQ,EAAMkQ,sBAAwB3O,OAAOwK,YAAW,WAEjB,OAAzB+C,IACFA,EAAqB5B,UAAS,GAAAhO,OAAM0E,EAAc+D,KAAIzI,KAAAA,OAAIc,EAAME,SAASkQ,mBAAkB,KAAAlR,OAAIc,EAAM8M,YAAYrM,SAInHsO,EAAgB7B,UAAYtJ,EAAc5D,OAAS,GAGnDgP,EAAqB9B,UAAYtJ,EAAcyM,OAAS,GAGxDnB,EAAoBjL,MAAMyG,QAAU,QACpCR,EAAalK,EAAO4D,EAAesL,EAAqBD,GAGxD7B,EACEpN,EAAME,SAASqM,YACfc,EACAzJ,GAGFmK,EACEV,EACArN,EAAM8N,aACN9N,EAAM8M,YAAYrM,QAIpByO,EAAoBjL,MAAM6H,QAAU,KAIlC,MAAO6C,GAEP,uBAAuBpK,KAAKoK,EAAkBzM,YAK9C,MAAOyM,IAFPA,EAAkB2B,QAUpB/K,EACEvF,EAAME,SAAS6D,gBACfH,EAAc2B,SACdvF,EAAME,SAASsF,cACf5B,EAAc3B,QACd8M,EAEH,GAAE,OAIGnH,EAAc6D,EAAc,MAAO,CACvCvJ,UAAWwM,IAEPS,EAAiB1D,EAAc,MAAO,CAC1CvJ,UAAW,kCAEPkI,EAAaqB,EAAc,MAAO,CACtCvJ,UAAW,kBAEPuD,EAAegG,EAAc,MAAO,CACxCvJ,UAAW,oBAEPkN,EAAmB3D,EAAc,MAAO,CAC5CvJ,UAAW,wBAEPmN,EAAqB5D,EAAc,MAAO,CAC9CvJ,UAAW,2BAEPoN,EAAoB7D,EAAc,KAAM,CAC5CvJ,UAAW,0BAGPqN,EAAe9D,EAAc,OAEnClE,EAASK,EAAa,CACpB,aAAY,uDAAA1I,OAAyDc,EAAME,SAASqQ,eAAepD,WAAU,oBAI/GrJ,EACE9D,EAAME,SAAS6D,gBACfH,EAAc3B,SAIhByF,EAAuB1H,EAAO4D,EAAegE,GAC7CF,EAAuB1H,EAAO4D,EAAeuL,GAG7CxD,EAAY3L,EAAM8H,eAAgBF,GAAa,GAC/C+D,EAAY3L,EAAM8H,eAAgBqH,GAElCC,EAAiBlC,UAAYtJ,EAAc5D,MAC3CsP,EAAkBpC,UAAYtJ,EAAcyM,MAE5Cd,EAAarN,UAAY,0BACU,IAA/BlC,EAAME,SAASsQ,cACjBjB,EAAatL,MAAMyG,QAAU,QAG/B2E,EAAmB1D,YAAY2D,GAC/B7J,EAAakG,YAAY0D,GACzB5J,EAAakG,YAAYyD,GAGrBpP,EAAME,SAASD,gBACXuP,EAAuB/D,EAAc,MAAO,CAChDvJ,UAAW,2BAEPuN,EAAwBhE,EAAc,QAAS,CACnD3K,KAAM,WACNI,GAAI,wBACJhD,KAAM,2BAEcuS,SAAW,SAACnP,GAChCtB,EAAMD,iBAAoCuB,EAAEoP,OAAQC,WAEhDjB,EAA6BjE,EAAc,QAAS,CACxDmF,QAAS,2BAEgBC,UAAY7Q,EAAME,SAAS4Q,mBACtDtB,EAAqB7D,YAAY8D,GACjCD,EAAqB7D,YAAY+D,GAEjCjK,EAAakG,YAAY6D,IAG3B/J,EAAakG,YAAYU,EAAerM,EAAO4D,IAC/C6B,EAAakG,YAAY6B,EAAmBxN,IAGtC2P,EAAoBlE,EAAc,QAED,IAAnCzL,EAAME,SAAS8K,kBACjB2E,EAAkBzN,UAAY,4BAC9ByN,EAAkBzC,UAAS,GAAAhO,OAAM0E,EAAc+D,KAAIzI,KAAAA,OAAIc,EAAME,SAASkQ,mBAAkB,KAAAlR,OAAIc,EAAM8M,YAAYrM,QAC9GgF,EAAakG,YAAYgE,IAG3BlK,EAAakG,YAAYvB,GACzB+E,EAAexD,YAAYlG,IAG3BkJ,EAAoBlD,EAAc,MAEhBwB,QAAOoB,EAAAC,IAAAC,MAAG,SAAAwC,IAAA,OAAAzC,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAAA,GACtB/P,EAAM8M,YAAYrM,OAAS,IAAMT,EAAM8N,aAAY,CAAAkD,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EAC/CkB,EAASjR,GAAM,KAAA,EAAAgR,EAAAjB,KAAA,GAAA,MAAA,KAAA,EAAA,IACZ,uBAAuBxL,KAAKoK,EAAkBzM,WAAU,CAAA8O,EAAAjB,KAAA,GAAA,KAAA,CAAA,IAC7DjO,EAAW9B,EAAMkR,wBAAuB,CAAAF,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EACpC/P,EAAMkR,uBAAuBjB,KACjCjQ,EACAA,EAAM8N,aACN,QACD,KAAA,EAAA,OAAAkD,EAAAjB,KAAA,GAGGoB,GAAUnR,EAAOA,EAAM8H,gBAAe,KAAA,GAAA,IAAA,MAAA,OAAAkJ,EAAAI,OAAA,GAAAL,EAE/C,KAED3K,EAAkBuI,GAClBA,EAAkBzB,UAAYlN,EAAME,SAASmR,WAG7CzC,EAAoBnD,EAAc,MAEhBwB,QAAOoB,EAAAC,IAAAC,MAAG,SAAA+C,IAAA,OAAAhD,IAAAsB,MAAA,SAAA2B,GAAA,cAAAA,EAAAzB,KAAAyB,EAAAxB,MAAA,KAAA,EAAA,KACtB/P,EAAM8N,aAAe,GAAC,CAAAyD,EAAAxB,KAAA,EAAA,KAAA,CAAA,OAAAwB,EAAAxB,KAAA,EAClByB,GAAaxR,GAAM,KAAA,EAAA,IAAA,MAAA,OAAAuR,EAAAH,OAAA,GAAAE,EAE5B,KAEDlL,EAAkBwI,GAClBA,EAAkB1B,UAAYlN,EAAME,SAASuR,UAO7CrL,EAJAyI,EAAoBpD,EAAc,IAAK,CACrCvJ,UAAW,wBAIb2M,EAAkB3B,UAAYlN,EAAME,SAASwR,UAE7C7C,EAAkB5B,QAAOoB,EAAAC,IAAAC,MAAG,SAAAoD,IAAA,OAAArD,IAAAsB,MAAA,SAAAgC,GAAA,cAAAA,EAAA9B,KAAA8B,EAAA7B,MAAA,KAAA,EAAA,GAExB/P,EAAM8M,YAAYrM,OAAS,IAAMT,EAAM8N,eACvChM,EAAW9B,EAAMkR,wBAAuB,CAAAU,EAAA7B,KAAA,EAAA,KAAA,CAAA,OAAA6B,EAAA7B,KAAA,EAElC/P,EAAMkR,uBAAuBjB,KACjCjQ,EACAA,EAAM8N,aACN,QACD,KAAA,EAAA,IAGChM,EAAW9B,EAAM6R,oBAAmB,CAAAD,EAAA7B,KAAA,EAAA,KAAA,CAAA,OAAA6B,EAAA7B,KAAA,EAChC/P,EAAM6R,mBAAmB5B,KAAKjQ,EAAOA,EAAM8N,cAAa,KAAA,EAAA,OAAA8D,EAAA7B,KAAA,EAG1DoB,GAAUnR,EAAOA,EAAM8H,gBAAe,KAAA,EAAA,IAAA,MAAA,OAAA8J,EAAAR,OAAA,GAAAO,EAC7C,KAEDtC,EAAmB1D,YAAYkD,GAG3B7O,EAAM8M,YAAYrM,OAAS,GAC7B8O,EAAa5D,YAAYiD,GAK3BW,EAAa5D,YAAYgD,GACzBlJ,EAAakG,YAAY4D,GAGzBrF,EAAalK,EAAO4D,EAAe6B,EAAc2E,GAGjD7E,EACEvF,EAAME,SAAS6D,gBACfH,EAAc2B,SACdvF,EAAME,SAASsF,cACf5B,EAAc3B,QACdwD,KAOE0G,EAA0BnM,EAAM8H,eAAesE,cACnD,iCAE6BD,EAAwB2F,YACrD3F,EAAwB2F,WAAWC,YAAY5F,GAI7CvI,EAAcoO,oBAChB9F,EAAoBlM,EAAO4D,GAIF,IAAvB5D,EAAM8N,cAAsB9N,EAAM8M,YAAYrM,OAAS,GAEvD,MAAOkO,IAGPA,EAAkBzM,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAAgC,uBAChFtD,EAAkBzB,UAAYlN,EAAME,SAASmR,YAGf,IAA5BrR,EAAME,SAASgS,UAEf,MAAOtD,IAGPA,EAAkB1M,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAA+C,uCAG/F,MAAOtD,GAGP3M,EAAS2M,EAAmB,uBAI5B,MAAOC,IAGPA,EAAkB1M,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAAiD,0CAIrGjS,EAAM8M,YAAYrM,OAAS,IAAMT,EAAM8N,cACV,IAA7B9N,EAAM8M,YAAYrM,QAIhB,MAAOmO,IAGPA,EAAkB1M,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAAgC,yBAGlD,IAA5BjS,EAAME,SAASiS,UAEf,MAAOxD,IAGPA,EAAkBzM,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAA+C,uCAG/F,MAAOrD,GAGP5M,EAAS4M,EAAmB,uBAI5B,MAAOD,KAG2B,IAA9B3O,EAAME,SAASkS,YACjBzD,EAAkBzB,UAAYlN,EAAME,SAASmS,UAC7CrQ,EACE2M,EAAiB,GAAAzP,OACdc,EAAME,SAAS+R,YAAW,4CAG/BtD,EAAkBzM,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAAiD,2CAOrG,MAAOrD,IAGPA,EAAkB1M,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAAgC,wBAGhF,MAAOtD,IAGPA,EAAkBzM,UAAS,GAAAhD,OAAMc,EAAME,SAAS+R,YAAgC,uBAChFtD,EAAkBzB,UAAYlN,EAAME,SAASmR,YAI7C,MAAOzC,GACTA,EAAkBrM,aAAa,OAAQ,UAErC,MAAOoM,GACTA,EAAkBpM,aAAa,OAAQ,UAErC,MAAOsM,GACTA,EAAkBtM,aAAa,OAAQ,UAIrC,MAAOoM,GACTA,EAAkB2B,QAGpB3M,EAAeC,EAAc3B,UAEzBH,EAAW9B,EAAMsS,2BAA0B,CAAAzC,EAAAE,KAAA,GAAA,KAAA,CAAA,OAAAF,EAAAE,KAAA,GACvC/P,EAAMsS,0BAA0BrC,KAAKjQ,EAAO4D,EAAc3B,SAAQ,KAAA,GAAA,IAAA,MAAA,OAAA4N,EAAAuB,OAAA,GAAA5C,EAE3E,MAAAJ,MAAAjN,KAAAX,UAAA,CCtkBD,SAAsBmM,EAAQsB,EAAAC,GAAA,OAAAqE,EAAAnE,MAAAjN,KAAAX,UAAA,CAQ9B,SAAA+R,IAFC,OAEDA,EAAAlE,EAAAC,IAAAC,MARO,SAAAwC,EAAwB/Q,EAAgB2H,GAAY,OAAA2G,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAE3B,GAA9B/P,EAAM8N,aAAenG,EAAO,OACK,IAAtB3H,EAAM8M,YAA2B,CAAAkE,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EACpCkB,EAASjR,GAAM,KAAA,EAAA,IAAA,MAAA,OAAAgR,EAAAI,OAAA,GAAAL,EAExB,MAAA3C,MAAAjN,KAAAX,UAAA,CAOD,SAAsBgS,EAAcC,EAAAC,GAAA,OAAAC,EAAAvE,MAAAjN,KAAAX,UAAA,CAOpC,SAAAmS,IAFC,OAEDA,EAAAtE,EAAAC,IAAAC,MAPO,SAAA+C,EAA8BtR,EAAgB2H,GAAY,OAAA2G,IAAAsB,MAAA,SAAA2B,GAAA,cAAAA,EAAAzB,KAAAyB,EAAAxB,MAAA,KAAA,EAC/B,GAAhC/P,EAAM4S,mBAAqBjL,OACM,IAAtB3H,EAAM8M,YAA2B,CAAAyE,EAAAxB,KAAA,EAAA,KAAA,CAAA,OAAAwB,EAAAxB,KAAA,EACpCkB,EAASjR,GAAM,KAAA,EAAA,IAAA,MAAA,OAAAuR,EAAAH,OAAA,GAAAE,EAExB,MAAAlD,MAAAjN,KAAAX,UAAA,CAOqByQ,SAAAA,EAAQ4B,GAAA,OAAAC,GAAA1E,MAAAjN,KAAAX,UAAA,CAsD9B,SAAAsS,KAFC,OAEDA,GAAAzE,EAAAC,IAAAC,MAtDO,SAAAoD,EAAwB3R,GAAc,IAAA6M,EAAAoE,EAAA8B,EAAA,OAAAzE,IAAAsB,MAAA,SAAAgC,GAAA,cAAAA,EAAA9B,KAAA8B,EAAA7B,MAAA,KAAA,EAG3C,GAFA/P,EAAMgT,WAAa,eAEqB,IAA7BhT,EAAM4S,mBACf,IAAS/F,EAAI,EAAGA,EAAI7M,EAAM8M,YAAYrM,OAAQoM,IAC/B7M,EAAM8M,YAAYD,GACtBlF,OAAS3H,EAAM4S,qBACtB5S,EAAM8N,aAAejB,EAAI,EACzB7M,EAAM4S,wBAAqBpU,GAYV,IAPK,IAAxBwB,EAAM8N,aACR9N,EAAM8N,aAAe,IAEnB9N,EAAM8N,aAGJmD,EAAWjR,EAAM8M,YAAY9M,EAAM8N,cACrCiF,GAAe,GAEfjR,EAAW9B,EAAMiT,4BAA2B,CAAArB,EAAA7B,KAAA,EAAA,KAAA,CAAA,OAAA6B,EAAA7B,KAAA,EACzB/P,EAAMiT,2BAA2BhD,KACpDjQ,EACAiR,GAAaA,EAAShP,QACtBjC,EAAM8N,aACN9N,EAAMgT,YACP,KAAA,EALDD,EAAYnB,EAAAsB,KAAA,KAAA,EAAA,IASO,IAAjBH,EAAsB,CAAAnB,EAAA7B,KAAA,GAAA,KAAA,CACH,QAAnB/P,EAAM8N,aAAa8D,EAAAuB,OAAA,UACd,GAAK,KAAA,GAAA,KAGVnT,EAAM8M,YAAYrM,QAAUT,EAAM8N,cAAY,CAAA8D,EAAA7B,KAAA,GAAA,KAAA,CAAA,IAG5CjO,EAAW9B,EAAMkR,wBAAuB,CAAAU,EAAA7B,KAAA,GAAA,KAAA,CAAA,OAAA6B,EAAA7B,KAAA,GACpC/P,EAAMkR,uBAAuBjB,KAAKjQ,EAAOA,EAAM8N,aAAc,OAAM,KAAA,GAAA,OAAA8D,EAAA7B,KAAA,GAGrEoB,GAAUnR,EAAOA,EAAM8H,gBAAe,KAAA,GAAA,OAAA8J,EAAAuB,OAAA,UAErC,GAAK,KAAA,GAAA,OAAAvB,EAAA7B,KAAA,GAGRqD,EAAYpT,EAAOiR,GAAS,KAAA,GAAA,OAAAW,EAAAuB,OAAA,UAE3B,GAAI,KAAA,GAAA,IAAA,MAAA,OAAAvB,EAAAR,OAAA,GAAAO,EACZ,KAAAmB,GAAA1E,MAAAjN,KAAAX,UAAA,CAOqBgR,SAAAA,GAAY6B,GAAA,OAAAC,GAAAlF,MAAAjN,KAAAX,UAAA,CA8BjC,SAAA8S,KAAA,OAAAA,GAAAjF,EAAAC,IAAAC,MA9BM,SAAAC,EAA4BxO,GAAc,IAAAiR,EAAA8B,EAAA,OAAAzE,IAAAsB,MAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,KAAA,EACjB,GAA9B/P,EAAMgT,WAAa,aAEfhT,EAAM8N,cAAgB,GAAC,CAAA+B,EAAAE,KAAA,EAAA,KAAA,CAAA,OAAAF,EAAAsD,OAAA,UAClB,GAAK,KAAA,EAMS,KAHrBnT,EAAM8N,aAEFmD,EAAWjR,EAAM8M,YAAY9M,EAAM8N,cACrCiF,GAAe,GAEfjR,EAAW9B,EAAMiT,4BAA2B,CAAApD,EAAAE,KAAA,GAAA,KAAA,CAAA,OAAAF,EAAAE,KAAA,EACzB/P,EAAMiT,2BAA2BhD,KACpDjQ,EACAiR,GAAaA,EAAShP,QACtBjC,EAAM8N,aACN9N,EAAMgT,YACP,KAAA,EALDD,EAAYlD,EAAAqD,KAAA,KAAA,GAAA,IASO,IAAjBH,EAAsB,CAAAlD,EAAAE,KAAA,GAAA,KAAA,CACH,QAAnB/P,EAAM8N,aAAa+B,EAAAsD,OAAA,UACd,GAAK,KAAA,GAAA,OAAAtD,EAAAE,KAAA,GAGRqD,EAAYpT,EAAOiR,GAAS,KAAA,GAAA,OAAApB,EAAAsD,OAAA,UAE3B,GAAI,KAAA,GAAA,IAAA,MAAA,OAAAtD,EAAAuB,OAAA,GAAA5C,EACZ,KAAA8E,GAAAlF,MAAAjN,KAAAX,UAAA,CCxJD,SAA8B+S,GAAStF,EAAAC,GAAA,OAAAsF,GAAApF,MAAAjN,KAAAX,UAAA,CAqDtC,SAAAgT,KAAA,OAAAA,GAAAnF,EAAAC,IAAAC,MArDc,SAAAwC,EAAyB/Q,EAAgBsB,GAAgB,IAAAmS,EAAA/C,EAAA,OAAApC,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAMrE,GAFY,QAHT0D,OAAkBjV,IAAX8C,EAAEmS,KAAqBnS,EAAEoS,MAAQpS,EAAEmS,QAI5CA,EAAsB,OAAfnS,EAAEqS,SAAoBrS,EAAEsS,QAAUtS,EAAEqS,UAG/B,WAATF,GAA8B,KAATA,IAA6C,IAA7BzT,EAAME,SAAS2T,UAAkB,CAAA7C,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EAGnEoB,GAAUnR,EAAOA,EAAM8H,gBAAe,KAAA,EAAAkJ,EAAAjB,KAAA,GAAA,MAAA,KAAA,EAAA,GAC1B,cAAT0D,GAAiC,KAATA,EAAW,CAAAzC,EAAAjB,KAAA,GAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,GAEtCyB,GAAaxR,GAAM,KAAA,GAAAgR,EAAAjB,KAAA,GAAA,MAAA,KAAA,GAAA,GACP,eAAT0D,GAAkC,KAATA,EAAW,CAAAzC,EAAAjB,KAAA,GAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,GAEvCkB,EAASjR,GAAM,KAAA,GAAAgR,EAAAjB,KAAA,GAAA,MAAA,KAAA,GAAA,GACH,UAAT0D,GAA6B,gBAATA,GAAmC,KAATA,EAAW,CAAAzC,EAAAjB,KAAA,GAAA,KAAA,CAE1B,KAAlCW,EAAUpP,EAAEoP,QAAUpP,EAAEwS,cAChBpD,EAAOxO,UAAUI,MAAM,sBAAqB,CAAA0O,EAAAjB,KAAA,GAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,GAElDyB,GAAaxR,GAAM,KAAA,GAAAgR,EAAAjB,KAAA,GAAA,MAAA,KAAA,GAAA,IAChBW,IAAUA,EAAOxO,UAAUI,MAAM,sBAAqB,CAAA0O,EAAAjB,KAAA,GAAA,KAAA,CAAA,GAG7D/P,EAAM8M,YAAYrM,OAAS,IAAMT,EAAM8N,eACvChM,EAAW9B,EAAMkR,wBAAuB,CAAAF,EAAAjB,KAAA,GAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,GAElC/P,EAAMkR,uBAAuBjB,KACjCjQ,EACAA,EAAM8N,aACN,QACD,KAAA,GAAA,OAAAkD,EAAAjB,KAAA,GAGGoB,GAAUnR,EAAOA,EAAM8H,gBAAe,KAAA,GAAAkJ,EAAAjB,KAAA,GAAA,MAAA,KAAA,GAAA,IACnCW,IAAUA,EAAOrO,aAAa,oBAAmB,CAAA2O,EAAAjB,KAAA,GAAA,KAAA,CAE1DW,EAAOqD,QAAQ/C,EAAAjB,KAAA,GAAA,MAAA,KAAA,GAAA,OAAAiB,EAAAjB,KAAA,GAGTkB,EAASjR,GAAM,KAAA,GAInBsB,EAAE0S,eACJ1S,EAAE0S,iBAEF1S,EAAE2S,aAAc,EACjB,KAAA,GAAA,IAAA,MAAA,OAAAjD,EAAAI,OAAA,GAAAL,EAEJ,MAAA3C,MAAAjN,KAAAX,UAAA,CCrEc,SAAS0T,GAAeC,GACrC,GAAe,OAAXA,GAAqC,WAAlBC,EAAOD,IAAuB,aAAcA,EACjE,OAAOA,EAGT,IAAME,EAAO,CAAA,EAEb,IAAK,IAAMrV,KAAOmV,EAEZ,WAAY5S,QAAU4S,EAAOnV,aAAgBuC,OAAO+S,OACtDD,EAAKrV,GAAOmV,EAAOnV,GAEnBqV,EAAKrV,GAAOkV,GAAYC,EAAOnV,IAGnC,OAAOqV,CACT,CCpBe,SAASE,GACtBC,EACAC,GAEA,IAAIC,EAEJ,OAAO,WAAa,IAAA,IAAAC,EAAAnU,UAAAC,OAATmU,EAAIxJ,IAAAA,MAAAuJ,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,GAAArU,UAAAqU,GACbtT,OAAO4O,aAAauE,GAEpBA,EAAQnT,OAAOwK,YAAW,WACxByI,EAAKI,EACN,GAAEH,GAEP,CCKO,SAASK,GAAqBC,GACnC,IAAMC,EAAe7V,SAASiN,cAAc,kBAC5C,OAAO4I,EACH5J,MAAMC,KAAK2J,EAAa1J,iBAAiByJ,IACzC,EACN,CAOA,SAAsBE,GAAQhH,EAAAC,GAAA,OAAAgH,GAAA9G,MAAAjN,KAAAX,UAAA,CAe9B,SAAA0U,KAFC,OAEDA,GAAA7G,EAAAC,IAAAC,MAfO,SAAAwC,EAAwB/Q,EAAgBmV,GAAc,IAAAC,EAAA,OAAA9G,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAS3D,GARMqF,EAAON,GAAoB,4BAAA5V,OAA6BiW,EAAU,OAAE,GAE1EE,KAEID,GACFpT,EAASoT,EAAM,qBAIbtT,EAAW9B,EAAMsV,oBAAmB,CAAAtE,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EAChC/P,EAAMsV,mBAAmBrF,KAAKjQ,EAAOmV,GAAO,KAAA,EAAA,IAAA,MAAA,OAAAnE,EAAAI,OAAA,GAAAL,EAErD,MAAA3C,MAAAjN,KAAAX,UAAA,CAOqB+U,SAAAA,GAAS9C,GAAA,OAAA+C,GAAApH,MAAAjN,KAAAX,UAAA,CAW/B,SAAAgV,KAFC,OAEDA,GAAAnH,EAAAC,IAAAC,MAXO,SAAA+C,EAAyBtR,GAAc,IAAAyV,EAAAC,EAAAC,EAAAP,EAAAzN,EAAA,OAAA2G,IAAAsB,MAAA,SAAA2B,GAAA,cAAAA,EAAAzB,KAAAyB,EAAAxB,MAAA,KAAA,EACtC0F,EAAQX,GAAqB,iBAAgBY,EAAA/S,EAEhC8S,GAAKlE,EAAAzB,KAAA,EAAA4F,EAAA9S,IAAA,KAAA,EAAA,IAAA+S,EAAAD,EAAA7S,KAAAC,KAAA,CAAAyO,EAAAxB,KAAA,GAAA,KAAA,CACqB,GADlCqF,EAAIO,EAAAxX,MACPwJ,EAAOyN,EAAK/S,aAAa,aACtB,CAAAkP,EAAAxB,KAAA,EAAA,KAAA,CAAA,OAAAwB,EAAA4B,OAAA,WAAA,IAAA,KAAA,EAAA,OAAA5B,EAAAxB,KAAA,GAEHkF,GAASjV,EAAO4M,SAASjF,EAAM,KAAI,KAAA,GAAA4J,EAAAxB,KAAA,EAAA,MAAA,KAAA,GAAAwB,EAAAxB,KAAA,GAAA,MAAA,KAAA,GAAAwB,EAAAzB,KAAA,GAAAyB,EAAAqE,GAAArE,EAAA,MAAA,GAAAmE,EAAApU,EAAAiQ,EAAAqE,IAAA,KAAA,GAAA,OAAArE,EAAAzB,KAAA,GAAA4F,EAAAxS,IAAAqO,EAAAsE,OAAA,IAAA,KAAA,GAAA,IAAA,MAAA,OAAAtE,EAAAH,OAAA,GAAAE,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA,GAAA,KAE5C,MAAAlD,MAAAjN,KAAAX,UAAA,CAOqBsV,SAAAA,GAASpD,GAAA,OAAAqD,GAAA3H,MAAAjN,KAAAX,UAAA,CAe/B,SAAAuV,KAFC,OAEDA,GAAA1H,EAAAC,IAAAC,MAfO,SAAAoD,EAAyB3R,GAAc,IAAAyV,EAAAO,EAAAC,EAAAb,EAAAzN,EAAA,OAAA2G,IAAAsB,MAAA,SAAAgC,GAAA,cAAAA,EAAA9B,KAAA8B,EAAA7B,MAAA,KAAA,EACO,KAA7C0F,EAAQX,GAAqB,oBAEtBW,EAAMhV,OAAM,CAAAmR,EAAA7B,KAAA,GAAA,KAAA,CAAAiG,EAAArT,EACJ8S,GAAK7D,EAAA9B,KAAA,EAAAkG,EAAApT,IAAA,KAAA,EAAA,IAAAqT,EAAAD,EAAAnT,KAAAC,KAAA,CAAA8O,EAAA7B,KAAA,GAAA,KAAA,CACqB,GADlCqF,EAAIa,EAAA9X,MACPwJ,EAAOyN,EAAK/S,aAAa,aACtB,CAAAuP,EAAA7B,KAAA,GAAA,KAAA,CAAA,OAAA6B,EAAAuB,OAAA,WAAA,IAAA,KAAA,GAET+C,GAAStJ,SAASjF,EAAM,KAAK,KAAA,GAAAiK,EAAA7B,KAAA,EAAA,MAAA,KAAA,GAAA6B,EAAA7B,KAAA,GAAA,MAAA,KAAA,GAAA6B,EAAA9B,KAAA,GAAA8B,EAAAgE,GAAAhE,EAAA,MAAA,GAAAoE,EAAA1U,EAAAsQ,EAAAgE,IAAA,KAAA,GAAA,OAAAhE,EAAA9B,KAAA,GAAAkG,EAAA9S,IAAA0O,EAAAiE,OAAA,IAAA,KAAA,GAAAjE,EAAA7B,KAAA,GAAA,MAAA,KAAA,GAAA,OAAA6B,EAAA7B,KAAA,GAGzBoG,GAAcnW,EAAOA,EAAM8H,gBAAe,KAAA,GAAA,IAAA,MAAA,OAAA8J,EAAAR,OAAA,GAAAO,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA,GAAA,KAEnD,MAAAvD,MAAAjN,KAAAX,UAAA,CAOM,SAAS0V,GAASf,GACvB,IAAMC,EAAON,GAAoB5V,4BAAAA,OAA6BiW,EAAU,OAAE,GAEtEC,GACFhO,EAAYgO,EAAM,oBAEtB,CAuCO,SAASgB,GAAWjB,GACzB,IAAMC,EAAON,GAAoB5V,4BAAAA,OAA6BiW,EAAU,OAAE,GAEtEC,GAAQA,EAAKtD,YACfsD,EAAKtD,WAAWC,YAAYqD,EAEhC,CAOsBiB,SAAAA,GAAQxD,GAAA,OAAAyD,GAAAlI,MAAAjN,KAAAX,UAAA,CA8F9B,SAAA8V,KAFC,OAEDA,GAAAjI,EAAAC,IAAAC,MA9FO,SAAAC,EAAwBxO,GAAc,IAAAgV,EAAAuB,EAAA1J,EAAA2J,EAAApB,EAAAqB,EAAAC,EAAA,OAAApI,IAAAsB,MAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,KAAA,EAGtB,QAFjBiF,EAAe7V,SAASiN,cAAc,qBAGxC4I,EAAevJ,EAAc,MAAO,CAClCvJ,UAAW,mBAOTqU,EAAe,SAAC1J,GAAS,OAAK,SAACvL,GACnC,IAAMqV,EAAMrV,GAAQC,OAAOC,MAEvBmV,GAAOA,EAAIC,iBACbD,EAAIC,kBAGFD,GAA4B,OAArBA,EAAIE,eACbF,EAAIE,cAAe,GAGrBC,GAAe9W,EAAO6M,GACvB,EAEQA,EAAI,EAAC,KAAA,EAAA,KAAEA,EAAI7M,EAAM+W,WAAWtW,QAAM,CAAAoP,EAAAE,KAAA,GAAA,KAAA,CAGzC,GAFMyG,EAAOxW,EAAM+W,WAAWlK,IAG1B1N,SAASiN,cAAa,4BAAAlN,OAA6B2N,SAAM,CAAAgD,EAAAE,KAAA,EAAA,KAAA,CAAA,OAAAF,EAAAsD,OAAA,UAAA,KAAA,EAO7D/M,EAHMgP,EAAO3J,EAAc,IAAK,CAC9BvJ,UAAW,kBAIbkT,EAAKnI,QAAUsJ,EAAa1J,GAEvB2J,EAAKQ,eACRhV,EAASoT,EAAM,wBAIb7O,EAAQiQ,EAAKvU,UACfD,EAASoT,EAAM,qBAGXqB,EAAUhL,EAAc,MAAO,CACnCvJ,UAAW,qBAGPwU,EAAYjL,EAAc,MAAO,CACrCvJ,UAAW,uBAGbkT,EAAKzJ,YAAY8K,GACjBrB,EAAKzJ,YAAY+K,GACjBtB,EAAK7S,aAAa,YAAasK,EAAEM,YAIjCqJ,EAAKS,kBAAoBT,EAAKvU,QAC9BuU,EAAKvU,QAAUmT,EAGf8B,GACEV,EAAKW,aACL/B,EACAoB,EAAKS,mBAGPjC,EAAarJ,YAAYyJ,GAAM,KAAA,GAhDYvI,IAAGgD,EAAAE,KAAA,EAAA,MAAA,KAAA,GAsDhD,GAFA5Q,SAASkF,KAAKsH,YAAYqJ,IAGtBlT,EAAW9B,EAAMoX,qBAAoB,CAAAvH,EAAAE,KAAA,GAAA,KAAA,CAAA,OAAAF,EAAAE,KAAA,GACjC/P,EAAMoX,oBAAoBnH,KAAKjQ,GAAM,KAAA,GAGzCA,EAAME,SAASmX,yBAA2B,IAC5CrX,EAAMsX,0BAA4B/C,IAChC,WAAA,OAAMgD,GAAavX,EAAM,GACzBA,EAAME,SAASmX,yBAEjB1W,EAAS6W,GAAGjW,OAAQ,SAAUvB,EAAMsX,0BAA2BtX,GAAO,IACvE,KAAA,GAAA,IAAA,MAAA,OAAA6P,EAAAuB,OAAA,GAAA5C,EACF,MAAAJ,MAAAjN,KAAAX,UAAA,CAOM,SAAS0W,GACd/S,EACAsT,EACA7T,GAEA,QAA6B,IAAlBA,EAAX,CAKA,IAAM8T,EAASjR,EAAU7C,GACnB+T,EAAY,GACZC,EAAa,GAGnB,OAAQzT,GACN,QACA,IAAK,WACHsT,EAAYxT,MAAM4B,KAAI,GAAA3G,OAAMwY,EAAO7R,KAAQ,MAC3C4R,EAAYxT,MAAM2B,IAAG,GAAA1G,OAAMwY,EAAO9R,IAAO,MACzC,MACF,IAAK,YACH6R,EAAYxT,MAAM4B,KAAI,GAAA3G,OAAMwY,EAAO7R,KAAO6R,EAAO1S,MAAQ2S,EAAa,MACtEF,EAAYxT,MAAM2B,IAAG,GAAA1G,OAAMwY,EAAO9R,IAAO,MACzC,MACF,IAAK,cACH6R,EAAYxT,MAAM4B,KAAI,GAAA3G,OAAMwY,EAAO7R,KAAQ,MAC3C4R,EAAYxT,MAAM2B,IAAG,GAAA1G,OAAMwY,EAAO9R,IAAM8R,EAAOzS,OAAS2S,EAAc,MACtE,MACF,IAAK,eACHH,EAAYxT,MAAM4B,KAAI,GAAA3G,OAAMwY,EAAO7R,KAAO6R,EAAO1S,MAAQ2S,EAAa,MACtEF,EAAYxT,MAAM2B,IAAG,GAAA1G,OAAMwY,EAAO9R,IAAM8R,EAAOzS,OAAS2S,EAAc,MACtE,MACF,IAAK,cACHH,EAAYxT,MAAM4B,KAAI,GAAA3G,OAAMwY,EAAO7R,KAAQ,MAC3C4R,EAAYxT,MAAM2B,IAAG1G,GAAAA,OACnBwY,EAAO9R,KAAO8R,EAAOzS,OAAS2S,GAAc,EAC1C,MACJ,MACF,IAAK,eACHH,EAAYxT,MAAM4B,KAAI,GAAA3G,OAAMwY,EAAO7R,KAAO6R,EAAO1S,MAAQ2S,EAAa,MACtEF,EAAYxT,MAAM2B,IAAG1G,GAAAA,OACnBwY,EAAO9R,KAAO8R,EAAOzS,OAAS2S,GAAc,EAC1C,MACJ,MACF,IAAK,gBACHH,EAAYxT,MAAM4B,KAAI3G,GAAAA,OACpBwY,EAAO7R,MAAQ6R,EAAO1S,MAAQ2S,GAAa,EACzC,MACJF,EAAYxT,MAAM2B,IAAG1G,GAAAA,OACnBwY,EAAO9R,KAAO8R,EAAOzS,OAAS2S,GAAc,EAC1C,MACJ,MACF,IAAK,gBACHH,EAAYxT,MAAM4B,KAAI3G,GAAAA,OACpBwY,EAAO7R,MAAQ6R,EAAO1S,MAAQ2S,GAAa,EACzC,MACJF,EAAYxT,MAAM2B,IAAG,GAAA1G,OAAMwY,EAAO9R,IAAM8R,EAAOzS,OAAS2S,EAAc,MACtE,MACF,IAAK,aACHH,EAAYxT,MAAM4B,KAAI3G,GAAAA,OACpBwY,EAAO7R,MAAQ6R,EAAO1S,MAAQ2S,GAAa,EACzC,MACJF,EAAYxT,MAAM2B,IAAG,GAAA1G,OAAMwY,EAAO9R,IAAO,MAxD7C,CA2DF,CAOA,SAAsBkR,GAAczD,EAAAwE,GAAA,OAAAC,GAAA1J,MAAAjN,KAAAX,UAAA,CA4EpC,SAAAsX,KAFC,OAEDA,GAAAzJ,EAAAC,IAAAC,MA5EO,SAAAwJ,EAA8B/X,EAAgBmV,GAAc,IAAAsC,EAAAjB,EAAAwB,EAAAvS,EAAA2J,EAAAhF,EAAA+E,EAAA8I,EAAAC,EAAAvQ,EAAAwC,EAAA,OAAAmE,IAAAsB,MAAA,SAAAuI,GAAA,cAAAA,EAAArI,KAAAqI,EAAApI,MAAA,KAAA,EAMjE,GALM0H,EAActY,SAASiN,0CAAalN,OACZiW,EAAM,OAE9BqB,EAAOxW,EAAM+W,WAAW5B,IAG1BrT,EAAW9B,EAAMoY,oBAAmB,CAAAD,EAAApI,KAAA,EAAA,KAAA,CAAA,OAAAoI,EAAApI,KAAA,EAChC/P,EAAMoY,mBAAmBnI,KAAKjQ,EAAOyX,EAAajB,EAAMrB,GAAO,KAAA,EAMvE,QACoB3W,KAHdwZ,EAAc3C,OAGazI,SAASoL,EAAa,MAAQ7C,EAAM,CAAAgD,EAAApI,KAAA,EAAA,KAAA,CAAA,OAAAoI,EAAAhF,OAAA,UAAA,KAAA,EAI/D1N,EAAegG,EAAc,MAAO,CACxCvJ,UAAW,oBAEPkN,EAAmB3D,EAAc,OACjCrB,EAAaqB,EAAc,OAC3B0D,EAAiB1D,EAAc,OAErChG,EAAawH,QAAU,SAAC3L,GAElBA,EAAEsV,gBACJtV,EAAEsV,kBAIFtV,EAAEuV,cAAe,GAIrBzH,EAAiBlN,UAAY,uBAEvB+V,EAAiBxM,EAAc,MACtByB,UAAYsJ,EAAKpB,MAAQ,GACxChG,EAAiBzD,YAAYsM,GAEzBjY,EAAME,SAASmY,kBACXH,EAAczM,EAAc,MACtBvJ,UAAYlC,EAAME,SAAS+R,YACvCiG,EAAY3V,aAAa,OAAQ,UACjC2V,EAAYhL,UAAYlN,EAAME,SAASoY,gBACvCJ,EAAYjL,QAAU,WAAA,OAAMgI,GAASjV,EAAOmV,EAAO,EACnD/F,EAAiBzD,YAAYuM,IAG/B9N,EAAWlI,UAAY,gBACvBuD,EAAakG,YAAYvB,GAEzB3E,EAAakG,YAAYyD,GAEnBzH,EAAO8P,EAAYpV,aAAa,cAAgB,GAGtDrC,EAAM8N,aAAelB,SAASjF,EAAM,IAC9BwC,EAAcnK,EAAM+W,WAAW/W,EAAM8N,cAG3CqB,EAAejN,UACb,sDACFiN,EAAe5M,aAAa,YAAaoF,GACzCD,EAAuB1H,EAAOmK,EAAagF,GAE3CA,EAAexD,YAAYlG,GAC3BtG,SAASkF,KAAKsH,YAAYwD,GAG1BjF,EAAalK,EAAOmK,EAAa1E,EAAc2E,GAAY,GAAM,KAAA,GAAA,IAAA,MAAA,OAAA+N,EAAA/G,OAAA,GAAA2G,EAClE,MAAA3J,MAAAjN,KAAAX,UAAA,CAOM,SAAS6U,KACd,IAAMkD,EAAUpZ,SAASiN,cAAc,0BAEvC,GAAImM,GAAWA,EAAQzG,WAAY,CACjC,IAAMnK,EAAO4Q,EAAQlW,aAAa,aAClC,IAAKsF,EAAM,OAIX,OAFA4Q,EAAQzG,WAAWC,YAAYwG,GAExB5Q,CACT,CAGF,CAOA,SAAsBwO,GAAaqC,EAAAC,GAAA,OAAAC,GAAAtK,MAAAjN,KAAAX,UAAA,CAqEnC,SAAAkY,KAFC,OAEDA,GAAArK,EAAAC,IAAAC,MArEO,SAAAoK,EACL3Y,EACA4Y,GAAsB,IAAAC,EAAAC,EAAA1D,EAAA2D,EAAAtD,EAAAvK,EAAA8N,EAAAC,EAAAC,EAAAlC,EAAA,OAAA1I,IAAAsB,MAAA,SAAAuJ,GAAA,cAAAA,EAAArJ,KAAAqJ,EAAApJ,MAAA,KAAA,EAEA,GAAtB/P,EAAM+W,WAAa,KAEf/W,EAAME,SAASuV,OAASzV,EAAME,SAASuV,MAAMhV,OAAS,GAAC,CAAA0Y,EAAApJ,KAAA,EAAA,KAAA,CAAA8I,EAAAlW,EACtC3C,EAAME,SAASuV,OAAK,IAAvC,IAAAoD,EAAAjW,MAAAkW,EAAAD,EAAAhW,KAAAC,MAAWsS,EAAI0D,EAAA3a,MAGsB,iBAF7B4a,EAAc7E,GAAYkB,IAETnT,UAErB8W,EAAY9W,QAAU9C,SAASiN,cAC7B2M,EAAY9W,UAIhB8W,EAAY5B,aACV4B,EAAY5B,cAAgBnX,EAAME,SAASiX,aAC7C4B,EAAY/B,cACV+B,EAAY/B,eAAiBhX,EAAME,SAAS8W,cAElB,OAAxB+B,EAAY9W,SACdjC,EAAM+W,WAAW9X,KAAK8Z,EAEzB,CAAA,MAAA9V,GAAA4V,EAAAvX,EAAA2B,EAAA,CAAA,QAAA4V,EAAA3V,GAAA,CAAAiW,EAAApJ,KAAA,GAAA,MAAA,KAAA,EAIA,IAFK0F,EAAQrK,MAAMC,KAClBuN,EAAUtN,iBAA8B,mBAG3BmK,EAAMhV,OAAM,CAAA0Y,EAAApJ,KAAA,EAAA,KAAA,CAAA,OAAAoJ,EAAAhG,OAAA,UAClB,GAAK,KAAA,EAId,IAAAjI,EAAA,EAAA8N,EAA6BvD,EAAKvK,EAAA8N,EAAAvY,OAAAyK,IAAvB+N,EAAcD,EAAA9N,GAEnBgO,EAAoBD,EAAe5W,aACrC,uBAGE2U,EAAyBhX,EAAME,SAAS8W,cACxCkC,IACFlC,EAAsC,SAAtBkC,GAGlBlZ,EAAM+W,WAAW9X,KAAK,CACpBgD,QAASgX,EACT7D,KAAM6D,EAAe5W,aAAa,cAAgB,GAClD8U,aAAe8B,EAAe5W,aAAa,uBACzCrC,EAAME,SAASiX,aACjBH,cAAAA,EACArM,aACEsO,EAAe5W,aAAa,4BAAyB7D,EACvD2F,SAAW8U,EAAe5W,aAAa,kBACrCrC,EAAME,SAASkZ,kBAEpB,KAAA,GAAA,OAAAD,EAAApJ,KAAA,GAGGsG,GAASrW,GAAM,KAAA,GAGoC,OADzDW,EAAS6W,GAAGrY,SAAU,QAASkW,GAAmBrV,GAAO,GACzDW,EAAS6W,GAAGjW,OAAQ,SAAUgW,GAAcvX,GAAO,GAAMmZ,EAAAhG,OAAA,UAElD,GAAI,KAAA,GAAA,IAAA,MAAA,OAAAgG,EAAA/H,OAAA,GAAAuH,EACZ,MAAAvK,MAAAjN,KAAAX,UAAA,CAOM,SAAS+W,GAAavX,GAAgB,IACgCqZ,EADhCC,EAAA3W,EACgB3C,EAAM+W,YAAU,IAA3E,IAAAuC,EAAA1W,MAAAyW,EAAAC,EAAAzW,KAAAC,MAA6E,CAAA,IAAAyW,EAAAF,EAAAlb,MAAhE8Y,EAAiBsC,EAAjBtC,kBACXC,GAD0CqC,EAAZpC,aAAqBoC,EAAPtX,QACYgV,EAC1D,CAAC,CAAA,MAAAhU,GAAAqW,EAAAhY,EAAA2B,EAAA,CAAA,QAAAqW,EAAApW,GAAA,CACH,CCree,SAASsW,GACtBxZ,EACA4Y,GAEA,IAAMa,EAA+BrO,MAAMC,KACzCuN,EAAUtN,iBAAiB,kBAEzBoO,EAA0B,GAE9B,GAAI1Z,EAAME,SAASyZ,OAAS3Z,EAAME,SAASyZ,MAAMlZ,OAAQ,CACvD,IACuCgC,EADvCC,EAAAC,EACmB3C,EAAME,SAASyZ,OAAK,IAAvC,IAAAjX,EAAAE,MAAAH,EAAAC,EAAAG,KAAAC,MAAyC,CAAA,IACjCiW,EAAc7E,GADPzR,EAAAtE,OAgBb,GAZA4a,EAAYpR,KAAO+R,EAAWjZ,OAAS,EAEvCsY,EAAY1I,MAAQ0I,EAAY1I,OAAS,GAGN,iBAAxB0I,EAAY9W,UAErB8W,EAAY9W,QACV9C,SAASiN,cAA2B2M,EAAY9W,eAAYzD,QAK/B,IAAxBua,EAAY9W,SACK,OAAxB8W,EAAY9W,QACZ,CACA,IAAI2X,EAAuBza,SAASiN,cAClC,2BAG2B,OAAzBwN,IACFA,EAAuBnO,EAAc,MAAO,CAC1CvJ,UAAW,2BAGb/C,SAASkF,KAAKsH,YAAYiO,IAG5Bb,EAAY9W,QAAU2X,EACtBb,EAAY5U,SAAW,UACzB,CAEA4U,EAAY5U,SACV4U,EAAY5U,UACXnE,EAAME,SAASkZ,gBAClBL,EAAYxT,SAAWwT,EAAYxT,UAAYvF,EAAME,SAASqF,cAEhB,IAAnCwT,EAAY/G,qBACrB+G,EAAY/G,mBAAqBhS,EAAME,SAAS8R,oBAGtB,OAAxB+G,EAAY9W,SACdyX,EAAWza,KAAK8Z,EAEpB,CAAC,CAAA,MAAA9V,GAAAP,EAAApB,EAAA2B,EAAA,CAAA,QAAAP,EAAAQ,GAAA,CACH,KAAO,CAEL,IACI8O,EAGJ,GAJmByH,EAAchZ,OAIhB,EACf,MAAO,GACR,IAEyC4Y,EAFzCC,EAAA3W,EAE4B8W,GAAa,IAA1C,IAAAH,EAAA1W,MAAAyW,EAAAC,EAAAzW,KAAAC,MAA4C,CAAA,IAAjCmW,EAAcI,EAAAlb,MAEvB,KACE6B,EAAME,SAAS2Z,OACfZ,EAAe5W,aAAa,sBAAwBrC,EAAME,SAAS2Z,QAMhC,SAAjCZ,EAAehV,MAAMyG,QAAzB,CAIA,IAAM/C,EAAOiF,SAASqM,EAAe5W,aAAa,cAAgB,GAAI,IAEtE2P,EAAqBhS,EAAME,SAAS8R,mBAChCiH,EAAea,aAAa,8BAC9B9H,IAAuBiH,EAAe5W,aACpC,6BAIAsF,EAAO,IACT+R,EAAW/R,EAAO,GAAK,CACrBA,KAAMA,EACN1F,QAASgX,EACT5I,MAAO4I,EAAe5W,aAAa,eAAiB,GACpDrC,MAAOiZ,EAAe5W,aAAa,eAAiB,GACpDsI,aACEsO,EAAe5W,aAAa,4BAAyB7D,EACvDkQ,eACEuK,EAAe5W,aAAa,8BAA2B7D,EACzD2F,SAAW8U,EAAe5W,aAAa,kBACrCrC,EAAME,SAASkZ,gBACjB7T,SACG0T,EAAe5W,aAAa,mBAC7BrC,EAAME,SAASqF,SACjByM,mBAAAA,GA1BJ,CA6BF,CAGA,CAAA,MAAA/O,GAAAqW,EAAAhY,EAAA2B,EAAA,CAAA,QAAAqW,EAAApW,GAAA,CACA,IAE0CyS,EAFtC1E,EAAW,EAAEyE,EAAA/S,EAEY8W,GAAa,IAA1C,IAAA/D,EAAA9S,MAAA+S,EAAAD,EAAA7S,KAAAC,MAA4C,CAAA,IAAjCmW,EAActD,EAAAxX,MAEvB,KACE6B,EAAME,SAAS2Z,OACfZ,EAAe5W,aAAa,sBAAwBrC,EAAME,SAAS2Z,QAKpB,OAA7CZ,EAAe5W,aAAa,aAAuB,CACrD,UACsC,IAAzBqX,EAAWzI,IAGpBA,IAKFe,EADEiH,EAAea,aAAa,8BACPb,EAAe5W,aACpC,4BAGmBrC,EAAME,SAAS8R,mBAGtC0H,EAAWzI,GAAY,CACrBhP,QAASgX,EACT5I,MAAO4I,EAAe5W,aAAa,eAAiB,GACpDrC,MAAOiZ,EAAe5W,aAAa,eAAiB,GACpDsF,KAAMsJ,EAAW,EACjBtG,aACEsO,EAAe5W,aAAa,4BAAyB7D,EACvDkQ,eACEuK,EAAe5W,aAAa,8BAA2B7D,EACzD2F,SAAW8U,EAAe5W,aAAa,kBACrCrC,EAAME,SAASkZ,gBACjB7T,SACG0T,EAAe5W,aAAa,mBAC7BrC,EAAME,SAASqF,SACjByM,mBAAAA,EAEJ,CACF,CAAC,CAAA,MAAA/O,GAAAyS,EAAApU,EAAA2B,EAAA,CAAA,QAAAyS,EAAAxS,GAAA,CACH,CAIA,IADA,IAAM6W,EAAiB,GACdC,EAAI,EAAGA,EAAIN,EAAWjZ,OAAQuZ,IACjCN,EAAWM,IAEbD,EAAe9a,KAAKya,EAAWM,IASnC,OALAN,EAAaK,GAGFE,MAAK,SAACC,EAAGC,GAAC,OAAKD,EAAEvS,KAAOwS,EAAExS,QAE9B+R,CACT,CCjLe,SAASU,GAAQpa,EAAgBqa,GAC9C,IAAMlQ,EAAcnK,EAAM8N,aAE1B,GAAI3D,UAAqE,GAAhBA,EAAzD,CAGA,IAAMxC,EAAO3H,EAAM8M,YAAY3C,GAEzBgF,EAAiBhQ,SAASiN,cAC9B,kCAEIxE,EAAczI,SAASiN,cAC3B,wBAEID,EAA0BhN,SAASiN,cACvC,+BAIF1E,EAAuB1H,EAAO2H,EAAMC,GACpCF,EAAuB1H,EAAO2H,EAAMwH,GACpCzH,EAAuB1H,EAAO2H,EAAMwE,GAEhCkO,IACFra,EAAM8M,YAAc0M,GAAgBxZ,EAAOA,EAAM8H,gBPoE9C,SAA0B9H,EAAgB4D,GAC/C,GAAI5D,EAAME,SAASqM,YAAa,CAC9B,IAAM+N,EAAWnb,SAASiN,cAAc,oBAEpCkO,GAAYA,EAASxI,YACvBwI,EAASxI,WAAWyI,aAClBlO,EAAerM,EAAO4D,GACtB0W,EAGN,CACF,CO9EIE,CAAiBxa,EAAO2H,GACxBoG,EAAmBoB,EAAgBhF,EAAanK,EAAM8M,YAAYrM,SAIpE,IAAMwO,EAAgB9P,SAASiN,cAA2B,kBACpD8C,EACJ/P,SAASiN,cAA2B,oBActC,OAZI8C,GAAuBD,GACzB/E,EACElK,EACAA,EAAM8M,YAAY3C,GAClB+E,EACAD,GAKJsI,GAAavX,GAENA,CA1CL,CA2CJ,CCvDe,SAASya,GAASza,GAC/Boa,GAAQpa,EACV,CCAe,SAAS+R,GACtB9P,GAEA,IADA2J,EAAOpL,UAAAC,OAAA,QAAAjC,IAAAgC,UAAA,IAAAA,UAAA,GAEP,GAAKyB,GAAYA,EAAQqC,cAAzB,CAEA,IAAMA,EAAgBrC,EAAQqC,cAE1BsH,GACFrE,EAAStF,EAAS,CAChB6J,QAAS,MAGXvK,OAAOwK,YAAW,WAChB,IAKEzH,EAAcyN,YAAY9P,EACf,CAAX,MAAOX,GAAI,CACd,GAAE,MAEHgD,EAAcyN,YAAY9P,EAnBY,CAqB1C,CChBA,SAA8BkP,GAASlD,EAAAC,GAAA,OAAAwM,GAAAtM,MAAAjN,KAAAX,UAAA,CAoEtC,SAAAka,KAAA,OAAAA,GAAArM,EAAAC,IAAAC,MApEc,SAAAwC,EACb/Q,EACA4D,GAA0B,IAAA+W,EAAAC,EAAAC,EAAAnY,EAAAD,EAAAqY,EAAAta,UAAA,OAAA8N,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAO1B,GANA4K,EAAcG,EAAAra,OAAA,QAAAjC,IAAAsc,EAAA,IAAAA,EAAA,GAEVF,GAAe,OAKoBpc,IAAnCwB,EAAM+a,yBAAsC,CAAA/J,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EACzB/P,EAAM+a,yBAAyB9K,KAClDjQ,EACA4D,GACD,KAAA,EAHDgX,EAAY5J,EAAAkC,KAAA,KAAA,EAAA,GAQTyH,IAA0B,IAAjBC,EAAsB,CAAA5J,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAmC,OAAA,UAAA,KAAA,EAOpC,IAJM0H,EAAgBzP,MAAMC,KAC1BzH,EAAc0H,iBAA8B,uBAGzBuP,EAAcpa,OAAQ,CAAAiC,EAAAC,EACdkY,GAAa,IAAxC,IAAAnY,EAAAE,MAAAH,EAAAC,EAAAG,KAAAC,MACEiP,GADqBtP,EAAAtE,MAEtB,CAAA,MAAA8E,GAAAP,EAAApB,EAAA2B,EAAA,CAAA,QAAAP,EAAAQ,GAAA,CACH,CA+BA,GAzBA6O,GAHoBnO,EAAcwI,cAChC,yBAEuB,GAKzB2F,GAHuBnO,EAAcwI,cACnC,mCAQF2F,GAHgCnO,EAAcwI,cAC5C,gCAQF2F,GAHwB5S,SAASiN,cAC/B,4BAIFnB,IAGAtK,EAASqa,IAAIzZ,OAAQ,UAAWgS,GAAWvT,GAAO,GAClDW,EAASqa,IAAIzZ,OAAQ,SAAUkZ,GAAUza,GAAO,IAG5C8B,EAAW9B,EAAMib,oBAAmB,CAAAjK,EAAAjB,KAAA,GAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,GAChC/P,EAAMib,mBAAmBhL,KAAKjQ,GAAM,KAAA,GAI5CA,EAAM8N,cAAgB,EAAE,KAAA,GAAA,IAAA,MAAA,OAAAkD,EAAAI,OAAA,GAAAL,EACzB,KAAA2J,GAAAtM,MAAAjN,KAAAX,UAAA,CCxEc,SAAS0a,GACtBlb,EACA4Y,GAEA,IAAMuC,EAAe1P,EAAc,MAAO,CACxCvJ,UAAW,oBAuBb,OApBAqF,EAAS4T,EAAc,CACrBvV,IAAK,EACLE,OAAQ,EACRD,KAAM,EACNE,MAAO,EACP5B,SAAU,UAGZyU,EAAUjN,YAAYwP,IAEoB,IAAtCnb,EAAME,SAASkb,qBACjB7T,EAAS4T,EAAc,CACrBE,OAAQ,YAGVF,EAAalO,QAAOoB,EAAAC,IAAAC,MAAG,SAAAwC,IAAA,OAAAzC,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAAA,OAAAiB,EAAAjB,KAAA,EACfoB,GAAUnR,EAAO4Y,GAAU,KAAA,EAAA,IAAA,MAAA,OAAA5H,EAAAI,OAAA,GAAAL,EAClC,OAGI,CACT,CCzBA,SAA8BuK,GAAerN,EAAAC,GAAA,OAAAqN,GAAAnN,MAAAjN,KAAAX,UAAA,CAmC5C,SAAA+a,KAAA,OAAAA,GAAAlN,EAAAC,IAAAC,MAnCc,SAAAwC,EACb/Q,EACA4Y,GAAsB,IAAAe,EAAA,OAAArL,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAAA,GAGjB/P,EAAMwb,WAAU,CAAAxK,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAmC,OAAA,UAAS,GAAK,KAAA,EAAA,IAE/BrR,EAAW9B,EAAMyb,qBAAoB,CAAAzK,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAjB,KAAA,EACjC/P,EAAMyb,oBAAoBxL,KAAKjQ,EAAO4Y,GAAU,KAAA,EAIT,GAE1B,KAFfe,EAAQH,GAAgBxZ,EAAO4Y,IAE3BnY,OAAY,CAAAuQ,EAAAjB,KAAA,EAAA,KAAA,CAAA,OAAAiB,EAAAmC,OAAA,UACb,GAAK,KAAA,EAMuB,OAHrCnT,EAAM8M,YAAc6M,EAGhBuB,GAAgBlb,EAAO4Y,GAAU5H,EAAAjB,KAAA,GAE7BkB,EAASjR,GAAM,KAAA,GAErB4Y,EAAUnX,iBACNzB,EAAME,SAASwb,oBACjB/a,EAAS6W,GAAGjW,OAAQ,UAAWgS,GAAWvT,GAAO,GAInDW,EAAS6W,GAAGjW,OAAQ,SAAUkZ,GAAUza,GAAO,GAAM,KAAA,GAAA,OAAAgR,EAAAmC,OAAA,UAGhD,GAAK,KAAA,GAAA,IAAA,MAAA,OAAAnC,EAAAI,OAAA,GAAAL,EACb,MAAA3C,MAAAjN,KAAAX,UAAA,CC2FM,SAASmb,GACdC,EACA5c,EACAb,GAGA,OADAyd,EAAQ5c,GAAOb,EACRyd,CACT,CC9EA,IAAaC,GAAO,WAwBlB,SAAAA,EAAmBjY,GAA4BhD,OAAAib,GAAAtd,EAAA4C,KAAA,gBAvBhB,GAAC5C,EAAA4C,KAAA,0BAAA,GAAA5C,EAAA4C,KAAA,kBAAA,GAAA5C,EAAA4C,KAAA,sBAAA,GAAA5C,qBAIE,IAAEA,oBACJ,IAAEA,EAAA4C,KAAA,gBAAA,GAAA5C,EAAA4C,KAAA,kCAAA,GAAA5C,EAAA4C,KAAA,4BAAA,GAAA5C,EAAA4C,KAAA,iCAAA,GAAA5C,EAAA4C,KAAA,8BAAA,GAAA5C,EAAA4C,KAAA,2BAAA,GAAA5C,EAAA4C,KAAA,0BAAA,GAAA5C,EAAA4C,KAAA,0BAAA,GAAA5C,EAAA4C,KAAA,gCAAA,GAAA5C,EAAA4C,KAAA,2BAAA,GAAA5C,EAAA4C,KAAA,0BAAA,GAAA5C,EAAA4C,KAAA,0BAAA,GAAA5C,EAAA4C,KAAA,6BAAA,GAAA5C,EAAA4C,KAAA,iCAAA,GAmBhCA,KAAK2G,eAAiBlE,EACtBzC,KAAKjB,SDFA,CACLyZ,MAAO,GACPlE,MAAO,GACP+F,UAAU,EACVnK,UAAW,OACXI,UAAW,OACXC,UAAW,IACXW,UAAW,OACXH,UAAU,EACVC,UAAU,EACVC,YAAY,EACZgH,gBAAiB,SACjBzO,aAAc,GACdkP,MAAO,GACPnL,eAAgB,GAChBmF,WAAW,EACXuH,oBAAoB,EACpBpQ,iBAAiB,EACjBoF,mBAAoB,KACpBsL,oBAAoB,EACpBlL,aAAa,EACbjE,aAAa,EACbmB,cAAc,EACd3J,iBAAiB,EACjBwB,SAAU,UACVC,cAAe,GACf+K,eAAgB,GAChBzF,cAAc,EACd9B,mBAAoB,CAAC,SAAU,MAAO,QAAS,QAC/CgJ,oBAAoB,EAEpB/R,eAAe,EACf6Q,mBAAoB,wBACpB3Q,oBAAqB,wBACrBC,wBAAyB,IACzB4H,qBAAsB,GAEtBmP,aAAc,aACdmB,gBAAiB,SACjBD,gBAAgB,EAChBhB,wBAAyB,GACzBL,eAAe,EACf/E,YAAa,iBACbrE,4BAA4B,ECxC9B,CA8NC,IAAAkO,EAfAC,EAVAC,EALAC,EAzHAC,EATAC,EALAC,EALAC,EAzBAC,EALAC,EA6MA,OAnOA1b,EAAAgb,EAAA,CAAA,CAAA7c,IAAA,WAAAb,MAED,WACE,QAAIgD,KAAKjB,SAASD,evCtEM,MADpBuc,EAAiBnd,EuCuE+B8B,KvCvEfjB,SAASC,uBAChBqc,IAAmB1c,IuC0E1CqB,KAAKjB,SAASsb,SvC5ElB,IACCgB,CuC4EN,GAAC,CAAAxd,IAAA,QAAAb,MAED,WACE,OAAO,IAAI0d,EAAQ1a,KAAK2G,eAC1B,GAAC,CAAA9I,IAAA,YAAAb,MAED,SAAmCa,EAAQb,GAEzC,OADAgD,KAAKjB,SAAWyb,GAAUxa,KAAKjB,SAAUlB,EAAKb,GACvCgD,IACT,GAAC,CAAAnC,IAAA,aAAAb,MAED,SAAWse,GAET,OADAtb,KAAKjB,SDiCF,SACL0b,EACAa,GAEA,IAAA,IAAAvR,EAAAwR,EAAAA,EAA2BxV,OAAOyV,QAAQF,GAAevR,EAAAwR,EAAAjc,OAAAyK,IAAE,CAAtD,IAAA0R,EAAAld,EAAAgd,EAAAxR,GAAA,GACH0Q,EAAUD,GAAUC,EADPgB,EAAA,GAAOA,EAAA,GAEtB,CACA,OAAOhB,CACT,CCzCoBiB,CAAW1b,KAAKjB,SAAUuc,GACnCtb,IACT,GAAC,CAAAnC,IAAA,QAAAb,OAAAoe,EAAAlO,EAAAC,IAAAC,MAED,SAAAwC,IAAA,OAAAzC,IAAAsB,MAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAAA,OAAAiB,EAAAjB,KAAA,EACQuL,GAAgBna,KAAMA,KAAK2G,gBAAe,KAAA,EAAA,OAAAkJ,EAAAmC,OAAA,SACzChS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAA6P,EAAAI,OAAA,GAAAL,EAAA5P,KACZ,KAAA,WAAA,OAAAob,EAAAnO,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,WAAAb,OAAAme,EAAAjO,EAAAC,IAAAC,MAED,SAAA+C,EAAe3J,GAAY,OAAA2G,IAAAsB,MAAA,SAAA2B,GAAA,cAAAA,EAAAzB,KAAAyB,EAAAxB,MAAA,KAAA,EAAA,OAAAwB,EAAAxB,KAAA,EACnBpD,EAASxL,KAAMwG,GAAK,KAAA,EAAA,OAAA4J,EAAA4B,OAAA,SACnBhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAoQ,EAAAH,OAAA,GAAAE,EAAAnQ,KACZ,KAAA,SAAA8M,GAAA,OAAAqO,EAAAlO,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,UAAAb,MAED,SAAQwJ,GAON,OANKxG,KAAKjB,SAASyZ,QACjBxY,KAAKjB,SAASyZ,MAAQ,IAGxBxY,KAAKjB,SAASyZ,MAAM1a,KAAK0I,GAElBxG,IACT,GAAC,CAAAnC,IAAA,WAAAb,MAED,SAASwb,GACP,IAAKA,EAAMlZ,OAAQ,OAAOU,KAE1B,IAAK,IAAI2b,EAAQ,EAAGA,EAAQnD,EAAMlZ,OAAQqc,IACxC3b,KAAK4b,QAAQpD,EAAMmD,IAGrB,OAAO3b,IACT,GAAC,CAAAnC,IAAA,iBAAAb,OAAAke,EAAAhO,EAAAC,IAAAC,MAED,SAAAoD,EAAqBhK,GAAY,OAAA2G,IAAAsB,MAAA,SAAAgC,GAAA,cAAAA,EAAA9B,KAAA8B,EAAA7B,MAAA,KAAA,EAAA,OAAA6B,EAAA7B,KAAA,EACzByC,EAAerR,KAAMwG,GAAK,KAAA,EAAA,OAAAiK,EAAAuB,OAAA,SACzBhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAyQ,EAAAR,OAAA,GAAAO,EAAAxQ,KACZ,KAAA,SAAA+M,GAAA,OAAAmO,EAAAjO,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,WAAAb,OAAAie,EAAA/N,EAAAC,IAAAC,MAED,SAAAC,IAAA,OAAAF,IAAAsB,MAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,KAAA,EAAA,OAAAF,EAAAE,KAAA,EACQkB,EAAS9P,MAAK,KAAA,EAAA,OAAA0O,EAAAsD,OAAA,SACbhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAA0O,EAAAuB,OAAA,GAAA5C,EAAArN,KACZ,KAAA,WAAA,OAAAib,EAAAhO,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,eAAAb,OAAAge,EAAA9N,EAAAC,IAAAC,MAED,SAAAwJ,IAAA,OAAAzJ,IAAAsB,MAAA,SAAAuI,GAAA,cAAAA,EAAArI,KAAAqI,EAAApI,MAAA,KAAA,EAAA,OAAAoI,EAAApI,KAAA,EACQyB,GAAarQ,MAAK,KAAA,EAAA,OAAAgX,EAAAhF,OAAA,SACjBhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAgX,EAAA/G,OAAA,GAAA2G,EAAA5W,KACZ,KAAA,WAAA,OAAAgb,EAAA/N,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,cAAAb,MAED,WACE,OAAOgD,KAAK2M,YACd,GAAC,CAAA9O,IAAA,OAAAb,OAAA+d,EAAA7N,EAAAC,IAAAC,MAED,SAAAoK,EAAWgC,GAAc,OAAArM,IAAAsB,MAAA,SAAAuJ,GAAA,cAAAA,EAAArJ,KAAAqJ,EAAApJ,MAAA,KAAA,EAAA,OAAAoJ,EAAApJ,KAAA,EACjBoB,GAAUhQ,KAAMA,KAAK2G,eAAgB6S,GAAM,KAAA,EAAA,OAAAxB,EAAAhG,OAAA,SAC1ChS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAgY,EAAA/H,OAAA,GAAAuH,EAAAxX,KACZ,KAAA,SAAAsR,GAAA,OAAAyJ,EAAA9N,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,UAAAb,MAED,SAAQkc,GAEN,OADAD,GAAQjZ,KAAMkZ,GACPlZ,IACT,GAAC,CAAAnC,IAAA,mBAAAb,MAED,SAAiB8B,GAEf,OADAF,EAAiBoB,KAAMlB,GAChBkB,IACT,GAAC,CAAAnC,IAAA,iBAAAb,MAED,SAAe6e,GACb,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MACR,2DAGJ,OANE9b,KAAK8R,2BAA6B+J,EAM7B7b,IACT,GAAC,CAAAnC,IAAA,WAAAb,MAED,SAAS6e,GACP,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,sDAElB,OAJE9b,KAAK6O,qBAAuBgN,EAIvB7b,IACT,GAAC,CAAAnC,IAAA,gBAAAb,MAED,SAAc6e,GACZ,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,0DAElB,OAJE9b,KAAKmR,0BAA4B0K,EAI5B7b,IACT,GAAC,CAAAnC,IAAA,aAAAb,MAED,SAAW6e,GACT,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,wDAElB,OAJE9b,KAAK+P,uBAAyB8L,EAIzB7b,IACT,GAAC,CAAAnC,IAAA,eAAAb,MAED,SAAa6e,GACX,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,0DAElB,OAJE9b,KAAKiW,oBAAsB4F,EAItB7b,IACT,GAAC,CAAAnC,IAAA,cAAAb,MAED,SAAY6e,GACV,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,yDAElB,OAJE9b,KAAKiX,mBAAqB4E,EAIrB7b,IACT,GAAC,CAAAnC,IAAA,cAAAb,MAED,SAAY6e,GACV,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,yDAElB,OAJE9b,KAAKmU,mBAAqB0H,EAIrB7b,IACT,GAAC,CAAAnC,IAAA,UAAAb,MAED,SAAQ6e,GACN,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,qDAElB,OAJE9b,KAAKsa,oBAAsBuB,EAItB7b,IACT,GAAC,CAAAnC,IAAA,SAAAb,MAED,SAAO6e,GACL,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,oDAElB,OAJE9b,KAAK8Z,mBAAqB+B,EAIrB7b,IACT,GAAC,CAAAnC,IAAA,SAAAb,MAED,SAAO6e,GACL,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,oDAElB,OAJE9b,KAAK0Q,mBAAqBmL,EAIrB7b,IACT,GAAC,CAAAnC,IAAA,eAAAb,MAED,SAAa6e,GACX,IAAIlb,EAAWkb,GAGb,MAAM,IAAIC,MAAM,0DAElB,OAJE9b,KAAK4Z,yBAA2BiC,EAI3B7b,IACT,GAAC,CAAAnC,IAAA,WAAAb,MAAA,WAAA,IAAAmY,EAAAjI,EAAAC,IAAAC,MAED,SAAA2O,IAAA,OAAA5O,IAAAsB,MAAA,SAAAuN,GAAA,cAAAA,EAAArN,KAAAqN,EAAApN,MAAA,KAAA,EAAA,OAAAoN,EAAApN,KAAA,EACQoG,GAAchV,KAAMA,KAAK2G,gBAAe,KAAA,EAAA,OAAAqV,EAAAhK,OAAA,SACvChS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAgc,EAAA/L,OAAA,GAAA8L,EAAA/b,KACZ,KAAA,OAAA,WAAA,OAAAmV,EAAAlI,MAAAjN,KAAAX,UAAA,CAAA,CALA,IAKA,CAAAxB,IAAA,WAAAb,OAAA8d,EAAA5N,EAAAC,IAAAC,MAED,SAAA6O,EAAejI,GAAc,OAAA7G,IAAAsB,MAAA,SAAAyN,GAAA,cAAAA,EAAAvN,KAAAuN,EAAAtN,MAAA,KAAA,EAAA,OAAAsN,EAAAtN,KAAA,EACrBkF,GAAS9T,KAAMgU,GAAO,KAAA,EAAA,OAAAkI,EAAAlK,OAAA,SACrBhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAkc,EAAAjM,OAAA,GAAAgM,EAAAjc,KACZ,KAAA,SAAAuR,GAAA,OAAAuJ,EAAA7N,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,YAAAb,OAAA6d,EAAA3N,EAAAC,IAAAC,MAED,SAAA+O,IAAA,OAAAhP,IAAAsB,MAAA,SAAA2N,GAAA,cAAAA,EAAAzN,KAAAyN,EAAAxN,MAAA,KAAA,EAAA,OAAAwN,EAAAxN,KAAA,EACQwF,GAAUpU,MAAK,KAAA,EAAA,OAAAoc,EAAApK,OAAA,SACdhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAoc,EAAAnM,OAAA,GAAAkM,EAAAnc,KACZ,KAAA,WAAA,OAAA6a,EAAA5N,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,WAAAb,MAED,SAASgX,GAEP,OADAe,GAASf,GACFhU,IACT,GAAC,CAAAnC,IAAA,YAAAb,OAAA4d,EAAA1N,EAAAC,IAAAC,MAED,SAAAiP,IAAA,OAAAlP,IAAAsB,MAAA,SAAA6N,GAAA,cAAAA,EAAA3N,KAAA2N,EAAA1N,MAAA,KAAA,EAAA,OAAA0N,EAAA1N,KAAA,EACQ+F,GAAU3U,MAAK,KAAA,EAAA,OAAAsc,EAAAtK,OAAA,SACdhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAsc,EAAArM,OAAA,GAAAoM,EAAArc,KACZ,KAAA,WAAA,OAAA4a,EAAA3N,MAAAjN,KAAAX,UAAA,IAAA,CAAAxB,IAAA,cAAAb,MAED,WAEE,OTpNG,SAAqB6B,GAC1B,IAEwByC,EAF4BC,EAAAC,EAAtCmS,GAAqB,kBAEX,IAAxB,IAAApS,EAAAE,MAAAH,EAAAC,EAAAG,KAAAC,MAA0B,CAAA,IAClB6E,EADOlF,EAAAtE,MACKkE,aAAa,aAC1BsF,GAELyO,GAAWxJ,SAASjF,EAAM,IAC5B,CAAC,CAAA,MAAA1E,GAAAP,EAAApB,EAAA2B,EAAA,CAAA,QAAAP,EAAAQ,GAAA,CAEDvC,EAASqa,IAAI7b,SAAU,QAASkW,GAAmBrV,GAAO,GAC1DW,EAASqa,IAAIzZ,OAAQ,SAAUgW,GAAcvX,GAAO,GAEhDA,EAAMsX,2BACR3W,EAASqa,IACPzZ,OACA,SACAvB,EAAMsX,0BACNtX,GACA,EAGN,CS6LI0d,CAAYvc,MACLA,IACT,GAAC,CAAAnC,IAAA,aAAAb,MAED,SAAWgX,GAET,OADAiB,GAAWjB,GACJhU,IACT,GAAC,CAAAnC,IAAA,iBAAAb,OAAA2d,EAAAzN,EAAAC,IAAAC,MAED,SAAAoP,EAAqBxI,GAAc,OAAA7G,IAAAsB,MAAA,SAAAgO,GAAA,cAAAA,EAAA9N,KAAA8N,EAAA7N,MAAA,KAAA,EAAA,OAAA6N,EAAA7N,KAAA,EAC3B+G,GAAe3V,KAAMgU,GAAO,KAAA,EAAA,OAAAyI,EAAAzK,OAAA,SAC3BhS,MAAI,KAAA,EAAA,IAAA,MAAA,OAAAyc,EAAAxM,OAAA,GAAAuM,EAAAxc,KACZ,KAAA,SAAA0R,GAAA,OAAAiJ,EAAA1N,MAAAjN,KAAAX,UAAA,MAAAqb,CAAA,CA9PiB,GC3DdgC,GAAU,SAAVA,EAAWjF,GACf,IAAIkF,EAEJ,GAAyB,WAArB1J,EAAOwE,GACTkF,EAAW,IAAIjC,GAAQjD,QAClB,GAAyB,iBAAdA,EAAwB,CAExC,IAAMhV,EAAgBzE,SAASiN,cAA2BwM,GAE1D,IAAIhV,EAGF,MAAM,IAAIqZ,MAAM,4CAFhBa,EAAW,IAAIjC,GAAQjY,EAI3B,MACEka,EAAW,IAAIjC,GAAQ1c,SAASkF,MAOlC,OAFAwZ,EAAQE,UAAUzd,EAAMwd,EAAU,qBAAuBA,EAElDA,CACT,SAQAD,GAAQG,gBAQRH,GAAQE,UAAY,CAAgC"}