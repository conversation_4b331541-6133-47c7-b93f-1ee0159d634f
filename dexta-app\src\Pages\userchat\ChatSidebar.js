import React from "react";
import { Link } from "react-router-dom";
import { IoSettingsOutline } from "react-icons/io5";
import { FiDownload } from "react-icons/fi";
import Logo from "../../Dexta_assets/logodexta2.png";
import Scrollbars from "react-custom-scrollbars";
import CollapseImage from "../../Assets/collapseSidebar.png";
import { getStartOfDay } from "../../Helpers/getDayStart";

// Helper function to get the start of a day

const ChatSidebar = ({ isOpen, toggleSidebar, setSidebarOpen, chats }) => {
  const allChats = chats?.data || [];

  const today = getStartOfDay(new Date());
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);

  const todayChats = allChats.filter((chat) => {
    const chatDate = getStartOfDay(new Date(chat.createdAt));
    return chatDate.getTime() === today.getTime();
  });

  const sevenDaysChats = allChats.filter((chat) => {
    const chatDate = getStartOfDay(new Date(chat.createdAt));
    return chatDate < today && chatDate >= sevenDaysAgo;
  });

  const olderChats = allChats.filter((chat) => {
    const chatDate = getStartOfDay(new Date(chat.createdAt));
    return chatDate < sevenDaysAgo;
  });

  const renderChatList = (chatList) => {
    if (!chatList || chatList.length === 0) {
      return (
        <p className="text-xs text-gray-500" style={{ fontFamily: "Silka" }}>
          No chats in this period.
        </p>
      );
    }
    return (
      <ul className="space-y-1">
        {chatList.map((chat) => (
          <li key={chat.id}>
            <Link
              to={`/chatuser?chat_id=${chat.id}`}
              className="block text-sm text-gray-700 hover:bg-gray-100 rounded p-2 truncate"
              style={{ fontFamily: "Silka" }}
              title={chat.title}
            >
              {chat.title}
            </Link>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <>
      <div
        className={`fixed top-0 left-0 h-full bg-white border-r border-gray-200 transition-all duration-300 z-50 ${
          isOpen ? "w-80" : "w-0 -translate-x-full"
        } overflow-hidden`}
      >
        <div className="flex flex-col h-full">
          <div
            className="p-4 border-b border-gray-200 flex items-center
        justify-between"
          >
            <img
              src={Logo}
              className="h-[50px] w-[190px] object-fill"
              alt="CP Logo"
            />
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-700 hover:text-gray-900 mr-4"
            >
              <img src={CollapseImage} alt="collapse" className="h-10 w-10" />
            </button>
          </div>

          <div className="flex-1 overflow-hidden">
            <Scrollbars autoHide style={{ width: "100%", height: "100%" }}>
              {todayChats.length > 0 && (
                <div className="p-4">
                  <h3
                    className="text-sm font-medium text-gray-900 mb-2"
                    style={{ fontFamily: "Archia Semibold" }}
                  >
                    Today
                  </h3>
                  {renderChatList(todayChats)}
                </div>
              )}

              {sevenDaysChats.length > 0 && (
                <div className="p-4">
                  <h3
                    className="text-sm font-medium text-gray-900 mb-2"
                    style={{ fontFamily: "Archia Semibold" }}
                  >
                    Last 7 days
                  </h3>
                  {renderChatList(sevenDaysChats)}
                </div>
              )}

              {olderChats.length > 0 && (
                <div className="p-4">
                  <h3
                    className="text-sm font-medium text-gray-900 mb-2"
                    style={{ fontFamily: "Archia Semibold" }}
                  >
                    Older
                  </h3>
                  {renderChatList(olderChats)}
                </div>
              )}
              {allChats.length === 0 && (
                <div className="p-4">
                  <p
                    className="text-sm text-gray-500 text-center mt-4"
                    style={{ fontFamily: "Silka" }}
                  >
                    No chat history.
                  </p>
                </div>
              )}
            </Scrollbars>
          </div>

          <div className="p-4 border-t border-gray-200">
            <ul className="space-y-2">
              <li>
                <Link
                  to="/settings"
                  className="flex items-center text-sm text-gray-700 hover:bg-gray-100 rounded p-2"
                  style={{ fontFamily: "Silka" }}
                >
                  <IoSettingsOutline className="mr-2" />
                  Setting
                </Link>
              </li>
              <li>
                <Link
                  to="/download"
                  className="flex items-center text-sm text-gray-700 hover:bg-gray-100 rounded p-2"
                  style={{ fontFamily: "Silka" }}
                >
                  <FiDownload className="mr-2" />
                  Download
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChatSidebar;
