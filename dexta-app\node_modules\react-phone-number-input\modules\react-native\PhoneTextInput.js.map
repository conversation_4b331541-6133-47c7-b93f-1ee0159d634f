{"version": 3, "file": "PhoneTextInput.js", "names": ["React", "useCallback", "PropTypes", "TextInput", "PhoneTextInput", "ref", "onChange", "autoCompleteType", "TextInputComponent", "rest", "onChangeText", "value", "preventDefault", "defaultPrevented", "target", "forwardRef", "propTypes", "string", "func", "isRequired", "elementType"], "sources": ["../../source/react-native/PhoneTextInput.js"], "sourcesContent": ["import React, { useCallback } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport { TextInput } from 'react-native'\r\n\r\n/**\r\n * This is an _experimental_ React Native component.\r\n * Feedback thread: https://github.com/catamphetamine/react-phone-number-input/issues/296\r\n */\r\nfunction PhoneTextInput({\r\n  onChange,\r\n  // By default, shows phone number suggestion(s) when the user focuses the input field.\r\n  autoCompleteType = 'tel',\r\n  // By default, uses the default React Native `TextInput` component.\r\n  TextInputComponent = TextInput,\r\n  ...rest\r\n}, ref) {\r\n  // Instead of `onChangeText(value: string)` it could use\r\n  // `onChange(nativeEvent: Event)` and get `value` from `nativeEvent.text`.\r\n  const onChangeText = useCallback((value) => {\r\n    onChange({\r\n      preventDefault() { this.defaultPrevented = true },\r\n      target: { value }\r\n    })\r\n  }, [onChange])\r\n\r\n  // React Native `<TextInput/>` supports properties:\r\n  // * `placeholder: string?`\r\n  // * `autoFocus: boolean?`\r\n  // * `value: string?`\r\n  // plus the ones mentioned below:\r\n  return (\r\n    <TextInputComponent\r\n      ref={ref}\r\n      autoCompleteType={autoCompleteType}\r\n      keyboardType=\"phone-pad\"\r\n      onChangeText={onChangeText}\r\n      {...rest}\r\n    />\r\n  )\r\n}\r\n\r\nPhoneTextInput = React.forwardRef(PhoneTextInput)\r\n\r\nPhoneTextInput.propTypes = {\r\n  /**\r\n   * The input field `value: string`.\r\n   */\r\n  value: PropTypes.string,\r\n\r\n  /**\r\n   * A function of `event: Event`.\r\n   * Updates the `value: string` property.\r\n   */\r\n  onChange: PropTypes.func.isRequired,\r\n\r\n  /**\r\n   * The standard `autoCompleteType` property of a React Native `<TextInput/>`.\r\n   */\r\n  autoCompleteType: PropTypes.string,\r\n\r\n  /**\r\n   * The input field component.\r\n   */\r\n  TextInputComponent: PropTypes.elementType\r\n}\r\n\r\nexport default PhoneTextInput\r\n"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,WAAhB,QAAmC,OAAnC;AACA,OAAOC,SAAP,MAAsB,YAAtB;AACA,SAASC,SAAT,QAA0B,cAA1B;AAEA;AACA;AACA;AACA;;AACA,SAASC,cAAT,OAOGC,GAPH,EAOQ;EAAA,IANNC,QAMM,QANNA,QAMM;EAAA,iCAJNC,gBAIM;EAAA,IAJNA,gBAIM,sCAJa,KAIb;EAAA,iCAFNC,kBAEM;EAAA,IAFNA,kBAEM,sCAFeL,SAEf;EAAA,IADHM,IACG;;EACN;EACA;EACA,IAAMC,YAAY,GAAGT,WAAW,CAAC,UAACU,KAAD,EAAW;IAC1CL,QAAQ,CAAC;MACPM,cADO,4BACU;QAAE,KAAKC,gBAAL,GAAwB,IAAxB;MAA8B,CAD1C;MAEPC,MAAM,EAAE;QAAEH,KAAK,EAALA;MAAF;IAFD,CAAD,CAAR;EAID,CAL+B,EAK7B,CAACL,QAAD,CAL6B,CAAhC,CAHM,CAUN;EACA;EACA;EACA;EACA;;EACA,oBACE,oBAAC,kBAAD;IACE,GAAG,EAAED,GADP;IAEE,gBAAgB,EAAEE,gBAFpB;IAGE,YAAY,EAAC,WAHf;IAIE,YAAY,EAAEG;EAJhB,GAKMD,IALN,EADF;AASD;;AAEDL,cAAc,gBAAGJ,KAAK,CAACe,UAAN,CAAiBX,cAAjB,CAAjB;AAEAA,cAAc,CAACY,SAAf,GAA2B;EACzB;AACF;AACA;EACEL,KAAK,EAAET,SAAS,CAACe,MAJQ;;EAMzB;AACF;AACA;AACA;EACEX,QAAQ,EAAEJ,SAAS,CAACgB,IAAV,CAAeC,UAVA;;EAYzB;AACF;AACA;EACEZ,gBAAgB,EAAEL,SAAS,CAACe,MAfH;;EAiBzB;AACF;AACA;EACET,kBAAkB,EAAEN,SAAS,CAACkB;AApBL,CAA3B;AAuBA,eAAehB,cAAf"}