{"name": "@mui/system", "version": "5.14.12", "private": false, "author": "MUI Team", "description": "CSS utilities for rapidly laying out custom designs.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "system"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-system"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/system/getting-started/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "dependencies": {"@babel/runtime": "^7.23.1", "@mui/private-theming": "^5.14.12", "@mui/styled-engine": "^5.14.12", "@mui/types": "^7.2.5", "@mui/utils": "^5.14.12", "clsx": "^2.0.0", "csstype": "^3.1.2", "prop-types": "^15.8.1"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public"}, "engines": {"node": ">=12.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}