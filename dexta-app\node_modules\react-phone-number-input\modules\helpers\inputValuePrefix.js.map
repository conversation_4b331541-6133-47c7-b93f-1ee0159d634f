{"version": 3, "file": "inputValuePrefix.js", "names": ["getCountryCallingCode", "getInputValuePrefix", "country", "international", "withCountryCallingCode", "metadata", "removeInputValuePrefix", "value", "prefix", "slice", "length"], "sources": ["../../source/helpers/inputValuePrefix.js"], "sourcesContent": ["import { getCountryCallingCode } from 'libphonenumber-js/core'\r\n\r\nexport function getInputValuePrefix({\r\n\tcountry,\r\n\tinternational,\r\n\twithCountryCallingCode,\r\n\tmetadata\r\n}) {\r\n\treturn country && international && !withCountryCallingCode ?\r\n\t\t`+${getCountryCallingCode(country, metadata)}` :\r\n\t\t''\r\n}\r\n\r\nexport function removeInputValuePrefix(value, prefix) {\r\n\tif (prefix) {\r\n\t\tvalue = value.slice(prefix.length)\r\n\t\tif (value[0] === ' ') {\r\n\t\t\tvalue = value.slice(1)\r\n\t\t}\r\n\t}\r\n\treturn value\r\n}"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,wBAAtC;AAEA,OAAO,SAASC,mBAAT,OAKJ;EAAA,IAJFC,OAIE,QAJFA,OAIE;EAAA,IAHFC,aAGE,QAHFA,aAGE;EAAA,IAFFC,sBAEE,QAFFA,sBAEE;EAAA,IADFC,QACE,QADFA,QACE;EACF,OAAOH,OAAO,IAAIC,aAAX,IAA4B,CAACC,sBAA7B,cACFJ,qBAAqB,CAACE,OAAD,EAAUG,QAAV,CADnB,IAEN,EAFD;AAGA;AAED,OAAO,SAASC,sBAAT,CAAgCC,KAAhC,EAAuCC,MAAvC,EAA+C;EACrD,IAAIA,MAAJ,EAAY;IACXD,KAAK,GAAGA,KAAK,CAACE,KAAN,CAAYD,MAAM,CAACE,MAAnB,CAAR;;IACA,IAAIH,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;MACrBA,KAAK,GAAGA,KAAK,CAACE,KAAN,CAAY,CAAZ,CAAR;IACA;EACD;;EACD,OAAOF,KAAP;AACA"}