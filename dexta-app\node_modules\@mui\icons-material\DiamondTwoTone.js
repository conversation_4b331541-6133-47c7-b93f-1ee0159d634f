"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M8.88 5H6.24l-1.5 3h2.64zm10.38 3-1.5-3h-2.64l1.5 3zM11 16.68V10H5.44zm2 0L18.56 10H13zM12.88 5h-1.76l-1.5 3h4.76z",
  opacity: ".3"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 3H5L2 9l10 12L22 9l-3-6zm-1.24 2 1.5 3h-2.65l-1.5-3h2.65zM6.24 5h2.65l-1.5 3H4.74l1.5-3zM11 16.68 5.44 10H11v6.68zM9.62 8l1.5-3h1.76l1.5 3H9.62zM13 16.68V10h5.56L13 16.68z"
}, "1")], 'DiamondTwoTone');
exports.default = _default;