{"name": "@babel/helper-module-imports", "version": "7.22.5", "description": "Babel helper functions for inserting module loads", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-imports"}, "main": "./lib/index.js", "dependencies": {"@babel/types": "^7.22.5"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}