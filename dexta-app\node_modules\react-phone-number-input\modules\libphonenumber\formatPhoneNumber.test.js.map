{"version": 3, "file": "formatPhoneNumber.test.js", "names": ["_formatPhoneNumber", "formatPhoneNumberIntl", "_formatPhoneNumberIntl", "metadata", "call", "func", "_arguments", "args", "Array", "prototype", "slice", "push", "apply", "formatPhoneNumber", "arguments", "describe", "it", "expect", "to", "should", "equal", "undefined"], "sources": ["../../source/libphonenumber/formatPhoneNumber.test.js"], "sourcesContent": ["import _formatPhoneNumber, { formatPhoneNumberIntl as _formatPhoneNumberIntl } from './formatPhoneNumber.js'\r\nimport metadata from 'libphonenumber-js/min/metadata'\r\n\r\nfunction call(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}\r\n\r\nfunction formatPhoneNumber() {\r\n\treturn call(_formatPhoneNumber, arguments)\r\n}\r\n\r\nfunction formatPhoneNumberIntl() {\r\n\treturn call(_formatPhoneNumberIntl, arguments)\r\n}\r\n\r\ndescribe('formatPhoneNumber', () => {\r\n\tit('should format phone numbers', () => {\r\n\t\texpect(() => formatPhoneNumber()).to.throw('must be a string')\r\n\t\t// formatPhoneNumber().should.equal('')\r\n\t\tformatPhoneNumber(null).should.equal('')\r\n\t\tformatPhoneNumber('').should.equal('')\r\n\t\texpect(() => _formatPhoneNumber('+1', 'NATIONAL')).to.throw('`metadata` argument not passed')\r\n\t\texpect(() => _formatPhoneNumber('+12133734253', undefined, metadata)).to.throw('Unknown \"format\"')\r\n\t\texpect(() => _formatPhoneNumber('+12133734253', '123', metadata)).to.throw('Unknown \"format\"')\r\n\t\tformatPhoneNumber('+1', 'NATIONAL').should.equal('')\r\n\t\tformatPhoneNumber('+12133734253', 'NATIONAL').should.equal('(*************')\r\n\t\tformatPhoneNumber('+12133734253').should.equal('(*************')\r\n\t\tformatPhoneNumber('+12133734253', 'INTERNATIONAL').should.equal('****** 373 4253')\r\n\t\t// Deprecated.\r\n\t\t// Legacy `format`s.\r\n\t\tformatPhoneNumber('+12133734253', 'National').should.equal('(*************')\r\n\t\tformatPhoneNumber('+12133734253', 'International').should.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should format international phone numbers', () => {\r\n\t\tformatPhoneNumberIntl('+12133734253').should.equal('****** 373 4253')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,kBAAP,IAA6BC,qBAAqB,IAAIC,sBAAtD,QAAoF,wBAApF;AACA,OAAOC,QAAP,MAAqB,gCAArB;;AAEA,SAASC,IAAT,CAAcC,IAAd,EAAoBC,UAApB,EAAgC;EAC/B,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBN,IAAtB,CAA2BE,UAA3B,CAAX;EACAC,IAAI,CAACI,IAAL,CAAUR,QAAV;EACA,OAAOE,IAAI,CAACO,KAAL,CAAW,IAAX,EAAiBL,IAAjB,CAAP;AACA;;AAED,SAASM,iBAAT,GAA6B;EAC5B,OAAOT,IAAI,CAACJ,kBAAD,EAAqBc,SAArB,CAAX;AACA;;AAED,SAASb,qBAAT,GAAiC;EAChC,OAAOG,IAAI,CAACF,sBAAD,EAAyBY,SAAzB,CAAX;AACA;;AAEDC,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,6BAAD,EAAgC,YAAM;IACvCC,MAAM,CAAC;MAAA,OAAMJ,iBAAiB,EAAvB;IAAA,CAAD,CAAN,CAAkCK,EAAlC,UAA2C,kBAA3C,EADuC,CAEvC;;IACAL,iBAAiB,CAAC,IAAD,CAAjB,CAAwBM,MAAxB,CAA+BC,KAA/B,CAAqC,EAArC;IACAP,iBAAiB,CAAC,EAAD,CAAjB,CAAsBM,MAAtB,CAA6BC,KAA7B,CAAmC,EAAnC;IACAH,MAAM,CAAC;MAAA,OAAMjB,kBAAkB,CAAC,IAAD,EAAO,UAAP,CAAxB;IAAA,CAAD,CAAN,CAAmDkB,EAAnD,UAA4D,gCAA5D;IACAD,MAAM,CAAC;MAAA,OAAMjB,kBAAkB,CAAC,cAAD,EAAiBqB,SAAjB,EAA4BlB,QAA5B,CAAxB;IAAA,CAAD,CAAN,CAAsEe,EAAtE,UAA+E,kBAA/E;IACAD,MAAM,CAAC;MAAA,OAAMjB,kBAAkB,CAAC,cAAD,EAAiB,KAAjB,EAAwBG,QAAxB,CAAxB;IAAA,CAAD,CAAN,CAAkEe,EAAlE,UAA2E,kBAA3E;IACAL,iBAAiB,CAAC,IAAD,EAAO,UAAP,CAAjB,CAAoCM,MAApC,CAA2CC,KAA3C,CAAiD,EAAjD;IACAP,iBAAiB,CAAC,cAAD,EAAiB,UAAjB,CAAjB,CAA8CM,MAA9C,CAAqDC,KAArD,CAA2D,gBAA3D;IACAP,iBAAiB,CAAC,cAAD,CAAjB,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,gBAA/C;IACAP,iBAAiB,CAAC,cAAD,EAAiB,eAAjB,CAAjB,CAAmDM,MAAnD,CAA0DC,KAA1D,CAAgE,iBAAhE,EAXuC,CAYvC;IACA;;IACAP,iBAAiB,CAAC,cAAD,EAAiB,UAAjB,CAAjB,CAA8CM,MAA9C,CAAqDC,KAArD,CAA2D,gBAA3D;IACAP,iBAAiB,CAAC,cAAD,EAAiB,eAAjB,CAAjB,CAAmDM,MAAnD,CAA0DC,KAA1D,CAAgE,iBAAhE;EACA,CAhBC,CAAF;EAkBAJ,EAAE,CAAC,2CAAD,EAA8C,YAAM;IACrDf,qBAAqB,CAAC,cAAD,CAArB,CAAsCkB,MAAtC,CAA6CC,KAA7C,CAAmD,iBAAnD;EACA,CAFC,CAAF;AAGA,CAtBO,CAAR"}