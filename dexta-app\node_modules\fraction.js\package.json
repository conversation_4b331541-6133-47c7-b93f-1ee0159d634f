{"name": "fraction.js", "title": "fraction.js", "version": "4.2.0", "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": "https://github.com/infusion/Fraction.js/issues", "description": "A rational number library", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": "<PERSON> <<EMAIL>> (http://www.xarg.org/)", "main": "fraction", "types": "./fraction.d.ts", "private": false, "readmeFilename": "README.md", "directories": {"example": "examples"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/infusion/Fraction.js.git"}, "funding": {"type": "patreon", "url": "https://www.patreon.com/infusion"}, "engines": {"node": "*"}, "scripts": {"test": "mocha tests/*.js"}, "devDependencies": {"mocha": "*"}}