{"version": 3, "file": "getPhoneInputWithCountryStateUpdateFromNewProps.js", "names": ["getPhoneInputWithCountryStateUpdateFromNewProps", "props", "prevProps", "state", "metadata", "countries", "newDefaultCountry", "defaultCountry", "newValue", "value", "newReset", "reset", "international", "displayInitialValueAsLocalNumber", "initialValueFormat", "prevDefaultCountry", "prevValue", "prevReset", "country", "hasUserSelectedACountry", "_getInitialPhoneDigits", "parameters", "getInitialPhoneDigits", "useNationalFormat", "phoneDigits", "undefined", "isNewDefaultCountrySupported", "isCountrySupportedWithError", "noValueHasBeenEnteredByTheUser", "noValueHasBeenEntered", "valuesAreEqual", "phoneNumber", "parsedCountry", "parsePhoneNumber", "supportedCountries", "getSupportedCountries", "indexOf", "getCountryForPartialE164Number", "getInternationalPhoneNumberPrefix", "hasUserSelectedACountryUpdate", "value1", "value2"], "sources": ["../../source/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js"], "sourcesContent": ["import {\r\n\tgetInitialPhoneDigits,\r\n\tgetCountryForPartialE164Number,\r\n\tparsePhoneNumber\r\n} from './phoneInputHelpers.js'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\nimport {\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries\r\n} from './countries.js'\r\n\r\nexport default function getPhoneInputWithCountryStateUpdateFromNewProps(props, prevProps, state) {\r\n\tconst {\r\n\t\tmetadata,\r\n\t\tcountries,\r\n\t\tdefaultCountry: newDefaultCountry,\r\n\t\tvalue: newValue,\r\n\t\treset: newReset,\r\n\t\tinternational,\r\n\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t// superceded by `initialValueFormat` property.\r\n\t\tdisplayInitialValueAsLocalNumber,\r\n\t\tinitialValueFormat\r\n\t} = props\r\n\r\n\tconst {\r\n\t\tdefaultCountry: prevDefaultCountry,\r\n\t\tvalue: prevValue,\r\n\t\treset: prevReset\r\n\t} = prevProps\r\n\r\n\tconst {\r\n\t\tcountry,\r\n\t\tvalue,\r\n\t\t// If the user has already manually selected a country\r\n\t\t// then don't override that already selected country\r\n\t\t// if the `defaultCountry` property changes.\r\n\t\t// That's what `hasUserSelectedACountry` flag is for.\r\n\t\thasUserSelectedACountry\r\n\t} = state\r\n\r\n\tconst _getInitialPhoneDigits = (parameters) => getInitialPhoneDigits({\r\n\t\t...parameters,\r\n\t\tinternational,\r\n\t\tuseNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',\r\n\t\tmetadata\r\n\t})\r\n\r\n\t// Some users requested a way to reset the component\r\n\t// (both number `<input/>` and country `<select/>`).\r\n\t// Whenever `reset` property changes both number `<input/>`\r\n\t// and country `<select/>` are reset.\r\n\t// It's not implemented as some instance `.reset()` method\r\n\t// because `ref` is forwarded to `<input/>`.\r\n\t// It's also not replaced with just resetting `country` on\r\n\t// external `value` reset, because a user could select a country\r\n\t// and then not input any `value`, and so the selected country\r\n\t// would be \"stuck\", if not using this `reset` property.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/300\r\n\tif (newReset !== prevReset) {\r\n\t\treturn {\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: undefined,\r\n\t\t\tcountry: newDefaultCountry,\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t}\r\n\t}\r\n\r\n\t// `value` is the value currently shown in the component:\r\n\t// it's stored in the component's `state`, and it's not the `value` property.\r\n\t// `prevValue` is \"previous `value` property\".\r\n\t// `newValue` is \"new `value` property\".\r\n\r\n\t// If the default country changed\r\n\t// (e.g. in case of ajax GeoIP detection after page loaded)\r\n\t// then select it, but only if the user hasn't already manually\r\n\t// selected a country, and no phone number has been manually entered so far.\r\n\t// Because if the user has already started inputting a phone number\r\n\t// then they're okay with no country being selected at all (\"International\")\r\n\t// and they don't want to be disturbed, don't want their input to be screwed, etc.\r\n\tif (newDefaultCountry !== prevDefaultCountry) {\r\n\t\tconst isNewDefaultCountrySupported = !newDefaultCountry || isCountrySupportedWithError(newDefaultCountry, metadata)\r\n\t\tconst noValueHasBeenEnteredByTheUser = (\r\n\t\t\t// By default, \"no value has been entered\" means `value` is `undefined`.\r\n\t\t\t!value ||\r\n\t\t\t// When `international` is `true`, and some country has been pre-selected,\r\n\t\t\t// then the `<input/>` contains a pre-filled value of `+${countryCallingCode}${leadingDigits}`,\r\n\t\t\t// so in case of `international` being `true`, \"the user hasn't entered anything\" situation\r\n\t\t\t// doesn't just mean `value` is `undefined`, but could also mean `value` is `+${countryCallingCode}`.\r\n\t\t\t(international && value === _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: prevDefaultCountry\r\n\t\t\t}))\r\n\t\t)\r\n\t\t// Only update the `defaultCountry` property if no phone number\r\n\t\t// has been entered by the user or pre-set by the application.\r\n\t\tconst noValueHasBeenEntered = !newValue && noValueHasBeenEnteredByTheUser\r\n\t\tif (!hasUserSelectedACountry && isNewDefaultCountrySupported && noValueHasBeenEntered) {\r\n\t\t\treturn {\r\n\t\t\t\tcountry: newDefaultCountry,\r\n\t\t\t\t// If `phoneDigits` is empty, then automatically select the new `country`\r\n\t\t\t\t// and set `phoneDigits` to `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\t// The code assumes that \"no phone number has been entered by the user\",\r\n\t\t\t\t// and no `value` property has been passed, so the `phoneNumber` parameter\r\n\t\t\t\t// of `_getInitialPhoneDigits({ value, phoneNumber, ... })` is `undefined`.\r\n\t\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\t\tvalue: undefined,\r\n\t\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t\t}),\r\n\t\t\t\t// `value` is `undefined` and it stays so.\r\n\t\t\t\tvalue: undefined\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If a new `value` is set externally.\r\n\t// (e.g. as a result of an ajax API request\r\n\t//  to get user's phone after page loaded)\r\n\t// The first part — `newValue !== prevValue` —\r\n\t// is basically `props.value !== prevProps.value`\r\n\t// so it means \"if value property was changed externally\".\r\n\t// The second part — `newValue !== value` —\r\n\t// is for ignoring the `getDerivedStateFromProps()` call\r\n\t// which happens in `this.onChange()` right after `this.setState()`.\r\n\t// If this `getDerivedStateFromProps()` call isn't ignored\r\n\t// then the country flag would reset on each input.\r\n\tif (!valuesAreEqual(newValue, prevValue) && !valuesAreEqual(newValue, value)) {\r\n\t\tlet phoneNumber\r\n\t\tlet parsedCountry\r\n\t\tif (newValue) {\r\n\t\t\tphoneNumber = parsePhoneNumber(newValue, metadata)\r\n\t\t\tconst supportedCountries = getSupportedCountries(countries, metadata)\r\n\t\t\tif (phoneNumber && phoneNumber.country) {\r\n\t\t\t\t// Ignore `else` because all countries are supported in metadata.\r\n\t\t\t\t/* istanbul ignore next */\r\n\t\t\t\tif (!supportedCountries || supportedCountries.indexOf(phoneNumber.country) >= 0) {\r\n\t\t\t\t\tparsedCountry = phoneNumber.country\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tparsedCountry = getCountryForPartialE164Number(newValue, {\r\n\t\t\t\t\tcountry: undefined,\r\n\t\t\t\t\tcountries: supportedCountries,\r\n\t\t\t\t\tmetadata\r\n\t\t\t\t})\r\n\t\t\t\t// In cases when multiple countries correspond to the same country calling code,\r\n\t\t\t\t// the phone number digits of `newValue` have to be matched against country-specific\r\n\t\t\t\t// regular expressions in order to determine the exact country.\r\n\t\t\t\t// Sometimes, that algorithm can't decide for sure which country does the phone number belong to,\r\n\t\t\t\t// for example when the digits of `newValue` don't match any of those regular expressions.\r\n\t\t\t\t// and the country of the phone number couldn't be determined.\r\n\t\t\t\t// In those cases, people prefer the component to show the flag of the `defaultCountry`\r\n\t\t\t\t// if the phone number could potentially belong to that `defaultCountry`.\r\n\t\t\t\t// At least that's how the component behaves when a user pastes an international\r\n\t\t\t\t// phone number into the input field: for example, when `defaultCountry` is `\"US\"`\r\n\t\t\t\t// and the user pastes value \"****** 555 5555\" into the input field, it keep showing \"US\" flag.\r\n\t\t\t\t// So when setting new `value` property externally, the component should behave the same way:\r\n\t\t\t\t// it should select the `defaultCountry` when the new `value` could potentially belong\r\n\t\t\t\t// to that country in cases when the exact country can't be determined.\r\n\t\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/413#issuecomment-1536219404\r\n\t\t\t\tif (!parsedCountry) {\r\n\t\t\t\t\tif (newDefaultCountry) {\r\n\t\t\t\t\t\tif (newValue.indexOf(getInternationalPhoneNumberPrefix(newDefaultCountry, metadata)) === 0) {\r\n\t\t\t\t\t\t\tparsedCountry = newDefaultCountry\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tlet hasUserSelectedACountryUpdate\r\n\t\tif (!newValue) {\r\n\t\t\t// Reset `hasUserSelectedACountry` flag in `state`.\r\n\t\t\thasUserSelectedACountryUpdate = {\r\n\t\t\t\thasUserSelectedACountry: undefined\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\t...hasUserSelectedACountryUpdate,\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tphoneNumber,\r\n\t\t\t\tvalue: newValue,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: newValue,\r\n\t\t\tcountry: newValue ? parsedCountry : newDefaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// `defaultCountry` didn't change.\r\n\t// `value` didn't change.\r\n\t// `phoneDigits` didn't change, because `value` didn't change.\r\n\t//\r\n\t// So no need to update state.\r\n}\r\n\r\nfunction valuesAreEqual(value1, value2) {\r\n\t// If `value` has been set to `null` externally then convert it to `undefined`.\r\n\t//\r\n\t// For example, `react-hook-form` sets `value` to `null` when the user clears the input.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/164\r\n\t// In that case, without this conversion of `null` to `undefined`, it would reset\r\n\t// the selected country to `defaultCountry` because in that case `newValue !== value`\r\n\t// because `null !== undefined`.\r\n\t//\r\n\t// Historically, empty `value` is encoded as `undefined`.\r\n\t// Perhaps empty `value` would be better encoded as `null` instead.\r\n\t// But because that would be a potentially breaking change for some people,\r\n\t// it's left as is for the current \"major\" version of this library.\r\n\t//\r\n\tif (value1 === null) {\r\n\t\tvalue1 = undefined\r\n\t}\r\n\tif (value2 === null) {\r\n\t\tvalue2 = undefined\r\n\t}\r\n\treturn value1 === value2\r\n}"], "mappings": ";;;;;;;AAAA;;AAMA;;AAEA;;;;;;;;;;AAKe,SAASA,+CAAT,CAAyDC,KAAzD,EAAgEC,SAAhE,EAA2EC,KAA3E,EAAkF;EAChG,IACCC,QADD,GAWIH,KAXJ,CACCG,QADD;EAAA,IAECC,SAFD,GAWIJ,KAXJ,CAECI,SAFD;EAAA,IAGiBC,iBAHjB,GAWIL,KAXJ,CAGCM,cAHD;EAAA,IAIQC,QAJR,GAWIP,KAXJ,CAICQ,KAJD;EAAA,IAKQC,QALR,GAWIT,KAXJ,CAKCU,KALD;EAAA,IAMCC,aAND,GAWIX,KAXJ,CAMCW,aAND;EAAA,IASCC,gCATD,GAWIZ,KAXJ,CASCY,gCATD;EAAA,IAUCC,kBAVD,GAWIb,KAXJ,CAUCa,kBAVD;EAaA,IACiBC,kBADjB,GAIIb,SAJJ,CACCK,cADD;EAAA,IAEQS,SAFR,GAIId,SAJJ,CAECO,KAFD;EAAA,IAGQQ,SAHR,GAIIf,SAJJ,CAGCS,KAHD;EAMA,IACCO,OADD,GAQIf,KARJ,CACCe,OADD;EAAA,IAECT,KAFD,GAQIN,KARJ,CAECM,KAFD;EAAA,IAOCU,uBAPD,GAQIhB,KARJ,CAOCgB,uBAPD;;EAUA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAyB,CAACC,UAAD;IAAA,OAAgB,IAAAC,wCAAA,kCAC3CD,UAD2C;MAE9CT,aAAa,EAAbA,aAF8C;MAG9CW,iBAAiB,EAAEV,gCAAgC,IAAIC,kBAAkB,KAAK,UAHhC;MAI9CV,QAAQ,EAARA;IAJ8C,GAAhB;EAAA,CAA/B,CA9BgG,CAqChG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAIM,QAAQ,KAAKO,SAAjB,EAA4B;IAC3B,OAAO;MACNO,WAAW,EAAEJ,sBAAsB,CAAC;QACnCX,KAAK,EAAEgB,SAD4B;QAEnClB,cAAc,EAAED;MAFmB,CAAD,CAD7B;MAKNG,KAAK,EAAEgB,SALD;MAMNP,OAAO,EAAEZ,iBANH;MAONa,uBAAuB,EAAEM;IAPnB,CAAP;EASA,CA1D+F,CA4DhG;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAInB,iBAAiB,KAAKS,kBAA1B,EAA8C;IAC7C,IAAMW,4BAA4B,GAAG,CAACpB,iBAAD,IAAsB,IAAAqB,sCAAA,EAA4BrB,iBAA5B,EAA+CF,QAA/C,CAA3D;;IACA,IAAMwB,8BAA8B,GACnC;IACA,CAACnB,KAAD,IACA;IACA;IACA;IACA;IACCG,aAAa,IAAIH,KAAK,KAAKW,sBAAsB,CAAC;MAClDX,KAAK,EAAEgB,SAD2C;MAElDlB,cAAc,EAAEQ;IAFkC,CAAD,CAPnD,CAF6C,CAc7C;IACA;;;IACA,IAAMc,qBAAqB,GAAG,CAACrB,QAAD,IAAaoB,8BAA3C;;IACA,IAAI,CAACT,uBAAD,IAA4BO,4BAA5B,IAA4DG,qBAAhE,EAAuF;MACtF,OAAO;QACNX,OAAO,EAAEZ,iBADH;QAEN;QACA;QACA;QACA;QACA;QACAkB,WAAW,EAAEJ,sBAAsB,CAAC;UACnCX,KAAK,EAAEgB,SAD4B;UAEnClB,cAAc,EAAED;QAFmB,CAAD,CAP7B;QAWN;QACAG,KAAK,EAAEgB;MAZD,CAAP;IAcA;EACD,CAzG+F,CA2GhG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAI,CAACK,cAAc,CAACtB,QAAD,EAAWQ,SAAX,CAAf,IAAwC,CAACc,cAAc,CAACtB,QAAD,EAAWC,KAAX,CAA3D,EAA8E;IAC7E,IAAIsB,WAAJ;IACA,IAAIC,aAAJ;;IACA,IAAIxB,QAAJ,EAAc;MACbuB,WAAW,GAAG,IAAAE,mCAAA,EAAiBzB,QAAjB,EAA2BJ,QAA3B,CAAd;MACA,IAAM8B,kBAAkB,GAAG,IAAAC,gCAAA,EAAsB9B,SAAtB,EAAiCD,QAAjC,CAA3B;;MACA,IAAI2B,WAAW,IAAIA,WAAW,CAACb,OAA/B,EAAwC;QACvC;;QACA;QACA,IAAI,CAACgB,kBAAD,IAAuBA,kBAAkB,CAACE,OAAnB,CAA2BL,WAAW,CAACb,OAAvC,KAAmD,CAA9E,EAAiF;UAChFc,aAAa,GAAGD,WAAW,CAACb,OAA5B;QACA;MACD,CAND,MAMO;QACNc,aAAa,GAAG,IAAAK,iDAAA,EAA+B7B,QAA/B,EAAyC;UACxDU,OAAO,EAAEO,SAD+C;UAExDpB,SAAS,EAAE6B,kBAF6C;UAGxD9B,QAAQ,EAARA;QAHwD,CAAzC,CAAhB,CADM,CAMN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,IAAI,CAAC4B,aAAL,EAAoB;UACnB,IAAI1B,iBAAJ,EAAuB;YACtB,IAAIE,QAAQ,CAAC4B,OAAT,CAAiB,IAAAE,6CAAA,EAAkChC,iBAAlC,EAAqDF,QAArD,CAAjB,MAAqF,CAAzF,EAA4F;cAC3F4B,aAAa,GAAG1B,iBAAhB;YACA;UACD;QACD;MACD;IACD;;IACD,IAAIiC,6BAAJ;;IACA,IAAI,CAAC/B,QAAL,EAAe;MACd;MACA+B,6BAA6B,GAAG;QAC/BpB,uBAAuB,EAAEM;MADM,CAAhC;IAGA;;IACD,uCACIc,6BADJ;MAECf,WAAW,EAAEJ,sBAAsB,CAAC;QACnCW,WAAW,EAAXA,WADmC;QAEnCtB,KAAK,EAAED,QAF4B;QAGnCD,cAAc,EAAED;MAHmB,CAAD,CAFpC;MAOCG,KAAK,EAAED,QAPR;MAQCU,OAAO,EAAEV,QAAQ,GAAGwB,aAAH,GAAmB1B;IARrC;EAUA,CAjL+F,CAmLhG;EACA;EACA;EACA;EACA;;AACA;;AAED,SAASwB,cAAT,CAAwBU,MAAxB,EAAgCC,MAAhC,EAAwC;EACvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,MAAM,KAAK,IAAf,EAAqB;IACpBA,MAAM,GAAGf,SAAT;EACA;;EACD,IAAIgB,MAAM,KAAK,IAAf,EAAqB;IACpBA,MAAM,GAAGhB,SAAT;EACA;;EACD,OAAOe,MAAM,KAAKC,MAAlB;AACA"}