{"name": "@emotion/styled", "version": "11.10.4", "description": "styled API for emotion", "main": "dist/emotion-styled.cjs.js", "module": "dist/emotion-styled.esm.js", "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/styled", "scripts": {"test:typescript": "dtslint types"}, "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.10.0", "@emotion/is-prop-valid": "^1.2.0", "@emotion/serialize": "^1.1.0", "@emotion/use-insertion-effect-with-fallbacks": "^1.0.0", "@emotion/utils": "^1.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0", "@emotion/react": "^11.0.0-rc.0", "react": ">=16.8.0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@types/react": {"optional": true}}, "devDependencies": {"@babel/core": "^7.18.5", "@definitelytyped/dtslint": "0.0.112", "@emotion/react": "11.10.4", "react": "16.14.0", "typescript": "^4.5.5"}, "publishConfig": {"access": "public"}, "files": ["src", "dist", "base", "types/*.d.ts", "macro.d.ts", "macro.js", "macro.js.flow"], "umd:main": "dist/emotion-styled.umd.min.js", "browser": {"./dist/emotion-styled.esm.js": "./dist/emotion-styled.browser.esm.js"}, "exports": {"./base": {"module": {"worker": "./base/dist/emotion-styled-base.worker.esm.js", "browser": "./base/dist/emotion-styled-base.browser.esm.js", "default": "./base/dist/emotion-styled-base.esm.js"}, "default": "./base/dist/emotion-styled-base.cjs.js"}, ".": {"module": {"worker": "./dist/emotion-styled.worker.esm.js", "browser": "./dist/emotion-styled.browser.esm.js", "default": "./dist/emotion-styled.esm.js"}, "default": "./dist/emotion-styled.cjs.js"}, "./package.json": "./package.json", "./macro": "./macro.js"}, "preconstruct": {"umdName": "emotionStyled", "entrypoints": ["./index.js", "./base.js"], "exports": {"envConditions": ["browser", "worker"], "extra": {"./macro": "./macro.js"}}}}