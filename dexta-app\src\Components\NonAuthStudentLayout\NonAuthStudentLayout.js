import React from "react";
import PropTypes from "prop-types";
import withRouter from "../Common/withRouter";
import { Navigate, useLocation } from "react-router-dom";

const NonAuthStudentLayout = (props) => {
  const location = useLocation();
  const currentScreen = localStorage.getItem("Current_screen");

  if (currentScreen === "Login") {
    if (
      location.pathname === "/invalid" ||
      location.pathname === "/expired" ||
      location.pathname === "/access-denied" ||
      location.pathname === "/error"
    ) {
      return <Navigate to={{ pathname: "/candidate/login" }} />;
    }
  }
  return <React.Fragment>{props.children}</React.Fragment>;
};

NonAuthStudentLayout.propTypes = {
  children: PropTypes.any,
  location: PropTypes.object,
};

export default withRouter(NonAuthStudentLayout);
