{"version": 3, "file": "getInternationalPhoneNumberPrefix.js", "names": ["getCountryCallingCode", "<PERSON><PERSON><PERSON>", "ONLY_DIGITS_REGEXP", "getInternationalPhoneNumberPrefix", "country", "metadata", "prefix"], "sources": ["../../source/helpers/getInternationalPhoneNumberPrefix.js"], "sourcesContent": ["import {\r\n\tgetCountryCallingCode,\r\n\tMetadata\r\n} from 'libphonenumber-js/core'\r\n\r\nconst ONLY_DIGITS_REGEXP = /^\\d+$/\r\n\r\nexport default function getInternationalPhoneNumberPrefix(country, metadata) {\r\n\t// Standard international phone number prefix: \"+\" and \"country calling code\".\r\n\tlet prefix = '+' + getCountryCallingCode(country, metadata)\r\n\r\n\t// \"Leading digits\" can't be used to rule out any countries.\r\n\t// So the \"pre-fill with leading digits on country selection\" feature had to be reverted.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10#note_1231042367\r\n\t// // Get \"leading digits\" for a phone number of the country.\r\n\t// // If there're \"leading digits\" then they can be part of the prefix too.\r\n\t// // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10\r\n\t// metadata = new Metadata(metadata)\r\n\t// metadata.selectNumberingPlan(country)\r\n\t// // \"Leading digits\" patterns are only defined for about 20% of all countries.\r\n\t// // By definition, matching \"leading digits\" is a sufficient but not a necessary\r\n\t// // condition for a phone number to belong to a country.\r\n\t// // The point of \"leading digits\" check is that it's the fastest one to get a match.\r\n\t// // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\r\n\t// const leadingDigits = metadata.numberingPlan.leadingDigits()\r\n\t// if (leadingDigits && ONLY_DIGITS_REGEXP.test(leadingDigits)) {\r\n\t// \tprefix += leadingDigits\r\n\t// }\r\n\r\n\treturn prefix\r\n}"], "mappings": "AAAA,SACCA,qBADD,EAECC,QAFD,QAGO,wBAHP;AAKA,IAAMC,kBAAkB,GAAG,OAA3B;AAEA,eAAe,SAASC,iCAAT,CAA2CC,OAA3C,EAAoDC,QAApD,EAA8D;EAC5E;EACA,IAAIC,MAAM,GAAG,MAAMN,qBAAqB,CAACI,OAAD,EAAUC,QAAV,CAAxC,CAF4E,CAI5E;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,OAAOC,MAAP;AACA"}