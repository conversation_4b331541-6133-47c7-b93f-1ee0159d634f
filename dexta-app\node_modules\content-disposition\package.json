{"name": "content-disposition", "description": "Create and parse Content-Disposition header", "version": "0.5.4", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["content-disposition", "http", "rfc6266", "res"], "repository": "jshttp/content-disposition", "dependencies": {"safe-buffer": "5.2.1"}, "devDependencies": {"deep-equal": "1.0.1", "eslint": "7.32.0", "eslint-config-standard": "13.0.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "istanbul": "0.4.5", "mocha": "9.1.3"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}}