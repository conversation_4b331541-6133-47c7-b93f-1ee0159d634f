export const filters_tests = [
  {
    id: "category",
    name: "Experience Level",
    options: [
      {},
      { id: 1, value: "<PERSON>ginner", label: "Entry Level" },
      { id: 2, value: "Intermediate", label: "Mid Level" },
      { id: 3, value: "Advanced", label: "Senior Level" },
      { id: 3, value: "All", label: "All Levels" },
    ],
  },
  {
    id: "industry",
    name: "Industry",
    options: [],
  },
  {
    id: "department",
    name: "Department",
    options: [],
  },
  {
    id: "job_role",
    name: "Job Role",
    options: [],
  },
];

export const test_duration = [
  { id: 1, value1: 0, value2: 10, label: "Up to 10 mins" },
  { id: 2, value1: 11, value2: 20, label: "11 - 20 mins" },
  { id: 3, value1: 21, value2: 30, label: "21-30 mins" },
  { id: 4, value1: 31, value2: 60, label: "31-60 mins" },
];

export const getStepsTests = (t) => [
  {
    title: t("testsSteps.modulesTitle"),
    element: "#tests2",
    intro: (
      <div>
        <p>{t("testsSteps.modulesIntro")}</p>
        <p style={{ marginTop: "8px" }}>{t("testsSteps.modulesIntro2")}</p>
        <p>{t("testsSteps.modulesIntro3")}</p>
        <ul className="list-disc px-4">
          <li>{t("testsSteps.modulesList1")}</li>
          <li>{t("testsSteps.modulesList2")}</li>
        </ul>
      </div>
    ),
    position: "right",
  },
  {
    title: t("testsSteps.filterModulesTitle"),
    element: "#tests3",
    intro: (
      <div>
        <p>{t("testsSteps.filterModulesIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("testsSteps.filterModulesList1")}</li>
          <li>{t("testsSteps.filterModulesList2")}</li>
          <li>{t("testsSteps.filterModulesList3")}</li>
          <li>{t("testsSteps.filterModulesList4")}</li>
        </ul>
      </div>
    ),
  },
  {
    intro: (
      <div>
        <p>
          {t("testsSteps.tourComplete1")}
          <a
            href="https://dexta.io/contact"
            target="_blank"
            className="font-bold cursor-pointer underline"
          >
            {t("testsSteps.tourCompleteLink")}
          </a>
        </p>
      </div>
    ),
  },
];

export const getStepsTestsV2 = (t) => [
  {
    title: t("testsSteps.modulesTitle"),
    element: "#tests2",
    intro: (
      <div>
        <p>{t("testsSteps.modulesIntro")}</p>
        <p style={{ marginTop: "8px" }}>{t("testsSteps.modulesIntro2")}</p>
        <p>{t("testsSteps.modulesIntro3")}</p>
        <ul className="list-disc px-4">
          <li>{t("testsSteps.modulesList1")}</li>
          <li>{t("testsSteps.modulesList2")}</li>
        </ul>
      </div>
    ),
    position: "right",
  },
  {
    title: t("testsSteps.filterModulesTitle"),
    element: "#tests3",
    intro: (
      <div>
        <p>{t("testsSteps.filterModulesIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("testsSteps.filterModulesList1")}</li>
          <li>{t("testsSteps.filterModulesList2")}</li>
          <li>{t("testsSteps.filterModulesList3")}</li>
          <li>{t("testsSteps.filterModulesList4")}</li>
        </ul>
      </div>
    ),
  },
];
