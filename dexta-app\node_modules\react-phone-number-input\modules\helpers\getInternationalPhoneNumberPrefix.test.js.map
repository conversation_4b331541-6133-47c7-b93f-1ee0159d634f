{"version": 3, "file": "getInternationalPhoneNumberPrefix.test.js", "names": ["metadata", "getInternationalPhoneNumberPrefix", "describe", "it", "should", "equal"], "sources": ["../../source/helpers/getInternationalPhoneNumberPrefix.test.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\ndescribe('getInternationalPhoneNumberPrefix', () => {\r\n\tit('should prepend leading digits when generating international phone number prefix', () => {\r\n\t\t// No leading digits.\r\n\t\tgetInternationalPhoneNumberPrefix('RU', metadata).should.equal('+7')\r\n\r\n\t\t// The \"pre-fill with leading digits on country selection\" feature had to be reverted.\r\n\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10#note_1231042367\r\n\t\t// // Has \"fixed\" leading digits.\r\n\t\t// // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10\r\n\t\t// getInternationalPhoneNumberPrefix('AS', metadata).should.equal('+1684')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gCAArB;AAEA,OAAOC,iCAAP,MAA8C,wCAA9C;AAEAC,QAAQ,CAAC,mCAAD,EAAsC,YAAM;EACnDC,EAAE,CAAC,iFAAD,EAAoF,YAAM;IAC3F;IACAF,iCAAiC,CAAC,IAAD,EAAOD,QAAP,CAAjC,CAAkDI,MAAlD,CAAyDC,KAAzD,CAA+D,IAA/D,EAF2F,CAI3F;IACA;IACA;IACA;IACA;EACA,CATC,CAAF;AAUA,CAXO,CAAR"}