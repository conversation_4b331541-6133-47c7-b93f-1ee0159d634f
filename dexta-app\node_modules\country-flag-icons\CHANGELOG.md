1.5.0 / 21.05.2022
==================

* Migrated to "ES Module" exports.

1.4.14 / 10.11.2021
==================

* Added [TypeScript "typings"](https://gitlab.com/catamphetamine/country-flag-icons/-/issues/6).

1.4.0 / 12.07.2021
==================

* Added stringified exports of `*.svg` flags, as [suggested](https://gitlab.com/catamphetamine/country-flag-icons/-/merge_requests/3) by Neek Sandhu.

1.3.0 / 09.07.2021
==================

* Added [SVGO](https://github.com/svg/svgo) optimization of the `*.svg` files, as [suggested](https://gitlab.com/catamphetamine/country-flag-icons/-/issues/12) by Neek Sandhu.

1.2.8 / 22.12.2020
==================

* Fixed South Korean flag.

1.2.3 / 08.08.2020
==================

* [Added "named exports"](https://gitlab.com/catamphetamine/country-flag-icons/-/issues/3) in React exports.

* Added `sideEffects` property to `package.json` ("tree shaking").

1.2.2 / 29.07.2020
==================

* Added [French Southern Territories (`TF`) flag](https://gitlab.com/catamphetamine/country-flag-icons/-/issues/2)

1.2.0 / 15.03.2020
==================

* Added `1:1` crops. `3:2` to `1:1` flag icons transform offsets submitted by [`@mindplay-dk`](https://github.com/mindplay-dk).

1.1.0 / 25.02.2020
==================

* Merged the CSS flag icons feature [submitted](https://github.com/catamphetamine/country-flag-icons/pull/4) by [`@mindplay-dk`](https://github.com/mindplay-dk).