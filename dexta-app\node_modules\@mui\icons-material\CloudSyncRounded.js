"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = (0, _createSvgIcon.default)( /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M24 17.48c0 1.38-1.12 2.5-2.5 2.5L15 20c-1.66 0-3-1.34-3-3 0-1.6 1.26-2.9 2.84-2.98C15.4 12.83 16.6 12 18 12c1.76 0 3.2 1.3 3.45 2.99.02 0 .03-.01.05-.01 1.38 0 2.5 1.12 2.5 2.5zM10 15c0-.55-.45-1-1-1s-1 .45-1 1v1.44c-1.22-1.1-2-2.67-2-4.44 0-2.38 1.39-4.43 3.4-5.4.37-.18.6-.56.6-.97 0-.71-.73-1.18-1.37-.88C5.89 6.03 4 8.79 4 12c0 2.4 1.06 4.54 2.73 6H5c-.55 0-1 .45-1 1s.45 1 1 1h4c.55 0 1-.45 1-1v-4zm9-9c.55 0 1-.45 1-1s-.45-1-1-1h-4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1s1-.45 1-1V7.56c.98.89 1.68 2.08 1.92 3.44h2.02c-.25-1.99-1.23-3.74-2.66-5H19z"
}), 'CloudSyncRounded');
exports.default = _default;