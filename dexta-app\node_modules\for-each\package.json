{"name": "for-each", "version": "0.3.3", "description": "A better for<PERSON>ach", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/for-each.git", "main": "index", "homepage": "https://github.com/Raynos/for-each", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "dependencies": {"is-callable": "^1.1.3"}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "eslint": "^4.19.1", "nsp": "^3.2.1", "tape": "^4.9.0"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/for-each/raw/master/LICENSE"}], "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node test/test", "posttest": "npm run security", "lint": "eslint *.js test/*.js", "security": "nsp check"}, "testling": {"files": "test/test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}}