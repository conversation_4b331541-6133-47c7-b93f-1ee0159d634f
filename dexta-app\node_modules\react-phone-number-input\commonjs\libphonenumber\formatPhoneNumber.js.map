{"version": 3, "file": "formatPhoneNumber.js", "names": ["formatPhoneNumber", "value", "format", "metadata", "phoneNumber", "parsePhoneNumber", "formatPhoneNumberIntl"], "sources": ["../../source/libphonenumber/formatPhoneNumber.js"], "sourcesContent": ["import parsePhoneNumber from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Formats a phone number.\r\n * Is a proxy for `libphonenumber-js`'s `.format()` function of a parsed `PhoneNumber`.\r\n * @param  {string} value\r\n * @param  {string} [format]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatPhoneNumber(value, format, metadata) {\r\n\tif (!metadata) {\r\n\t\tif (typeof format === 'object') {\r\n\t\t\tmetadata = format\r\n\t\t\tformat = 'NATIONAL'\r\n\t\t}\r\n\t}\r\n\tif (!value) {\r\n\t\treturn ''\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(value, metadata)\r\n\tif (!phoneNumber) {\r\n\t\treturn ''\r\n\t}\r\n\t// Deprecated.\r\n\t// Legacy `format`s.\r\n\tswitch (format) {\r\n\t\tcase 'National':\r\n\t\t\tformat = 'NATIONAL'\r\n\t\t\tbreak\r\n\t\tcase 'International':\r\n\t\t\tformat = 'INTERNATIONAL'\r\n\t\t\tbreak\r\n\t}\r\n\treturn phoneNumber.format(format)\r\n}\r\n\r\nexport function formatPhoneNumberIntl(value, metadata) {\r\n\treturn formatPhoneNumber(value, 'INTERNATIONAL', metadata)\r\n}"], "mappings": ";;;;;;;;AAAA;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,iBAAT,CAA2BC,KAA3B,EAAkCC,MAAlC,EAA0CC,QAA1C,EAAoD;EAClE,IAAI,CAACA,QAAL,EAAe;IACd,IAAI,QAAOD,MAAP,MAAkB,QAAtB,EAAgC;MAC/BC,QAAQ,GAAGD,MAAX;MACAA,MAAM,GAAG,UAAT;IACA;EACD;;EACD,IAAI,CAACD,KAAL,EAAY;IACX,OAAO,EAAP;EACA;;EACD,IAAMG,WAAW,GAAG,IAAAC,gBAAA,EAAiBJ,KAAjB,EAAwBE,QAAxB,CAApB;;EACA,IAAI,CAACC,WAAL,EAAkB;IACjB,OAAO,EAAP;EACA,CAbiE,CAclE;EACA;;;EACA,QAAQF,MAAR;IACC,KAAK,UAAL;MACCA,MAAM,GAAG,UAAT;MACA;;IACD,KAAK,eAAL;MACCA,MAAM,GAAG,eAAT;MACA;EANF;;EAQA,OAAOE,WAAW,CAACF,MAAZ,CAAmBA,MAAnB,CAAP;AACA;;AAEM,SAASI,qBAAT,CAA+BL,KAA/B,EAAsCE,QAAtC,EAAgD;EACtD,OAAOH,iBAAiB,CAACC,KAAD,EAAQ,eAAR,EAAyBE,QAAzB,CAAxB;AACA"}