{"version": 3, "file": "getPhoneInputWithCountryStateUpdateFromNewProps.js", "names": ["getInitialPhoneDigits", "getCountryForPartialE164Number", "parsePhoneNumber", "getInternationalPhoneNumberPrefix", "isCountrySupportedWithError", "getSupportedCountries", "getPhoneInputWithCountryStateUpdateFromNewProps", "props", "prevProps", "state", "metadata", "countries", "newDefaultCountry", "defaultCountry", "newValue", "value", "newReset", "reset", "international", "displayInitialValueAsLocalNumber", "initialValueFormat", "prevDefaultCountry", "prevValue", "prevReset", "country", "hasUserSelectedACountry", "_getInitialPhoneDigits", "parameters", "useNationalFormat", "phoneDigits", "undefined", "isNewDefaultCountrySupported", "noValueHasBeenEnteredByTheUser", "noValueHasBeenEntered", "valuesAreEqual", "phoneNumber", "parsedCountry", "supportedCountries", "indexOf", "hasUserSelectedACountryUpdate", "value1", "value2"], "sources": ["../../source/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js"], "sourcesContent": ["import {\r\n\tgetInitialPhoneDigits,\r\n\tgetCountryForPartialE164Number,\r\n\tparsePhoneNumber\r\n} from './phoneInputHelpers.js'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\nimport {\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries\r\n} from './countries.js'\r\n\r\nexport default function getPhoneInputWithCountryStateUpdateFromNewProps(props, prevProps, state) {\r\n\tconst {\r\n\t\tmetadata,\r\n\t\tcountries,\r\n\t\tdefaultCountry: newDefaultCountry,\r\n\t\tvalue: newValue,\r\n\t\treset: newReset,\r\n\t\tinternational,\r\n\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t// superceded by `initialValueFormat` property.\r\n\t\tdisplayInitialValueAsLocalNumber,\r\n\t\tinitialValueFormat\r\n\t} = props\r\n\r\n\tconst {\r\n\t\tdefaultCountry: prevDefaultCountry,\r\n\t\tvalue: prevValue,\r\n\t\treset: prevReset\r\n\t} = prevProps\r\n\r\n\tconst {\r\n\t\tcountry,\r\n\t\tvalue,\r\n\t\t// If the user has already manually selected a country\r\n\t\t// then don't override that already selected country\r\n\t\t// if the `defaultCountry` property changes.\r\n\t\t// That's what `hasUserSelectedACountry` flag is for.\r\n\t\thasUserSelectedACountry\r\n\t} = state\r\n\r\n\tconst _getInitialPhoneDigits = (parameters) => getInitialPhoneDigits({\r\n\t\t...parameters,\r\n\t\tinternational,\r\n\t\tuseNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',\r\n\t\tmetadata\r\n\t})\r\n\r\n\t// Some users requested a way to reset the component\r\n\t// (both number `<input/>` and country `<select/>`).\r\n\t// Whenever `reset` property changes both number `<input/>`\r\n\t// and country `<select/>` are reset.\r\n\t// It's not implemented as some instance `.reset()` method\r\n\t// because `ref` is forwarded to `<input/>`.\r\n\t// It's also not replaced with just resetting `country` on\r\n\t// external `value` reset, because a user could select a country\r\n\t// and then not input any `value`, and so the selected country\r\n\t// would be \"stuck\", if not using this `reset` property.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/300\r\n\tif (newReset !== prevReset) {\r\n\t\treturn {\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: undefined,\r\n\t\t\tcountry: newDefaultCountry,\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t}\r\n\t}\r\n\r\n\t// `value` is the value currently shown in the component:\r\n\t// it's stored in the component's `state`, and it's not the `value` property.\r\n\t// `prevValue` is \"previous `value` property\".\r\n\t// `newValue` is \"new `value` property\".\r\n\r\n\t// If the default country changed\r\n\t// (e.g. in case of ajax GeoIP detection after page loaded)\r\n\t// then select it, but only if the user hasn't already manually\r\n\t// selected a country, and no phone number has been manually entered so far.\r\n\t// Because if the user has already started inputting a phone number\r\n\t// then they're okay with no country being selected at all (\"International\")\r\n\t// and they don't want to be disturbed, don't want their input to be screwed, etc.\r\n\tif (newDefaultCountry !== prevDefaultCountry) {\r\n\t\tconst isNewDefaultCountrySupported = !newDefaultCountry || isCountrySupportedWithError(newDefaultCountry, metadata)\r\n\t\tconst noValueHasBeenEnteredByTheUser = (\r\n\t\t\t// By default, \"no value has been entered\" means `value` is `undefined`.\r\n\t\t\t!value ||\r\n\t\t\t// When `international` is `true`, and some country has been pre-selected,\r\n\t\t\t// then the `<input/>` contains a pre-filled value of `+${countryCallingCode}${leadingDigits}`,\r\n\t\t\t// so in case of `international` being `true`, \"the user hasn't entered anything\" situation\r\n\t\t\t// doesn't just mean `value` is `undefined`, but could also mean `value` is `+${countryCallingCode}`.\r\n\t\t\t(international && value === _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: prevDefaultCountry\r\n\t\t\t}))\r\n\t\t)\r\n\t\t// Only update the `defaultCountry` property if no phone number\r\n\t\t// has been entered by the user or pre-set by the application.\r\n\t\tconst noValueHasBeenEntered = !newValue && noValueHasBeenEnteredByTheUser\r\n\t\tif (!hasUserSelectedACountry && isNewDefaultCountrySupported && noValueHasBeenEntered) {\r\n\t\t\treturn {\r\n\t\t\t\tcountry: newDefaultCountry,\r\n\t\t\t\t// If `phoneDigits` is empty, then automatically select the new `country`\r\n\t\t\t\t// and set `phoneDigits` to `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\t// The code assumes that \"no phone number has been entered by the user\",\r\n\t\t\t\t// and no `value` property has been passed, so the `phoneNumber` parameter\r\n\t\t\t\t// of `_getInitialPhoneDigits({ value, phoneNumber, ... })` is `undefined`.\r\n\t\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\t\tvalue: undefined,\r\n\t\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t\t}),\r\n\t\t\t\t// `value` is `undefined` and it stays so.\r\n\t\t\t\tvalue: undefined\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If a new `value` is set externally.\r\n\t// (e.g. as a result of an ajax API request\r\n\t//  to get user's phone after page loaded)\r\n\t// The first part — `newValue !== prevValue` —\r\n\t// is basically `props.value !== prevProps.value`\r\n\t// so it means \"if value property was changed externally\".\r\n\t// The second part — `newValue !== value` —\r\n\t// is for ignoring the `getDerivedStateFromProps()` call\r\n\t// which happens in `this.onChange()` right after `this.setState()`.\r\n\t// If this `getDerivedStateFromProps()` call isn't ignored\r\n\t// then the country flag would reset on each input.\r\n\tif (!valuesAreEqual(newValue, prevValue) && !valuesAreEqual(newValue, value)) {\r\n\t\tlet phoneNumber\r\n\t\tlet parsedCountry\r\n\t\tif (newValue) {\r\n\t\t\tphoneNumber = parsePhoneNumber(newValue, metadata)\r\n\t\t\tconst supportedCountries = getSupportedCountries(countries, metadata)\r\n\t\t\tif (phoneNumber && phoneNumber.country) {\r\n\t\t\t\t// Ignore `else` because all countries are supported in metadata.\r\n\t\t\t\t/* istanbul ignore next */\r\n\t\t\t\tif (!supportedCountries || supportedCountries.indexOf(phoneNumber.country) >= 0) {\r\n\t\t\t\t\tparsedCountry = phoneNumber.country\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tparsedCountry = getCountryForPartialE164Number(newValue, {\r\n\t\t\t\t\tcountry: undefined,\r\n\t\t\t\t\tcountries: supportedCountries,\r\n\t\t\t\t\tmetadata\r\n\t\t\t\t})\r\n\t\t\t\t// In cases when multiple countries correspond to the same country calling code,\r\n\t\t\t\t// the phone number digits of `newValue` have to be matched against country-specific\r\n\t\t\t\t// regular expressions in order to determine the exact country.\r\n\t\t\t\t// Sometimes, that algorithm can't decide for sure which country does the phone number belong to,\r\n\t\t\t\t// for example when the digits of `newValue` don't match any of those regular expressions.\r\n\t\t\t\t// and the country of the phone number couldn't be determined.\r\n\t\t\t\t// In those cases, people prefer the component to show the flag of the `defaultCountry`\r\n\t\t\t\t// if the phone number could potentially belong to that `defaultCountry`.\r\n\t\t\t\t// At least that's how the component behaves when a user pastes an international\r\n\t\t\t\t// phone number into the input field: for example, when `defaultCountry` is `\"US\"`\r\n\t\t\t\t// and the user pastes value \"****** 555 5555\" into the input field, it keep showing \"US\" flag.\r\n\t\t\t\t// So when setting new `value` property externally, the component should behave the same way:\r\n\t\t\t\t// it should select the `defaultCountry` when the new `value` could potentially belong\r\n\t\t\t\t// to that country in cases when the exact country can't be determined.\r\n\t\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/413#issuecomment-1536219404\r\n\t\t\t\tif (!parsedCountry) {\r\n\t\t\t\t\tif (newDefaultCountry) {\r\n\t\t\t\t\t\tif (newValue.indexOf(getInternationalPhoneNumberPrefix(newDefaultCountry, metadata)) === 0) {\r\n\t\t\t\t\t\t\tparsedCountry = newDefaultCountry\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tlet hasUserSelectedACountryUpdate\r\n\t\tif (!newValue) {\r\n\t\t\t// Reset `hasUserSelectedACountry` flag in `state`.\r\n\t\t\thasUserSelectedACountryUpdate = {\r\n\t\t\t\thasUserSelectedACountry: undefined\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\t...hasUserSelectedACountryUpdate,\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tphoneNumber,\r\n\t\t\t\tvalue: newValue,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: newValue,\r\n\t\t\tcountry: newValue ? parsedCountry : newDefaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// `defaultCountry` didn't change.\r\n\t// `value` didn't change.\r\n\t// `phoneDigits` didn't change, because `value` didn't change.\r\n\t//\r\n\t// So no need to update state.\r\n}\r\n\r\nfunction valuesAreEqual(value1, value2) {\r\n\t// If `value` has been set to `null` externally then convert it to `undefined`.\r\n\t//\r\n\t// For example, `react-hook-form` sets `value` to `null` when the user clears the input.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/164\r\n\t// In that case, without this conversion of `null` to `undefined`, it would reset\r\n\t// the selected country to `defaultCountry` because in that case `newValue !== value`\r\n\t// because `null !== undefined`.\r\n\t//\r\n\t// Historically, empty `value` is encoded as `undefined`.\r\n\t// Perhaps empty `value` would be better encoded as `null` instead.\r\n\t// But because that would be a potentially breaking change for some people,\r\n\t// it's left as is for the current \"major\" version of this library.\r\n\t//\r\n\tif (value1 === null) {\r\n\t\tvalue1 = undefined\r\n\t}\r\n\tif (value2 === null) {\r\n\t\tvalue2 = undefined\r\n\t}\r\n\treturn value1 === value2\r\n}"], "mappings": ";;;;;;AAAA,SACCA,qBADD,EAECC,8BAFD,EAGCC,gBAHD,QAIO,wBAJP;AAMA,OAAOC,iCAAP,MAA8C,wCAA9C;AAEA,SACCC,2BADD,EAECC,qBAFD,QAGO,gBAHP;AAKA,eAAe,SAASC,+CAAT,CAAyDC,KAAzD,EAAgEC,SAAhE,EAA2EC,KAA3E,EAAkF;EAChG,IACCC,QADD,GAWIH,KAXJ,CACCG,QADD;EAAA,IAECC,SAFD,GAWIJ,KAXJ,CAECI,SAFD;EAAA,IAGiBC,iBAHjB,GAWIL,KAXJ,CAGCM,cAHD;EAAA,IAIQC,QAJR,GAWIP,KAXJ,CAICQ,KAJD;EAAA,IAKQC,QALR,GAWIT,KAXJ,CAKCU,KALD;EAAA,IAMCC,aAND,GAWIX,KAXJ,CAMCW,aAND;EAAA,IASCC,gCATD,GAWIZ,KAXJ,CASCY,gCATD;EAAA,IAUCC,kBAVD,GAWIb,KAXJ,CAUCa,kBAVD;EAaA,IACiBC,kBADjB,GAIIb,SAJJ,CACCK,cADD;EAAA,IAEQS,SAFR,GAIId,SAJJ,CAECO,KAFD;EAAA,IAGQQ,SAHR,GAIIf,SAJJ,CAGCS,KAHD;EAMA,IACCO,OADD,GAQIf,KARJ,CACCe,OADD;EAAA,IAECT,KAFD,GAQIN,KARJ,CAECM,KAFD;EAAA,IAOCU,uBAPD,GAQIhB,KARJ,CAOCgB,uBAPD;;EAUA,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAyB,CAACC,UAAD;IAAA,OAAgB3B,qBAAqB,iCAChE2B,UADgE;MAEnET,aAAa,EAAbA,aAFmE;MAGnEU,iBAAiB,EAAET,gCAAgC,IAAIC,kBAAkB,KAAK,UAHX;MAInEV,QAAQ,EAARA;IAJmE,GAArC;EAAA,CAA/B,CA9BgG,CAqChG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAIM,QAAQ,KAAKO,SAAjB,EAA4B;IAC3B,OAAO;MACNM,WAAW,EAAEH,sBAAsB,CAAC;QACnCX,KAAK,EAAEe,SAD4B;QAEnCjB,cAAc,EAAED;MAFmB,CAAD,CAD7B;MAKNG,KAAK,EAAEe,SALD;MAMNN,OAAO,EAAEZ,iBANH;MAONa,uBAAuB,EAAEK;IAPnB,CAAP;EASA,CA1D+F,CA4DhG;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAIlB,iBAAiB,KAAKS,kBAA1B,EAA8C;IAC7C,IAAMU,4BAA4B,GAAG,CAACnB,iBAAD,IAAsBR,2BAA2B,CAACQ,iBAAD,EAAoBF,QAApB,CAAtF;;IACA,IAAMsB,8BAA8B,GACnC;IACA,CAACjB,KAAD,IACA;IACA;IACA;IACA;IACCG,aAAa,IAAIH,KAAK,KAAKW,sBAAsB,CAAC;MAClDX,KAAK,EAAEe,SAD2C;MAElDjB,cAAc,EAAEQ;IAFkC,CAAD,CAPnD,CAF6C,CAc7C;IACA;;;IACA,IAAMY,qBAAqB,GAAG,CAACnB,QAAD,IAAakB,8BAA3C;;IACA,IAAI,CAACP,uBAAD,IAA4BM,4BAA5B,IAA4DE,qBAAhE,EAAuF;MACtF,OAAO;QACNT,OAAO,EAAEZ,iBADH;QAEN;QACA;QACA;QACA;QACA;QACAiB,WAAW,EAAEH,sBAAsB,CAAC;UACnCX,KAAK,EAAEe,SAD4B;UAEnCjB,cAAc,EAAED;QAFmB,CAAD,CAP7B;QAWN;QACAG,KAAK,EAAEe;MAZD,CAAP;IAcA;EACD,CAzG+F,CA2GhG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAI,CAACI,cAAc,CAACpB,QAAD,EAAWQ,SAAX,CAAf,IAAwC,CAACY,cAAc,CAACpB,QAAD,EAAWC,KAAX,CAA3D,EAA8E;IAC7E,IAAIoB,WAAJ;IACA,IAAIC,aAAJ;;IACA,IAAItB,QAAJ,EAAc;MACbqB,WAAW,GAAGjC,gBAAgB,CAACY,QAAD,EAAWJ,QAAX,CAA9B;MACA,IAAM2B,kBAAkB,GAAGhC,qBAAqB,CAACM,SAAD,EAAYD,QAAZ,CAAhD;;MACA,IAAIyB,WAAW,IAAIA,WAAW,CAACX,OAA/B,EAAwC;QACvC;;QACA;QACA,IAAI,CAACa,kBAAD,IAAuBA,kBAAkB,CAACC,OAAnB,CAA2BH,WAAW,CAACX,OAAvC,KAAmD,CAA9E,EAAiF;UAChFY,aAAa,GAAGD,WAAW,CAACX,OAA5B;QACA;MACD,CAND,MAMO;QACNY,aAAa,GAAGnC,8BAA8B,CAACa,QAAD,EAAW;UACxDU,OAAO,EAAEM,SAD+C;UAExDnB,SAAS,EAAE0B,kBAF6C;UAGxD3B,QAAQ,EAARA;QAHwD,CAAX,CAA9C,CADM,CAMN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,IAAI,CAAC0B,aAAL,EAAoB;UACnB,IAAIxB,iBAAJ,EAAuB;YACtB,IAAIE,QAAQ,CAACwB,OAAT,CAAiBnC,iCAAiC,CAACS,iBAAD,EAAoBF,QAApB,CAAlD,MAAqF,CAAzF,EAA4F;cAC3F0B,aAAa,GAAGxB,iBAAhB;YACA;UACD;QACD;MACD;IACD;;IACD,IAAI2B,6BAAJ;;IACA,IAAI,CAACzB,QAAL,EAAe;MACd;MACAyB,6BAA6B,GAAG;QAC/Bd,uBAAuB,EAAEK;MADM,CAAhC;IAGA;;IACD,uCACIS,6BADJ;MAECV,WAAW,EAAEH,sBAAsB,CAAC;QACnCS,WAAW,EAAXA,WADmC;QAEnCpB,KAAK,EAAED,QAF4B;QAGnCD,cAAc,EAAED;MAHmB,CAAD,CAFpC;MAOCG,KAAK,EAAED,QAPR;MAQCU,OAAO,EAAEV,QAAQ,GAAGsB,aAAH,GAAmBxB;IARrC;EAUA,CAjL+F,CAmLhG;EACA;EACA;EACA;EACA;;AACA;;AAED,SAASsB,cAAT,CAAwBM,MAAxB,EAAgCC,MAAhC,EAAwC;EACvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,MAAM,KAAK,IAAf,EAAqB;IACpBA,MAAM,GAAGV,SAAT;EACA;;EACD,IAAIW,MAAM,KAAK,IAAf,EAAqB;IACpBA,MAAM,GAAGX,SAAT;EACA;;EACD,OAAOU,MAAM,KAAKC,MAAlB;AACA"}