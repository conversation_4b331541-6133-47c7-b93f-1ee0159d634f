import {
  createUniver,
  defaultTheme,
  LocaleType,
  merge,
} from "@univerjs/presets";
import { UniverSheetsCorePreset } from "@univerjs/presets/preset-sheets-core";
import UniverPresetSheetsCoreEnUS from "@univerjs/presets/preset-sheets-core/locales/en-US";

import "./style.css";
import "@univerjs/presets/lib/styles/preset-sheets-core.css";

import { BooleanNumber, SheetTypes } from "@univerjs/core";
import { LocaleType as CoreLocaleType } from "@univerjs/core";
import {
  useCallback,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import { ToastContainer, Zoom } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Currencies } from "../Modals/data";

function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

// Helper function to detect currency symbol in a string
const detectCurrencySymbol = (value) => {
  if (typeof value !== "string") return null;

  for (const currency of Currencies) {
    if (value.includes(currency.sign)) {
      return currency.sign;
    }
  }
  return null;
};

// Helper function to get currency format pattern
const getCurrencyFormatPattern = (currencySymbol) => {
  if (!currencySymbol) return null;

  // Handle special cases for multi-character currency symbols
  if (currencySymbol === "SAR") {
    return '"SAR"#,##0';
  }

  // For single character symbols, wrap in quotes
  return `"${currencySymbol}"#,##0`;
};

// Helper function to clean currency symbols from string values
const cleanCurrencyValue = (value) => {
  if (typeof value !== "string") return value;

  let cleaned = value;
  // Remove all known currency symbols
  for (const currency of Currencies) {
    cleaned = cleaned.replace(
      new RegExp(currency.sign.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
      ""
    );
  }

  // Remove commas and trim
  cleaned = cleaned.replace(/,/g, "").trim();

  return cleaned;
};

export const letterToColumn = (letter) => {
  let col = 0;
  for (let i = 0; i < letter.length; i++) {
    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);
  }
  return col - 1;
};

export const columnToLetter = (col) => {
  let letter = "";
  while (col >= 0) {
    letter = String.fromCharCode((col % 26) + 65) + letter;
    col = Math.floor(col / 26) - 1;
  }
  return letter;
};

export const cellRefToIndices = (cellRef) => {
  const match = cellRef.match(/([A-Z]+)(\d+)/);
  if (!match) return null;
  const [, colLetter, rowNumber] = match;
  const rowIndex = parseInt(rowNumber, 10) - 1;
  const colIndex = letterToColumn(colLetter);
  return { rowIndex, colIndex };
};

export const transformApiToMatrix = (apiData) => {
  const cellData = {};
  let maxRow = 0;
  let maxCol = 0;

  if (apiData?.excelData) {
    Object?.entries(apiData.excelData).forEach(([cell, value]) => {
      const indices = cellRefToIndices(cell);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;

      if (!cellData[rowIndex]) cellData[rowIndex] = {};

      if (typeof value === "string" && value.startsWith("=")) {
        cellData[rowIndex][colIndex] = {
          f: value,
          s: {
            ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
          },
        };
      } else {
        cellData[rowIndex][colIndex] = {
          v: value,
          s: {
            ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
          },
        };
      }

      maxRow = Math.max(maxRow, rowIndex);
      maxCol = Math.max(maxCol, colIndex);
    });
  }

  if (Array?.isArray(apiData?.maskedCells)) {
    apiData.maskedCells.forEach((cellRef) => {
      const indices = cellRefToIndices(cellRef);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;

      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      cellData[rowIndex][colIndex].s = {
        ...(cellData[rowIndex][colIndex].s || {}),
        bg: { rgb: "#FFFF00" },
        ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
      };

      if (apiData?.highlightAllMaskedCells) {
        cellData[rowIndex][colIndex].v = "";
      }
    });
  }

  return {
    id: `workbook-${Date.now()}`,
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        cellData,
        rowCount: Math.max(maxRow + 1, 20),
        columnCount: Math.max(maxCol + 1, 20),
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
      },
    },
  };
};

export const transformMatrixToApi = (
  cellData,
  setExcelCellMatrix,
  userInteractedCells = new Set(),
  maskedCells = [],
  existingCellMatrix = null
) => {

  console.log(cellData, "cell data")
  const structuredMatrix = JSON.parse(JSON.stringify(existingCellMatrix || {}));
  const apiExcelData = {};

  // Log what cells are currently tracked as user-interacted
  console.log(
    "User-interacted cells before processing:",
    Array.from(userInteractedCells)
  );

  if (!cellData)
    return { cellMatrix: structuredMatrix, excelData: apiExcelData };

  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      if (!structuredMatrix[rowIndex]) structuredMatrix[rowIndex] = {};
      structuredMatrix[rowIndex][colIndex] = { ...cell };

      const colLetter = columnToLetter(parseInt(colIndex));
      const rowNumber = parseInt(rowIndex) + 1;
      const ref = `${colLetter}${rowNumber}`;
      const isMaskedCell = maskedCells.includes(ref);

      if (isMaskedCell) {
        console.log("ahhhahaha");
        if (userInteractedCells.has(ref)) {
          const isZeroWithoutFormula =
            (cell.v === 0 || cell.v === "0") &&
            !cell.f &&
            (!cell.m || cell.m === "");

          if (isZeroWithoutFormula) {
            console.log(
              `⛔ Skipping ${ref}: zero with no formula or display value`
            );
            return;
          }

          const isNumber =
            typeof cell.v === "number" || (!isNaN(cell.v) && cell.t === 2);
          const finalValue = isNumber ? Number(cell.v) : String(cell.v);
          apiExcelData[ref] = finalValue;
          console.log(
            `✅ Including masked cell ${ref}:`,
            finalValue,
            `formula:`,
            cell.f || "none"
          );
        }
      } else {
        if (
          (cell?.v !== undefined && cell.v !== null && cell.v !== "") ||
          (cell?.v === 0 && cell?.f && userInteractedCells.has(ref))
        ) {
          // For non-masked cells, only include zeros if they have formulas
          const isNumber =
            typeof cell.v === "number" || (!isNaN(cell.v) && cell.t === 2);
          apiExcelData[ref] = isNumber ? Number(cell.v) : String(cell.v);
        }
      }
    });
  });

  // Final safety check - remove any zeros in masked cells that somehow made it through
  const finalApiData = {};
  Object.entries(apiExcelData).forEach(([cellRef, value]) => {
    const isMaskedCell = maskedCells.includes(cellRef);

    if (isMaskedCell && value === 0) {
      console.log(
        `FINAL CHECK: Removing zero from masked cell ${cellRef} before API submission`
      );
      // Don't include this cell
    } else {
      finalApiData[cellRef] = value;
    }
  });

  for (const row in structuredMatrix) {
    for (const col in structuredMatrix[row]) {
      const cell = structuredMatrix[row][col];
      const colLetter = columnToLetter(Number(col));
      const rowNumber = Number(row) + 1;
      const ref = `${colLetter}${rowNumber}`;
      const isMasked = maskedCells.includes(ref);
      const isUserInteracted = userInteractedCells.has(ref);

      const isZeroCell =
        (cell.v === 0 || cell.v === "0") &&
        !cell.f &&
        (!cell.m || cell.m === "") &&
        !isUserInteracted;

      if (isZeroCell) {
        delete structuredMatrix[row][col];
      }
    }
    if (Object.keys(structuredMatrix[row]).length === 0) {
      delete structuredMatrix[row];
    }
  }

  if (typeof setExcelCellMatrix === "function") {
    setExcelCellMatrix(structuredMatrix);
  }

  console.log(finalApiData, "final api data")

  return { cellMatrix: structuredMatrix, excelData: finalApiData };
};

export const transformApiMatrixToSheetData = (
  cellMatrix,
  maskedCells = [],
  highlightAllMaskedCells = false
) => {
  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));

  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      if (!cell.s) cell.s = {};
      delete cell.s.ht;
      delete cell.s.vt;
      delete cell.s.tb;
      cell.s.ht = 1;
    });
  });
  if (Array.isArray(maskedCells)) {
    maskedCells.forEach((cellRef) => {
      const indices = cellRefToIndices(cellRef);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;
      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      const style = {
        bg: { rgb: "#FFFF00" },
        ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
      };

      cellData[rowIndex][colIndex].s = style;
      if (!highlightAllMaskedCells) {
        cellData[rowIndex][colIndex].v = "";
        if (cellData[rowIndex][colIndex].f)
          delete cellData[rowIndex][colIndex].f;
      }
    });
  }

  return {
    id: `workbook-${Date.now()}`,
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        cellData,
        rowCount: 20,
        columnCount: 20,
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
      },
    },
  };
};

export const transformMatrixWithResponseOverlay = (
  cellMatrix,
  responseSubmitted,
  maskedCells = [],
  userInteractedCellsRef = null
) => {
  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));

  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      if (!cell.s) cell.s = {};
      delete cell.s.ht;
      delete cell.s.vt;
      delete cell.s.tb;
      cell.s.ht = 1;
    });
  });

  const isMatrixFormat =
    responseSubmitted &&
    typeof responseSubmitted === "object" &&
    Object.keys(responseSubmitted).some((key) => !isNaN(key));

  if (Array.isArray(maskedCells)) {
    maskedCells.forEach((cellRef) => {
      const indices = cellRefToIndices(cellRef);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;
      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      const style = {
        bg: { rgb: "#FFFF00" },
        ht: 1,
      };

      cellData[rowIndex][colIndex].s = style;

      let submittedValue = null;

      if (responseSubmitted) {
        if (isMatrixFormat) {
          const submittedCell = responseSubmitted[rowIndex]?.[colIndex];
          if (submittedCell) {
            // Track this cell as user-interacted since it has submitted data
            if (userInteractedCellsRef?.current) {
              userInteractedCellsRef.current.add(cellRef);
            }

            cellData[rowIndex][colIndex] = {
              ...cellData[rowIndex][colIndex],
              ...submittedCell,
              s: {
                ...cellData[rowIndex][colIndex].s,
                ...submittedCell.s,
                bg: { rgb: "#FFFF00" },
                ht: 1,
              },
            };

            if (cellData[rowIndex][colIndex].s?.n) {
              let pattern = cellData[rowIndex][colIndex].s.n;
              if (typeof pattern === "string") {
                pattern = pattern.replace(/\\"/g, '"');
                cellData[rowIndex][colIndex].s.n = { pattern: pattern };
              } else if (pattern.pattern) {
                let cleanPattern = pattern.pattern.replace(/\\"/g, '"');
                cellData[rowIndex][colIndex].s.n = { pattern: cleanPattern };
              }

              if (
                submittedCell.m &&
                submittedCell.m !== cellData[rowIndex][colIndex].v
              ) {
                cellData[rowIndex][colIndex].m = submittedCell.m;
              }
            }

            return;
          }
        } else {
          submittedValue = responseSubmitted[cellRef];
          // Track this cell as user-interacted if it has submitted data
          if (
            submittedValue !== undefined &&
            submittedValue !== null &&
            submittedValue !== "" &&
            userInteractedCellsRef?.current
          ) {
            userInteractedCellsRef.current.add(cellRef);
          }
        }
      }

      if (
        submittedValue !== undefined &&
        submittedValue !== null &&
        submittedValue !== ""
      ) {
        let cellType = 1;
        let finalValue = submittedValue;

        if (
          typeof submittedValue === "string" &&
          submittedValue.startsWith("=")
        ) {
          cellData[rowIndex][colIndex].f = submittedValue;
        } else {
          if (typeof submittedValue === "number") {
            finalValue = submittedValue;
            cellType = 2;
          } else if (typeof submittedValue === "string") {
            const stringValue = submittedValue.trim();
            if (
              !isNaN(stringValue) &&
              !isNaN(parseFloat(stringValue)) &&
              stringValue !== ""
            ) {
              finalValue = Number(stringValue);
              cellType = 2;
            } else {
              finalValue = submittedValue;
              cellType = 1;
            }
          }

          cellData[rowIndex][colIndex].v = finalValue;
          cellData[rowIndex][colIndex].t = cellType;

          if (cellType === 2) {
            if (!cellData[rowIndex][colIndex].s) {
              cellData[rowIndex][colIndex].s = {};
            }

            // Check if there's already a pattern in the original cell data
            const originalCell = cellMatrix?.[rowIndex]?.[colIndex];
            if (originalCell?.s?.n) {
              // Clean up the existing pattern - remove escape slashes
              let pattern = originalCell.s.n;
              if (typeof pattern === "string") {
                pattern = pattern.replace(/\\"/g, '"');
                cellData[rowIndex][colIndex].s.n = { pattern: pattern };
              } else if (pattern.pattern) {
                let cleanPattern = pattern.pattern.replace(/\\"/g, '"');
                cellData[rowIndex][colIndex].s.n = { pattern: cleanPattern };
              }
            } else {
              // Fallback to default currency pattern - detect from value or use USD as default
              const cellValue = cellData[rowIndex][colIndex].v;
              const detectedSymbol = detectCurrencySymbol(
                String(cellValue || "")
              );
              const formatPattern = getCurrencyFormatPattern(
                detectedSymbol || "$"
              );
              cellData[rowIndex][colIndex].s.n = { pattern: formatPattern };
            }
          }
        }
      } else {
        cellData[rowIndex][colIndex].v = "";
        cellData[rowIndex][colIndex].t = 1;
        if (cellData[rowIndex][colIndex].f) {
          delete cellData[rowIndex][colIndex].f;
        }
      }
    });
  }

  console.log(cellData, "my god data");
  return {
    id: `workbook-${Date.now()}`,
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        cellData,
        rowCount: 20,
        columnCount: 20,
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
      },
    },
  };
};
const ExcelSheets = forwardRef(
  (
    {
      cellsData,
      maskedCells,
      SetExcelApiData,
      SetCellsFilled,
      cellsFilled,
      apiData,
      excelID,
      responseSubmitted,
      highlightAllMaskedCells = false,
      setExcelCellMatrix = null,
    },
    ref
  ) => {
    const [workbookData, setWorkbookData] = useState(null);
    const univerCreatedRef = useRef(false);
    const univerAPIRef = useRef(null);
    const workbookDataRef = useRef(null);
    const pendingChangesRef = useRef(new Map()); // Track pending changes
    const debouncedHandleRef = useRef(null);
    const userInteractedCellsRef = useRef(new Set()); // Track cells user actually interacted with

    // Expose method to force sync all data immediately
    useImperativeHandle(ref, () => ({
      syncData: () => {
        // Cancel any pending debounced calls
        if (debouncedHandleRef.current) {
          debouncedHandleRef.current.cancel?.();
        }
        // Process any pending changes immediately
        handleDataChangeImmediate();
      },
    }));

    // Helper function to check if all masked cells are filled in responseSubmitted
    const areAllMaskedCellsFilled = useCallback(() => {
      if (!responseSubmitted || !maskedCells || maskedCells.length === 0) {
        return false;
      }

      return maskedCells.every((cellRef) => {
        const value = responseSubmitted[cellRef];
        return value !== undefined && value !== null && value !== "";
      });
    }, [responseSubmitted, maskedCells]);

    useEffect(() => {
      let matrix;

      if (apiData?.cellMatrix && !responseSubmitted) {
        matrix = transformApiMatrixToSheetData(
          apiData.cellMatrix,
          maskedCells,
          highlightAllMaskedCells
        );
      } else if (responseSubmitted) {
        if (apiData?.cellMatrix) {
          const responseData = apiData?.responseSubmitted?.cellMatrixResponse;

          if (responseData) {
            matrix = transformMatrixWithResponseOverlay(
              apiData.cellMatrix,
              responseData,
              maskedCells,
              userInteractedCellsRef
            );
          } else {
            matrix = transformMatrixWithResponseOverlay(
              apiData.cellMatrix,
              responseSubmitted,
              maskedCells,
              userInteractedCellsRef
            );
          }
        } else {
          matrix = transformApiToMatrix({
            excelData: responseSubmitted,
            maskedCells,
            highlightAllMaskedCells,
          });
        }
      } else {
        matrix = transformApiToMatrix({
          excelData: apiData?.excelData,
          maskedCells,
          highlightAllMaskedCells,
        });
      }
      // Only proceed if we have a valid matrix
      if (matrix) {
        const initialWorkbook = {
          ...matrix,
          locale: CoreLocaleType.EN_US,
          appVersion: "3.0.0-alpha",
        };

        setWorkbookData(initialWorkbook);
        workbookDataRef.current = initialWorkbook;
        univerCreatedRef.current = false;
        pendingChangesRef.current.clear();

        // If we have a response submitted, update the API data before clearing user interactions
        if (responseSubmitted && matrix?.sheets?.["sheet-01"]?.cellData) {
          SetExcelApiData(
            transformMatrixToApi(
              matrix.sheets["sheet-01"].cellData,
              setExcelCellMatrix,
              userInteractedCellsRef.current,
              maskedCells,
              apiData?.responseSubmitted?.cellMatrixResponse || null
            )
          );
        }

        // Only clear user interactions if we don't have a response submitted
        if (!responseSubmitted) {
          userInteractedCellsRef.current.clear();
        }
      }
    }, [
      apiData,
      maskedCells,
      highlightAllMaskedCells,
      responseSubmitted,
      areAllMaskedCellsFilled,
    ]);

    // Immediate data change handler (no debounce)
    const handleDataChangeImmediate = useCallback(() => {
      const univerAPI = univerAPIRef.current;
      const book = univerAPI?.getActiveWorkbook();
      const sheet = book?.getActiveSheet();
      if (!sheet) return;

      const refWorkbook = workbookDataRef.current;
      const existingCellData =
        refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = {};

      const sheetData = sheet.getSheet().getSnapshot();
      const maxRow = sheetData.rowCount - 1;
      const maxCol = sheetData.columnCount - 1;

      // Process all cells including pending changes
      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
        for (let colNum = 0; colNum <= maxCol; colNum++) {
          const cellKey = `${rowNum}-${colNum}`;
          const range = sheet.getRange(rowNum, colNum);
          const formula = range.getFormula() || "";
          let calculatedValue = range.getValue();

          // For formulas, also get the display value to ensure we have the calculated result
          let displayValue = calculatedValue;
          if (formula && typeof range.getDisplayValue === "function") {
            displayValue = range.getDisplayValue();
            // If display value is different and not empty, use it as the calculated value
            if (displayValue !== null && displayValue !== undefined && displayValue !== calculatedValue) {
              calculatedValue = displayValue;
            }
          }

          // Don't convert 0 to empty string - 0 is a valid value
          if (calculatedValue === null || calculatedValue === undefined) {
            calculatedValue = "";
          }

          // Check if there's a pending change for this cell
          let hasManualEdit = false;
          if (pendingChangesRef.current.has(cellKey)) {
            calculatedValue = pendingChangesRef.current.get(cellKey);
            hasManualEdit = true;
          }

          const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;
          const existingCell = existingCellData?.[rowNum]?.[colNum];
          const isMaskedCell = maskedCells.includes(cellRef);
          const wasUserInteracted = userInteractedCellsRef.current.has(cellRef);

          // Check if this cell had a formula before but now has been manually edited
          const hadFormulaButManuallyEdited = existingCell?.f && hasManualEdit;

          const isBlankLike =
            calculatedValue === "" ||
            calculatedValue === null ||
            (typeof calculatedValue === "number" &&
              calculatedValue === 0 &&
              !formula);

          const hasData = isMaskedCell
            ? wasUserInteracted && !isBlankLike
            : (wasUserInteracted && !isBlankLike) ||
              formula ||
              pendingChangesRef.current.has(cellKey);

          if (hasData) {
            if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};

            // Determine proper type for the cell value
            let cellType = 1; // Default to text
            let finalValue = calculatedValue;

            // For formulas, ensure we use the calculated numeric value if possible
            if (formula && calculatedValue !== "" && typeof calculatedValue !== "undefined") {
              // Try to convert to number if it's a numeric result
              if (typeof calculatedValue === "string") {
                const cleaned = calculatedValue.replace(/[,%$]/g, "");
                if (!isNaN(cleaned) && !isNaN(parseFloat(cleaned))) {
                  finalValue = Number(cleaned);
                  cellType = 2; // Number type
                } else {
                  finalValue = calculatedValue;
                  cellType = 1; // Text type
                }
              } else if (typeof calculatedValue === "number") {
                finalValue = calculatedValue;
                cellType = 2; // Number type
              }
            } else if (
              !formula &&
              calculatedValue !== "" &&
              typeof calculatedValue !== "undefined"
            ) {
              // For non-formula cells, check if the value is a number
              const stringValue = String(calculatedValue).trim();
              if (!isNaN(stringValue) && !isNaN(parseFloat(stringValue))) {
                finalValue = Number(stringValue);
                cellType = 2; // Number type
              }
            }

            const newCellData = {
              v: finalValue,
              t: cellType,
              s: {
                ...(existingCell?.s || {}),
                ht: 1, // Ensure left alignment (1 = left, 2 = center, 3 = right)
              },
            };

            // Only include formula if:
            // 1. There is a formula AND
            // 2. The cell hasn't been manually edited (which would override the formula)
            if (formula && !hadFormulaButManuallyEdited) {
              newCellData.f = formula;
            }
            if (isMaskedCell) {
              newCellData.s = {
                ...newCellData.s,
                bg: { rgb: "#FFFF00" },
                ht: 1, // Ensure left alignment for masked cells (1 = left, 2 = center, 3 = right)
              };
              if (highlightAllMaskedCells) {
                newCellData.v = "";
              }
            }
            updatedCellData[rowNum][colNum] = newCellData;
          }
        }
      }

      // Clear pending changes as they've been processed
      pendingChangesRef.current.clear();

      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };

      SetExcelApiData(
        transformMatrixToApi(
          updatedCellData,
          setExcelCellMatrix,
          userInteractedCellsRef.current,
          maskedCells,
          apiData?.responseSubmitted?.cellMatrixResponse || null
        )
      );

      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        let anyFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = updatedCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyFilled = true;
            break;
          }
        }
        SetCellsFilled(anyFilled);
      }
    }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells, responseSubmitted]);

    // Enhanced data change handler that preserves formatting during paste operations
    const handleFormattedDataChange = useCallback(() => {
      try {
        const univerAPI = univerAPIRef.current;
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book?.getActiveSheet();
        if (!sheet || typeof sheet.getRange !== "function") return;

        const refWorkbook = workbookDataRef.current;
        const existingCellData =
          refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
        const updatedCellData = {};

        const sheetData = sheet.getSheet().getSnapshot();
        const maxRow = sheetData.rowCount - 1;
        const maxCol = sheetData.columnCount - 1;

        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
          for (let colNum = 0; colNum <= maxCol; colNum++) {
            const cellKey = `${rowNum}-${colNum}`;
            const range = sheet.getRange(rowNum, colNum);
            if (!range || typeof range.getFormula !== "function") continue;

            const formula = range.getFormula();
            const rawValue = range.getValue();
            const displayValue = range.getDisplayValue?.() ?? rawValue;
            const styleFromSheet = range.getStyle?.() || {};

            if (
              formula ||
              rawValue !== null ||
              maskedCells.includes(`${columnToLetter(colNum)}${rowNum + 1}`)
            ) {
              if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};

              const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;
              const isMasked = maskedCells.includes(cellRef);

              // Check if this cell had a formula but was manually edited
              const existingCell = existingCellData?.[rowNum]?.[colNum];
              const hasManualEdit = pendingChangesRef.current.has(cellKey);
              const hadFormulaButManuallyEdited = existingCell?.f && hasManualEdit;

              let format = styleFromSheet.n;
              if (!format && typeof displayValue === "string") {
                const detectedSymbol = detectCurrencySymbol(displayValue);
                if (detectedSymbol) {
                  format = getCurrencyFormatPattern(detectedSymbol);
                } else if (displayValue.includes("%")) {
                  format = "0.00%";
                }
              }

              let finalValue = rawValue;

              if (typeof rawValue === "string") {
                const cleaned = cleanCurrencyValue(rawValue).replace(/%/g, "");
                finalValue = isNaN(cleaned) ? rawValue : Number(cleaned);
              }

              const patchedCell = {
                v: finalValue,
                m: displayValue,
                t: typeof finalValue === "number" ? 2 : 1,
                s: {
                  ...(existingCellData?.[rowNum]?.[colNum]?.s || {}),
                  ...(format ? { n: format } : {}),
                  ht: 1,
                  ...(isMasked ? { bg: { rgb: "#FFFF00" } } : {}),
                },
              };

              // Only include formula if it exists and the cell hasn't been manually edited
              if (formula && !hadFormulaButManuallyEdited) {
                patchedCell.f = formula;
              }

              updatedCellData[rowNum][colNum] = patchedCell;
            }
          }
        }

        workbookDataRef.current = {
          ...refWorkbook,
          sheets: {
            ...refWorkbook.sheets,
            ["sheet-01"]: {
              ...refWorkbook.sheets["sheet-01"],
              cellData: updatedCellData,
            },
          },
        };

        SetExcelApiData(
          transformMatrixToApi(
            updatedCellData,
            setExcelCellMatrix,
            userInteractedCellsRef.current,
            maskedCells,
            apiData?.responseSubmitted?.cellMatrixResponse || null
          )
        );

        if (highlightAllMaskedCells) {
          SetCellsFilled(false);
        } else {
          let anyFilled = false;
          for (const cellRef of maskedCells) {
            const { rowIndex, colIndex } = cellRefToIndices(cellRef);
            const cell = updatedCellData?.[rowIndex]?.[colIndex];
            if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
              anyFilled = true;
              break;
            }
          }
          SetCellsFilled(anyFilled);
        }
      } catch (error) {
        console.error("Error in handleFormattedDataChange:", error);
      }
    }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);

    // Debounced version for regular updates
    const handleDataChange = useCallback(() => {
      handleDataChangeImmediate();
    }, [handleDataChangeImmediate]);

    useEffect(() => {
      if (!workbookData || univerCreatedRef.current) return;

      const { univerAPI } = createUniver({
        locale: LocaleType.EN_US,
        locales: { [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS) },
        theme: defaultTheme,
        presets: [UniverSheetsCorePreset()],
      });

      univerAPIRef.current = univerAPI;
      univerAPI.createWorkbook(workbookData);

      const initialCellData = workbookData.sheets["sheet-01"].cellData;
      SetExcelApiData(
        transformMatrixToApi(
          initialCellData,
          setExcelCellMatrix,
          userInteractedCellsRef.current,
          maskedCells,
          apiData?.responseSubmitted?.cellMatrixResponse || null
        )
      );

      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        let anyMaskedFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = initialCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyMaskedFilled = true;
            break;
          }
        }
        SetCellsFilled(anyMaskedFilled);
      }

      const restrictPermissionOnCells = async () => {
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book.getActiveSheet();
        const bookId = book.getId();
        const sheetId = sheet.getSheetId();
        const permission = book.getPermission();
        const allCells = [];
        for (let row = 1; row <= 100; row++) {
          for (let col = 0; col < 26; col++) {
            allCells.push(`${String.fromCharCode(65 + col)}${row}`);
          }
        }
        const cellsToLock = allCells.filter(
          (cell) => !maskedCells.includes(cell)
        );
        const ranges = cellsToLock.map((cell) => sheet.getRange(cell));
        const { permissionId } = await permission.addRangeBaseProtection(
          bookId,
          sheetId,
          ranges
        );
        const editPoint =
          permission.permissionPointsDefinition
            .RangeProtectionPermissionEditPoint;
        permission.rangeRuleChangedAfterAuth$.subscribe((id) => {
          if (id === permissionId) {
            permission.setRangeProtectionPermissionPoint(
              bookId,
              sheetId,
              permissionId,
              editPoint,
              false
            );
          }
        });
      };

      restrictPermissionOnCells();

      // Create debounced version with longer delay and store reference
      const debouncedHandleDataChange = debounce(handleDataChange, 100);
      debouncedHandleRef.current = debouncedHandleDataChange;

      univerAPI.addEvent(univerAPI.Event.SheetEditChanging, (params) => {
        const newValue = params?.value?._data?.body?.dataStream;
        const row = params?.row;
        const column = params?.column;
        if (newValue === undefined || row === undefined || column === undefined)
          return;

        const trimmedValue = String(newValue).trim();
        const cellKey = `${row}-${column}`;
        const cellRef = `${columnToLetter(column)}${row + 1}`;

        // Track that user interacted with this cell
        userInteractedCellsRef.current.add(cellRef);

        // Store pending change - but store the properly typed value
        let typedValue = trimmedValue;
        let cellType = 1; // Default to text

        // Check if the value is a number
        if (
          trimmedValue !== "" &&
          !isNaN(trimmedValue) &&
          !isNaN(parseFloat(trimmedValue))
        ) {
          typedValue = Number(trimmedValue);
          cellType = 2; // Number type
        }

        pendingChangesRef.current.set(cellKey, typedValue);

        const refWorkbook = workbookDataRef.current;
        const cellData = refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
        const updatedCellData = { ...cellData };
        if (!updatedCellData[row]) updatedCellData[row] = {};
        const existingCell = updatedCellData[row][column] || {};

        const isMaskedCell = maskedCells.some((cellRef) => {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          return rowIndex === row && colIndex === column;
        });

        if (isMaskedCell) {
          if (highlightAllMaskedCells) {
            updatedCellData[row][column] = {
              ...existingCell,
              v: "",
              t: 1,
              s: {
                bg: { rgb: "#FFFF00" },
                ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
              },
            };
          } else {
            updatedCellData[row][column] = {
              ...existingCell,
              v: typedValue,
              t: cellType,
              s: {
                bg: { rgb: "#FFFF00" },
                ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
              },
            };
          }
        } else {
          updatedCellData[row][column] = {
            ...existingCell,
            v: typedValue,
            t: cellType,
            s: {
              ...(existingCell?.s || {}),
              ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
            },
          };
        }

        workbookDataRef.current = {
          ...refWorkbook,
          sheets: {
            ...refWorkbook.sheets,
            ["sheet-01"]: {
              ...refWorkbook.sheets["sheet-01"],
              cellData: updatedCellData,
            },
          },
        };

        debouncedHandleDataChange();
      });

      // Use the enhanced data change handler for better formatting preservation
      const debouncedFormattedDataChange = debounce(
        handleFormattedDataChange,
        100
      );

      // Aggressive formula formatting handler - triggers on ANY value change
      const handleFormulaFormatting = () => {
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book?.getActiveSheet();
        if (!sheet) return;

        // Check all cells for formulas that need formatting
        const sheetData = sheet.getSheet().getSnapshot();
        const maxRow = Math.min(sheetData.rowCount - 1, 100); // Limit to reasonable range
        const maxCol = Math.min(sheetData.columnCount - 1, 26);

        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
          for (let colNum = 0; colNum <= maxCol; colNum++) {
            const range = sheet.getRange(rowNum, colNum);
            const formula = range.getFormula();
            const calculatedValue = range.getValue();

            if (
              formula &&
              formula.startsWith("=") &&
              typeof calculatedValue === "number"
            ) {
              const cellRefs = formula.match(/[A-Z]+\d+/g);
              if (cellRefs) {
                let shouldApplyCurrencyFormat = false;

                for (const cellRef of cellRefs) {
                  const refIndices = cellRefToIndices(cellRef);
                  if (refIndices) {
                    const refRange = sheet.getRange(
                      refIndices.rowIndex,
                      refIndices.colIndex
                    );
                    const refDisplayValue = refRange.getDisplayValue();
                    if (
                      refDisplayValue &&
                      typeof refDisplayValue === "string" &&
                      detectCurrencySymbol(refDisplayValue)
                    ) {
                      shouldApplyCurrencyFormat =
                        detectCurrencySymbol(refDisplayValue);
                      break;
                    }
                  }
                }

                if (shouldApplyCurrencyFormat) {
                  const currentDisplayValue = range.getDisplayValue();
                  // Only apply if not already formatted with the detected currency
                  if (
                    !currentDisplayValue ||
                    !detectCurrencySymbol(currentDisplayValue)
                  ) {
                    try {
                      const formatPattern = getCurrencyFormatPattern(
                        shouldApplyCurrencyFormat
                      );
                      range.setNumberFormat(formatPattern);
                    } catch (e) {}
                  }
                }
              }
            }
          }
        }
      };

      const captureAllFormulaResults = () => {
        const univerAPI = univerAPIRef.current;
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book?.getActiveSheet();
        if (sheet) {
          const sheetData = sheet.getSheet().getSnapshot();
          const maxRow = sheetData.rowCount - 1;
          const maxCol = sheetData.columnCount - 1;

          for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
            for (let colNum = 0; colNum <= maxCol; colNum++) {
              const range = sheet.getRange(rowNum, colNum);
              if (range) {
                const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;
                const isMaskedCell = maskedCells.includes(cellRef);

                // For masked cells, be very strict about what counts as user interaction
                if (isMaskedCell) {
                  let shouldTrack = false;

                  // Check for formula first - formulas are always meaningful
                  if (typeof range.getFormula === "function") {
                    const formula = range.getFormula();
                    if (formula) {
                      shouldTrack = true;
                    }
                  }

                  // For values without formulas, be very restrictive
                  if (!shouldTrack && typeof range.getValue === "function") {
                    const value = range.getValue();
                    // ONLY track if:
                    // 1. Value is not zero (zeros are likely from drag)
                    // 2. Value is not empty/null/undefined
                    // 3. Cell was already tracked (preserve existing tracking)
                    if (
                      value !== null &&
                      value !== undefined &&
                      value !== "" &&
                      value !== 0 &&
                      !userInteractedCellsRef.current.has(cellRef)
                    ) {
                      shouldTrack = true;
                    }
                  }

                  // Only add to tracking if we're sure it's meaningful
                  if (shouldTrack) {
                    userInteractedCellsRef.current.add(cellRef);
                  }
                }
              }
            }
          }
        }

        // Clean up any cells that were marked as user-interacted but only have zeros
        // This is an aggressive cleanup for drag-generated zeros
        const cellsToRemove = [];
        userInteractedCellsRef.current.forEach((cellRef) => {
          const indices = cellRefToIndices(cellRef);
          if (indices) {
            const { rowIndex, colIndex } = indices;
            const range = sheet.getRange(rowIndex, colIndex);
            if (range) {
              const value = range.getValue();
              const formula = range.getFormula();
              const isMaskedCell = maskedCells.includes(cellRef);

              // Remove from tracking if it's a masked cell with zero and no formula
              if (isMaskedCell && value === 0 && !formula) {
                cellsToRemove.push(cellRef);
              }
            }
          }
        });

        // Remove the problematic cells from tracking
        cellsToRemove.forEach((cellRef) => {
          console.log(
            `Removing cell ${cellRef} from user interaction tracking - had zero without formula`
          );
          userInteractedCellsRef.current.delete(cellRef);
        });

        if (cellsToRemove.length > 0) {
          console.log(
            `Cleaned up ${cellsToRemove.length} cells with drag-generated zeros`
          );
        }

        // Force immediate data capture - no delays, no complications
        handleDataChangeImmediate();

        // Also trigger the formatted version as backup
        setTimeout(() => {
          handleFormattedDataChange();
        }, 100);
      };

      // Trigger formula formatting on multiple events
      const debouncedFormulaFormatting = debounce(handleFormulaFormatting, 200);

      // Listen to SheetValueChanged for immediate formatting
      univerAPI.addEvent(univerAPI.Event.SheetValueChanged, () => {
        debouncedFormulaFormatting();

        // In response submitted mode, prioritize formatted data change for better formula handling
        if (responseSubmitted) {
          // Add a small delay to ensure formulas are fully evaluated
          setTimeout(() => {
            debouncedFormattedDataChange();
          }, 100);
        } else {
          debouncedFormattedDataChange();
        }
      });

      // AGGRESSIVE DRAG DETECTION - Capture data on ANY mouse activity
      const containerElement = document.getElementById(excelID);
      if (containerElement) {
        let isDragging = false;
        let dragTimer = null;
        let isTyping = false;

        const handleMouseDown = (e) => {
          if (e.target.closest(".univer-container")) {
            isDragging = false;
            isTyping = false;
            // Clear any existing timer
            if (dragTimer) {
              clearTimeout(dragTimer);
              dragTimer = null;
            }
          }
        };

        // Track actual typing/input events
        const handleKeyDown = (e) => {
          if (e.target.closest(".univer-container")) {
            isTyping = true;
            // Reset typing flag after a short delay
            setTimeout(() => {
              isTyping = false;
            }, 1000);
          }
        };

        const handleMouseMove = (e) => {
          if (e.buttons === 1) {
            isDragging = true;
          }
        };

        const handleMouseUp = () => {
          if (isDragging) {
            setTimeout(() => {
              captureAllFormulaResults();
            }, 100);

            setTimeout(() => {
              captureAllFormulaResults();
            }, 300);

            setTimeout(() => {
              captureAllFormulaResults();
            }, 600);

            isDragging = false;
          }
        };

        containerElement.addEventListener("mousedown", handleMouseDown);
        containerElement.addEventListener("mousemove", handleMouseMove);
        containerElement.addEventListener("mouseup", handleMouseUp);
        containerElement.addEventListener("keydown", handleKeyDown);

        // Store references for cleanup
        containerElement._dragHandlers = {
          mousedown: handleMouseDown,
          mousemove: handleMouseMove,
          mouseup: handleMouseUp,
          keydown: handleKeyDown,
        };

        const handlePaste = () => {
          setTimeout(() => {
            debouncedFormattedDataChange();
          }, 100);
        };

        containerElement.addEventListener("paste", handlePaste);

        // Store all references for cleanup
        containerElement._pasteHandler = handlePaste;
      }

      // Also try to listen for any available fill/drag events
      try {
        // Try different possible event names for drag operations
        const possibleEvents = [
          "SheetFillSeries",
          "SheetAutofill",
          "SheetDragFill",
          "SheetCellsChanged",
        ];

        possibleEvents.forEach((eventName) => {
          if (univerAPI.Event[eventName]) {
            univerAPI.addEvent(univerAPI.Event[eventName], (params) => {
              setTimeout(() => {
                debouncedFormulaFormatting();
                debouncedFormattedDataChange();
              }, 300);
            });
          }
        });
      } catch (e) {}

      // Listen for paste events if available
      try {
        if (univerAPI.Event.ClipboardPasted) {
          univerAPI.addEvent(
            univerAPI.Event.ClipboardPasted,
            debouncedFormattedDataChange
          );
        }
      } catch (e) {}

      univerCreatedRef.current = true;

      return () => {
        // Cancel any pending debounced calls
        if (debouncedHandleRef.current?.cancel) {
          debouncedHandleRef.current.cancel();
        }

        // Clean up DOM event listeners
        const containerElement = document.getElementById(excelID);
        if (containerElement) {
          // Clean up paste handler
          if (containerElement._pasteHandler) {
            containerElement.removeEventListener(
              "paste",
              containerElement._pasteHandler
            );
            delete containerElement._pasteHandler;
          }

          // Clean up drag handlers
          if (containerElement._dragHandlers) {
            containerElement.removeEventListener(
              "mousedown",
              containerElement._dragHandlers.mousedown
            );
            containerElement.removeEventListener(
              "mousemove",
              containerElement._dragHandlers.mousemove
            );
            containerElement.removeEventListener(
              "mouseup",
              containerElement._dragHandlers.mouseup
            );
            containerElement.removeEventListener(
              "keydown",
              containerElement._dragHandlers.keydown
            );
            delete containerElement._dragHandlers;
          }
        }

        univerAPIRef.current?.dispose?.();
        univerAPIRef.current = null;
        univerCreatedRef.current = false;
        pendingChangesRef.current.clear();
        userInteractedCellsRef.current.clear();
      };
    }, [
      workbookData,
      excelID,
      SetExcelApiData,
      SetCellsFilled,
      highlightAllMaskedCells,
      maskedCells,
      handleDataChange,
      handleFormattedDataChange,
    ]);

    return (
      <div>
        <div className="univer-container" id={excelID} />
        <ToastContainer transition={Zoom} />
      </div>
    );
  }
);

ExcelSheets.displayName = "ExcelSheets";

export default ExcelSheets;