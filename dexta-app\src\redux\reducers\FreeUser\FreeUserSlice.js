import { createSlice } from "@reduxjs/toolkit";
const initialState = {
  setFree: false,
};

export const FreeUserSlice = createSlice({
  name: "examdone",
  initialState,
  reducers: {
    setFreeToTrue: (state, action) => {
      state.setFree = true;
    },
    setFreeToFalse: (state, action) => {
      state.setFree = false;
    },
  },
});

export const { setFreeToTrue, setFreeToFalse } = FreeUserSlice.actions;
export default FreeUserSlice.reducer;
