{"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.0.0", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>", "Théo FIDRY <<EMAIL>>"], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": "visionmedia/bytes.js", "devDependencies": {"mocha": "2.5.3", "nyc": "10.3.2"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}