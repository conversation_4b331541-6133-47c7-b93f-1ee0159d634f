{"name": "handle-thing", "version": "2.0.1", "description": "Wrap Streams2 instance into a HandleWrap", "main": "lib/handle.js", "scripts": {"lint": "standard", "test": "mocha --reporter=spec test/*-test.js", "coverage": "istanbul cover node_modules/.bin/_mocha -- --reporter=spec test/**/*-test.js"}, "pre-commit": ["lint", "test"], "repository": {"type": "git", "url": "git+ssh://**************/indutny/handle-thing.git"}, "keywords": ["handle", "net", "streams2"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/spdy-http2/handle-thing/issues"}, "homepage": "https://github.com/spdy-http2/handle-thing#readme", "devDependencies": {"istanbul": "^0.4.5", "mocha": "^5.2.0", "pre-commit": "^1.2.2", "readable-stream": "^3.0.6", "standard": "^12.0.1", "stream-pair": "^1.0.3"}}