{"name": "@emotion/serialize", "version": "1.1.2", "description": "serialization utils for emotion", "main": "dist/emotion-serialize.cjs.js", "module": "dist/emotion-serialize.esm.js", "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/serialize", "publishConfig": {"access": "public"}, "scripts": {"test:typescript": "dtslint types"}, "dependencies": {"@emotion/hash": "^0.9.1", "@emotion/memoize": "^0.8.1", "@emotion/unitless": "^0.8.1", "@emotion/utils": "^1.2.1", "csstype": "^3.0.2"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^4.5.5"}, "files": ["src", "dist", "types/*.d.ts"], "browser": {"./dist/emotion-serialize.esm.js": "./dist/emotion-serialize.browser.esm.js"}, "exports": {".": {"module": {"browser": "./dist/emotion-serialize.browser.esm.js", "default": "./dist/emotion-serialize.esm.js"}, "import": "./dist/emotion-serialize.cjs.mjs", "default": "./dist/emotion-serialize.cjs.js"}, "./package.json": "./package.json"}, "preconstruct": {"exports": {"envConditions": ["browser"]}}}