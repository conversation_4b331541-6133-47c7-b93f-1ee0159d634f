{"name": "@babel/helper-compilation-targets", "version": "7.22.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Helper functions on Babel compilation targets", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-compilation-targets"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.5", "browserslist": "^4.21.9", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.22.5", "@types/lru-cache": "^5.1.1", "@types/semver": "^5.5.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}