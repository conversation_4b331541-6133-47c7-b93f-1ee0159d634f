{"name": "jest-get-type", "description": "A utility function to get the type of a value", "version": "29.4.3", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-get-type"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1"}