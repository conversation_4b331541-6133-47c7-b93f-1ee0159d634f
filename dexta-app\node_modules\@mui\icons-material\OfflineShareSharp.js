"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M6 5H4v18h12v-2H6z"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M20 1H8v18h12V1zm-2 14h-8V5h8v10z"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12.5 10.25h2V12L17 9.5 14.5 7v1.75H11V12h1.5z"
}, "2")], 'OfflineShareSharp');
exports.default = _default;