There are two licenses, one for the software library, and one for the data.
This product includes GeoLite2 data created by MaxMind, available from http://maxmind.com/


----------------------------------------------
SOFTWARE LICENSE (Node JS library)
----------------------------------------------
The geoip-country is licensed under the Apache License 2.0.
http://www.apache.org/licenses/LICENSE-2.0

Copyright 2020 geoip-country <https://github.com/sapics/geoip-country>
----------------------------------------------

The geoip-country's source code is fork of `node-geoip`.
This is the `node-geoip`'s software licence.
----------------------------------------------
The node-geoip JavaScript library is licensed under the Apache License, Version 2.0:

Copyright 2011 <PERSON> <<EMAIL>>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.


----------------------------------------------
DATABASES LICENSE (GeoLite2 databases)
----------------------------------------------
By downloading or using the "geoip-country" software library (published
to the Node Package Manager at https://www.npmjs.com/package/geoip-country,
thereafter the "Library"), you agree to be bound by the terms and
conditions of this License.

The Library redistributes copyrighted data (the "GeoLite2 Databases")
that is property of MaxMind, Inc. ("MaxMind"), and which use is bound
by the terms and conditions of the MaxMind's End User License Agreement
(the "Agreement"). You should have received an archived copy of the
Agreement with the Library (see EULA), but this License shall only
refer to the latest revision which can be found at
https://www.maxmind.com/en/geolite2/eula.

The database comes with certain restrictions and obligations, most notably:
- You cannot prevent the library from updating the databases.
- You cannot use the GeoLite2 data:
  - for FCRA purposes,
  - to identify specific households or individuals.

The present License incorporates all the terms and conditions of said
Agreement. Your use of the Library must honor those terms.

Additionally, (i) you may not redistribute the Library itself under
different terms and conditions, (ii) you may not remove or obscure any
copyright notice, (iii) you may not prevent the Library from updating
local copies of the GeoLite2 Databases to honor Do Not Sell requests
submitted to MaxMind.

GeoLite2 database is provided under CC BY-SA 4.0 by MaxMind, so,
you must create attribusion to MaxMind for using GeoLite2 database.
https://creativecommons.org/licenses/by-sa/4.0/legalcode