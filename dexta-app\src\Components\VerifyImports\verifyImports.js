import React, { Fragment, useRef } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { IoClose } from "react-icons/io5";
import { LuCheck } from "react-icons/lu";
import { FaExclamationTriangle } from "react-icons/fa";
import * as XLSX from "xlsx";
import { useTranslation } from "react-i18next";

const VerifyImports = ({
  verifyModal,
  setVerifyModal,
  tableData,
  setTableData,
  setFile,
  fileName,
  setFileName,
}) => {
  const { t } = useTranslation();
  const cancelButtonRef = useRef(null);

  const handleDeleteRow = (index) => {
    const newData = tableData.filter((_, i) => i !== index);
    setTableData(newData);
  };

  const handleUpdateFile = () => {
    const worksheet = XLSX.utils.json_to_sheet(tableData);
    const csvData = XLSX.utils.sheet_to_csv(worksheet);
    const fileBlob = new Blob([csvData], { type: "text/csv" });
    const updatedFile = new File([fileBlob], fileName || "updated_file.csv", {
      type: "text/csv",
    });
    setFile(updatedFile);
    // const url = URL.createObjectURL(fileBlob);
    // const link = document.createElement("a");
    // link.href = url;
    // link.download = fileName || "updated_file.csv";
    // document.body.appendChild(link);
    // link.click();
    // document.body.removeChild(link);
    setVerifyModal(false);
  };
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const normalizeHeaders = (data) => {
    return data.map((row) => {
      const normalizedRow = {};
      Object.keys(row).forEach((key) => {
        const normalizedKey = key.toLowerCase() || key.toUpperCase();
        normalizedRow[normalizedKey] = row[key];
      });
      return normalizedRow;
    });
  };

  const validateHeaders = (data) => {
    const expectedHeaders = ["first name", "last name", "email"];
    const normalizedData = normalizeHeaders(data);
    const missingFields = new Set();
    expectedHeaders.forEach((header) => {
      const normalizedHeader = header.toLowerCase();
      if (
        !normalizedData.some((row) =>
          Object.keys(row).some((key) => key.toLowerCase() === normalizedHeader)
        )
      ) {
        missingFields.add(normalizedHeader);
      }
    });

    return [...missingFields];
  };

  const isValidHeader = (header) => {
    const expectedHeaders = ["first name", "last name", "email"];
    return expectedHeaders.some(
      (expected) => expected.toLowerCase() === header.toLowerCase()
    );
  };

  const getMissingFields = () => {
    const missingFields = new Set();
    const normalizedData = normalizeHeaders(tableData);
    normalizedData.forEach((row) => {
      Object.keys(normalizedData[0]).forEach((key) => {
        if (
          !(key in row) ||
          row[key] === null ||
          row[key] === undefined ||
          row[key] === ""
        ) {
          missingFields.add(key);
        }
      });
    });
    return [...missingFields];
  };

  const missingFields = getMissingFields();
  const headerMissingFields = validateHeaders(tableData);

  const hasInvalidHeaders =
    tableData.length > 0
      ? Object.keys(tableData[0]).some((header) => !isValidHeader(header))
      : false;

  const hasInvalidEmailFormat = tableData.some((row) => {
    return !validateEmail(row.email || row.Email);
  });

  const hasDuplicateEmails = tableData.some((row, index) => {
    const email = row.email || row.Email;
    return tableData.findIndex((r) => (r.email || r.Email) === email) !== index;
  });

  const hasInvalidEmails = hasInvalidEmailFormat || hasDuplicateEmails;
  return (
    <Transition.Root show={verifyModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-40"
        initialFocus={cancelButtonRef}
        onClose={() => {
          setFile(null);
          setVerifyModal(false);
          setFileName("");
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" />
        </Transition.Child>
        <div className="fixed inset-0 z-40 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center text-center sm:items-center sm:p-0 sm:px-4 md:px-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="transform overflow-hidden rounded-lg overflow-y-auto bg-white text-left transition-all sm:my-8 sm:max-md:w-full w-3/4 2xl:w-2/4 ">
                <div className="bg-white">
                  <p className="text-xl font-bold text-black cursor-pointer text-left px-8 pt-5">
                    {t('verify_imports.title')}
                  </p>
                  <IoClose
                    className="absolute top-3 right-5 z-20 w-5 h-5 cursor-pointer"
                    onClick={() => {
                      setFile(null);
                      setVerifyModal(false);
                      setFileName("");
                    }}
                  />
                </div>
                <hr className="w-full mt-4 md:block sm:hidden bg-gray-500 border-1" />
                <div className="px-4 mt-3">
                  {(missingFields.length > 0 ||
                    hasInvalidEmails ||
                    hasInvalidHeaders ||
                    headerMissingFields.length > 0) && (
                    <div className="px-4 py-2 bg-red-100 border border-red-400 text-red-800 rounded mb-4">
                      <p className="font-semibold">
                        {t('verify_imports.issues_title')}
                      </p>
                      {missingFields.length > 0 && (
                        <ul className="list-disc list-inside">
                          {missingFields.map((field) => (
                            <li key={field}>
                              {t('verify_imports.missing_field', { field: field.charAt(0).toUpperCase() + field.slice(1) })}
                            </li>
                          ))}
                        </ul>
                      )}
                      {hasInvalidEmailFormat && (
                        <ul className="list-disc list-inside">
                          <li>{t('verify_imports.invalid_email_format')}</li>
                        </ul>
                      )}
                      {hasDuplicateEmails && (
                        <ul className="list-disc list-inside">
                          <li>{t('verify_imports.duplicate_emails')}</li>
                        </ul>
                      )}
                      {(headerMissingFields.length > 0 || hasInvalidHeaders) && (
                        <ul className="list-disc list-inside">
                          {headerMissingFields.map((field) => (
                            <li key={field}>
                              {t('verify_imports.header_missing', { field: field.charAt(0).toUpperCase() + field.slice(1) })}
                            </li>
                          ))}
                          {hasInvalidHeaders && (
                            <li>{t('verify_imports.invalid_headers')}</li>
                          )}
                        </ul>
                      )}
                    </div>
                  )}
                </div>
                {tableData.length > 0 && (
                  <div className="px-4 overflow-auto h-[35rem]">
                    <table className="w-full text-xs 2xl:text-sm rounded text-left rtl:text-right mt-5">
                      <thead className="text-sm bg-coalColor text-white text-left">
                        <tr
                          className="font-normal"
                          style={{ fontFamily: "Archia Semibold" }}
                        >
                          {Object.keys(tableData[0]).map((key) => (
                            <th
                              key={key}
                              className="px-6 py-3 border border-gray-400 relative"
                            >
                              <div className="flex items-center">
                                <span>{key}</span>
                                {!isValidHeader(key) && (
                                  <FaExclamationTriangle
                                    className="ml-2 text-yellow-300"
                                    title={t('verify_imports.header_tooltip')}
                                  />
                                )}
                              </div>
                            </th>
                          ))}
                          <th
                            className="px-6 py-3 border border-gray-400"
                            style={{ width: "50px" }}
                          >
                            {t('verify_imports.status')}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {tableData.map((row, index) => {
                          const emailValue = row.email || row.Email || "";

                          const hasMissingValue = Object.keys(
                            tableData[0]
                          ).some(
                            (key) =>
                              !(key in row) ||
                              row[key] === null ||
                              row[key] === undefined ||
                              row[key] === ""
                          );

                          const isEmailInvalid = !validateEmail(emailValue);

                          const email = row.email || row.Email || "";
                          const isDuplicateEmail =
                            email !== "" &&
                            tableData.findIndex(
                              (r) => (r.email || r.Email) === email
                            ) !== index;

                          // We're directly using isDuplicateEmail instead of these intermediate variables

                          return (
                            <tr
                              key={index}
                              style={{ fontFamily: "Silka" }}
                              className={
                                hasMissingValue ||
                                isEmailInvalid ||
                                isDuplicateEmail
                                  ? "bg-[#FEE2E2]"
                                  : "bg-[#DCFCE7]"
                              }
                            >
                              {Object.keys(tableData[0]).map((key) => (
                                <td
                                  key={key}
                                  className="px-4 py-4 border border-gray-400"
                                >
                                  {row[key] || "N/A"}
                                </td>
                              ))}
                              <td className="px-4 py-4 border border-gray-400">
                                {hasMissingValue ||
                                isEmailInvalid ||
                                isDuplicateEmail ? (
                                  <IoClose
                                    className="w-6 h-6  text-alertRed my-auto mx-auto flex object-cover"
                                    // onClick={() => handleDeleteRow(index)}
                                  />
                                ) : (
                                  <LuCheck className="w-7 h-7  text-[#387c50] my-auto mx-auto flex object-cover" />
                                )}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}

                <div
                  className="flex justify-end p-4"
                  style={{ fontFamily: "Silka" }}
                >
                  <button
                    className="mr-2 px-4 py-2 hover:text-alertRed rounded"
                    onClick={() => {
                      setFile(null);
                      setVerifyModal(false);
                      setFileName("");
                    }}
                  >
                    {t('verify_imports.cancel')}
                  </button>
                  <button
                    className={`px-4 py-2 ${
                      missingFields?.length > 0 ||
                      hasInvalidEmails ||
                      hasInvalidHeaders ||
                      headerMissingFields.length > 0
                        ? "bg-[#D3D5D8]"
                        : "bg-coalColor hover:bg-primaryGreen hover:text-coalColor text-white"
                    } rounded`}
                    onClick={handleUpdateFile}
                    disabled={
                      missingFields?.length > 0 ||
                      hasInvalidEmails ||
                      hasInvalidHeaders ||
                      headerMissingFields.length > 0
                    }
                  >
                    {t('verify_imports.confirm_emails')}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default VerifyImports;
