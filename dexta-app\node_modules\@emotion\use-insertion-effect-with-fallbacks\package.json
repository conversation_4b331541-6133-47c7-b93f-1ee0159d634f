{"name": "@emotion/use-insertion-effect-with-fallbacks", "version": "1.0.1", "description": "A wrapper package that uses `useInsertionEffect` or a fallback for it", "main": "dist/emotion-use-insertion-effect-with-fallbacks.cjs.js", "module": "dist/emotion-use-insertion-effect-with-fallbacks.esm.js", "browser": {"./dist/emotion-use-insertion-effect-with-fallbacks.esm.js": "./dist/emotion-use-insertion-effect-with-fallbacks.browser.esm.js"}, "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/use-insertion-effect-with-fallbacks", "publishConfig": {"access": "public"}, "files": ["src", "dist"], "peerDependencies": {"react": ">=16.8.0"}, "devDependencies": {"react": "16.14.0"}, "exports": {".": {"module": {"worker": "./dist/emotion-use-insertion-effect-with-fallbacks.worker.esm.js", "browser": "./dist/emotion-use-insertion-effect-with-fallbacks.browser.esm.js", "default": "./dist/emotion-use-insertion-effect-with-fallbacks.esm.js"}, "import": "./dist/emotion-use-insertion-effect-with-fallbacks.cjs.mjs", "default": "./dist/emotion-use-insertion-effect-with-fallbacks.cjs.js"}, "./package.json": "./package.json"}, "preconstruct": {"exports": {"envConditions": ["browser", "worker"]}}}