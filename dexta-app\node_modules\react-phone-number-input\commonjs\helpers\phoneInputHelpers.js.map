{"version": 3, "file": "phoneInputHelpers.js", "names": ["getPreSelectedCountry", "value", "phoneNumber", "defaultCountry", "getAnyCountry", "countries", "required", "metadata", "country", "couldNumberBelongToCountry", "indexOf", "undefined", "length", "getCountrySelectOptions", "countryNames", "addInternationalOption", "compareStringsLocales", "_compareStrings", "compareStrings", "countrySelectOptions", "map", "label", "sort", "a", "b", "unshift", "ZZ", "parsePhoneNumber", "parsePhoneNumber_", "generateNationalNumberDigits", "formatNational", "replace", "getPhoneDigitsForNewCountry", "phoneDigits", "prevCountry", "newCountry", "useNationalFormat", "getInternationalPhoneNumberPrefix", "getCountryCallingCode", "stripCountryCallingCode", "newCountryPrefix", "defaultValue", "e164", "number", "asYouType", "AsYouType", "input", "getNumberValue", "partial_national_significant_number", "getNationalSignificantNumberDigits", "trimNumber", "nationalSignificantNumberPart", "overflowDigitsCount", "getMaxNumberLength", "slice", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "numberingPlan", "possibleLengths", "getCountryForPartialE164Number", "partialE164Number", "derived_country", "getCountryFromPossiblyIncompleteInternationalPhoneNumber", "onPhoneDigitsChange", "prevPhoneDigits", "countryRequired", "international", "limitMaxLength", "countryCallingCodeEditable", "prefix", "hasStartedTypingInNationalNumberDigitsHavingInputValueSelected", "convertInternationalPhoneDigitsToNational", "formatter", "getNumber", "getCountry", "locales", "String", "prototype", "localeCompare", "countryCallingCodePrefix", "Object", "keys", "country_calling_codes", "country_calling_code", "nationalNumber", "intlPhoneNumberPrefix", "i", "getInitialPhoneDigits"], "sources": ["../../source/helpers/phoneInputHelpers.js"], "sourcesContent": ["import parsePhoneNumber_, {\r\n\tgetCountryCallingCode,\r\n\tAsYouType,\r\n\tMetadata\r\n} from 'libphonenumber-js/core'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\n/**\r\n * Decides which country should be pre-selected\r\n * when the phone number input component is first mounted.\r\n * @param  {object?} phoneNumber - An instance of `PhoneNumber` class.\r\n * @param  {string?} country - Pre-defined country (two-letter code).\r\n * @param  {string[]?} countries - A list of countries available.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {string?}\r\n */\r\nexport function getPreSelectedCountry({\r\n\tvalue,\r\n\tphoneNumber,\r\n\tdefaultCountry,\r\n\tgetAnyCountry,\r\n\tcountries,\r\n\trequired,\r\n\tmetadata\r\n}) {\r\n\tlet country\r\n\r\n\t// If can get country from E.164 phone number\r\n\t// then it overrides the `country` passed (or not passed).\r\n\tif (phoneNumber && phoneNumber.country) {\r\n\t\t// `country` will be left `undefined` in case of non-detection.\r\n\t\tcountry = phoneNumber.country\r\n\t} else if (defaultCountry) {\r\n\t\tif (!value || couldNumberBelongToCountry(value, defaultCountry, metadata)) {\r\n\t\t\tcountry = defaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// Only pre-select a country if it's in the available `countries` list.\r\n\tif (countries && countries.indexOf(country) < 0) {\r\n\t\tcountry = undefined\r\n\t}\r\n\r\n\t// If there will be no \"International\" option\r\n\t// then some `country` must be selected.\r\n\t// It will still be the wrong country though.\r\n\t// But still country `<select/>` can't be left in a broken state.\r\n\tif (!country && required && countries && countries.length > 0) {\r\n\t\tcountry = getAnyCountry()\r\n\t\t// noCountryMatchesTheNumber = true\r\n\t}\r\n\r\n\treturn country\r\n}\r\n\r\n/**\r\n * Generates a sorted list of country `<select/>` options.\r\n * @param  {string[]} countries - A list of two-letter (\"ISO 3166-1 alpha-2\") country codes.\r\n * @param  {object} labels - Custom country labels. E.g. `{ RU: 'Россия', US: 'США', ... }`.\r\n * @param  {boolean} addInternationalOption - Whether should include \"International\" option at the top of the list.\r\n * @return {object[]} A list of objects having shape `{ value : string, label : string }`.\r\n */\r\nexport function getCountrySelectOptions({\r\n\tcountries,\r\n\tcountryNames,\r\n\taddInternationalOption,\r\n\t// `locales` are only used in country name comparator:\r\n\t// depending on locale, string sorting order could be different.\r\n\tcompareStringsLocales,\r\n\tcompareStrings: _compareStrings\r\n}) {\r\n\t// Default country name comparator uses `String.localeCompare()`.\r\n\tif (!_compareStrings) {\r\n\t\t_compareStrings = compareStrings\r\n\t}\r\n\r\n\t// Generates a `<Select/>` option for each country.\r\n\tconst countrySelectOptions = countries.map((country) => ({\r\n\t\tvalue: country,\r\n\t\t// All `locale` country names included in this library\r\n\t\t// include all countries (this is checked at build time).\r\n\t\t// The only case when a country name might be missing\r\n\t\t// is when a developer supplies their own `labels` property.\r\n\t\t// To guard against such cases, a missing country name\r\n\t\t// is substituted by country code.\r\n\t\tlabel: countryNames[country] || country\r\n\t}))\r\n\r\n\t// Sort the list of countries alphabetically.\r\n\tcountrySelectOptions.sort((a, b) => _compareStrings(a.label, b.label, compareStringsLocales))\r\n\r\n\t// Add the \"International\" option to the country list (if suitable)\r\n\tif (addInternationalOption) {\r\n\t\tcountrySelectOptions.unshift({\r\n\t\t\tlabel: countryNames.ZZ\r\n\t\t})\r\n\t}\r\n\r\n\treturn countrySelectOptions\r\n}\r\n\r\n/**\r\n * Parses a E.164 phone number to an instance of `PhoneNumber` class.\r\n * @param {string?} value = E.164 phone number.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {object} Object having shape `{ country: string?, countryCallingCode: string, number: string }`. `PhoneNumber`: https://gitlab.com/catamphetamine/libphonenumber-js#phonenumber.\r\n * @example\r\n * parsePhoneNumber('+78005553535')\r\n */\r\nexport function parsePhoneNumber(value, metadata) {\r\n\treturn parsePhoneNumber_(value || '', metadata)\r\n}\r\n\r\n/**\r\n * Generates national number digits for a parsed phone.\r\n * May prepend national prefix.\r\n * The phone number must be a complete and valid phone number.\r\n * @param  {object} phoneNumber - An instance of `PhoneNumber` class.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {string}\r\n * @example\r\n * getNationalNumberDigits({ country: 'RU', phone: '8005553535' })\r\n * // returns '88005553535'\r\n */\r\nexport function generateNationalNumberDigits(phoneNumber) {\r\n\treturn phoneNumber.formatNational().replace(/\\D/g, '')\r\n}\r\n\r\n/**\r\n * Migrates parsed `<input/>` `value` for the newly selected `country`.\r\n * @param {string?} phoneDigits - Phone number digits (and `+`) parsed from phone number `<input/>` (it's not the same as the `value` property).\r\n * @param {string?} prevCountry - Previously selected country.\r\n * @param {string?} newCountry - Newly selected country. Can't be same as previously selected country.\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @param {boolean} useNationalFormat - whether should attempt to convert from international to national number for the new country.\r\n * @return {string?}\r\n */\r\nexport function getPhoneDigitsForNewCountry(phoneDigits, {\r\n\tprevCountry,\r\n\tnewCountry,\r\n\tmetadata,\r\n\tuseNationalFormat\r\n}) {\r\n\tif (prevCountry === newCountry) {\r\n\t\treturn phoneDigits\r\n\t}\r\n\r\n\t// If `parsed_input` is empty\r\n\t// then no need to migrate anything.\r\n\tif (!phoneDigits) {\r\n\t\tif (useNationalFormat) {\r\n\t\t\treturn ''\r\n\t\t} else {\r\n\t\t\tif (newCountry) {\r\n\t\t\t\t// If `phoneDigits` is empty then set `phoneDigits` to\r\n\t\t\t\t// `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\treturn getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t}\r\n\t\t\treturn ''\r\n\t\t}\r\n\t}\r\n\r\n\t// If switching to some country.\r\n\t// (from \"International\" or another country)\r\n\t// If switching from \"International\" then `phoneDigits` starts with a `+`.\r\n\t// Otherwise it may or may not start with a `+`.\r\n\tif (newCountry) {\r\n\t\t// If the phone number was entered in international format\r\n\t\t// then migrate it to the newly selected country.\r\n\t\t// The phone number may be incomplete.\r\n\t\t// The phone number entered not necessarily starts with\r\n\t\t// the previously selected country phone prefix.\r\n\t\tif (phoneDigits[0] === '+') {\r\n\t\t\t// If the international phone number is for the new country\r\n\t\t\t// then convert it to local if required.\r\n\t\t\tif (useNationalFormat) {\r\n\t\t\t\t// // If a phone number is being input in international form\r\n\t\t\t\t// // and the country can already be derived from it,\r\n\t\t\t\t// // and if it is the new country, then format as a national number.\r\n\t\t\t\t// const derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(phoneDigits, metadata)\r\n\t\t\t\t// if (derived_country === newCountry) {\r\n\t\t\t\t// \treturn stripCountryCallingCode(phoneDigits, derived_country, metadata)\r\n\t\t\t\t// }\r\n\r\n\t\t\t\t// Actually, the two countries don't necessarily need to match:\r\n\t\t\t\t// the condition could be looser here, because several countries\r\n\t\t\t\t// might share the same international phone number format\r\n\t\t\t\t// (for example, \"NANPA\" countries like US, Canada, etc).\r\n\t\t\t\t// The looser condition would be just \"same nternational phone number format\"\r\n\t\t\t\t// which would mean \"same country calling code\" in the context of `libphonenumber-js`.\r\n\t\t\t\tif (phoneDigits.indexOf('+' + getCountryCallingCode(newCountry, metadata)) === 0) {\r\n\t\t\t\t\treturn stripCountryCallingCode(phoneDigits, newCountry, metadata)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Simply discard the previously entered international phone number,\r\n\t\t\t\t// because otherwise any \"smart\" transformation like getting the\r\n\t\t\t\t// \"national (significant) number\" part and then prepending the\r\n\t\t\t\t// newly selected country's \"country calling code\" to it\r\n\t\t\t\t// would just be confusing for a user without being actually useful.\r\n\t\t\t\treturn ''\r\n\r\n\t\t\t\t// // Simply strip the leading `+` character\r\n\t\t\t\t// // therefore simply converting all digits into a \"local\" phone number.\r\n\t\t\t\t// // https://github.com/catamphetamine/react-phone-number-input/issues/287\r\n\t\t\t\t// return phoneDigits.slice(1)\r\n\t\t\t}\r\n\r\n\t\t\tif (prevCountry) {\r\n\t\t\t\tconst newCountryPrefix = getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t\tif (phoneDigits.indexOf(newCountryPrefix) === 0) {\r\n\t\t\t\t\treturn phoneDigits\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn newCountryPrefix\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconst defaultValue = getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t\t// If `phoneDigits`'s country calling code part is the same\r\n\t\t\t\t// as for the new `country`, then leave `phoneDigits` as is.\r\n\t\t\t\tif (phoneDigits.indexOf(defaultValue) === 0) {\r\n\t\t\t\t\treturn phoneDigits\r\n\t\t\t\t}\r\n\t\t\t\t// If `phoneDigits`'s country calling code part is not the same\r\n\t\t\t\t// as for the new `country`, then set `phoneDigits` to\r\n\t\t\t\t// `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\treturn defaultValue\r\n\t\t\t}\r\n\r\n\t\t\t// // If the international phone number already contains\r\n\t\t\t// // any country calling code then trim the country calling code part.\r\n\t\t\t// // (that could also be the newly selected country phone code prefix as well)\r\n\t\t\t// // `phoneDigits` doesn't neccessarily belong to `prevCountry`.\r\n\t\t\t// // (e.g. if a user enters an international number\r\n\t\t\t// //  not belonging to any of the reduced `countries` list).\r\n\t\t\t// phoneDigits = stripCountryCallingCode(phoneDigits, prevCountry, metadata)\r\n\r\n\t\t\t// // Prepend country calling code prefix\r\n\t\t\t// // for the newly selected country.\r\n\t\t\t// return e164(phoneDigits, newCountry, metadata) || `+${getCountryCallingCode(newCountry, metadata)}`\r\n\t\t}\r\n\t}\r\n\t// If switching to \"International\" from a country.\r\n\telse {\r\n\t\t// If the phone number was entered in national format.\r\n\t\tif (phoneDigits[0] !== '+') {\r\n\t\t\t// Format the national phone number as an international one.\r\n\t\t\t// The phone number entered not necessarily even starts with\r\n\t\t\t// the previously selected country phone prefix.\r\n\t\t\t// Even if the phone number belongs to whole another country\r\n\t\t\t// it will still be parsed into some national phone number.\r\n\t\t\t//\r\n\t\t\t// Ignore the now-uncovered `|| ''` code branch:\r\n\t\t\t// previously `e164()` function could return an empty string\r\n\t\t\t// even when `phoneDigits` were not empty.\r\n\t\t\t// Now it always returns some `value` when there're any `phoneDigits`.\r\n\t\t\t// Still, didn't remove the `|| ''` code branch just in case\r\n\t\t\t// that logic changes somehow in some future, so there're no\r\n\t\t\t// possible bugs related to that.\r\n\t\t\t//\r\n\t\t\t// (ignore the `|| ''` code branch)\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn e164(phoneDigits, prevCountry, metadata) || ''\r\n\t\t}\r\n\t}\r\n\r\n\treturn phoneDigits\r\n}\r\n\r\n/**\r\n * Converts phone number digits to a (possibly incomplete) E.164 phone number.\r\n * @param  {string?} number - A possibly incomplete phone number digits string. Can be a possibly incomplete E.164 phone number.\r\n * @param  {string?} country\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function e164(number, country, metadata) {\r\n\tif (!number) {\r\n\t\treturn\r\n\t}\r\n\t// If the phone number is being input in international format.\r\n\tif (number[0] === '+') {\r\n\t\t// If it's just the `+` sign then return nothing.\r\n\t\tif (number === '+') {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Return a E.164 phone number.\r\n\t\t//\r\n\t\t// Could return `number` \"as is\" here, but there's a possibility\r\n\t\t// that some user might incorrectly input an international number\r\n\t\t// with a \"national prefix\". Such numbers aren't considered valid,\r\n\t\t// but `libphonenumber-js` is \"forgiving\" when it comes to parsing\r\n\t\t// user's input, and this input component follows that behavior.\r\n\t\t//\r\n\t\tconst asYouType = new AsYouType(country, metadata)\r\n\t\tasYouType.input(number)\r\n\t\t// This function would return `undefined` only when `number` is `\"+\"`,\r\n\t\t// but at this point it is known that `number` is not `\"+\"`.\r\n\t\treturn asYouType.getNumberValue()\r\n\t}\r\n\t// For non-international phone numbers\r\n\t// an accompanying country code is required.\r\n\t// The situation when `country` is `undefined`\r\n\t// and a non-international phone number is passed\r\n\t// to this function shouldn't happen.\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\tconst partial_national_significant_number = getNationalSignificantNumberDigits(number, country, metadata)\r\n\t//\r\n\t// Even if no \"national (significant) number\" digits have been input,\r\n\t// still return a non-`undefined` value.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/113\r\n\t//\r\n\t// For example, if the user has selected country `US` and entered `\"1\"`\r\n\t// then that `\"1\"` is just a \"national prefix\" and no \"national (significant) number\"\r\n\t// digits have been input yet. Still, return `\"+1\"` as `value` in such cases,\r\n\t// because otherwise the app would think that the input is empty and mark it as such\r\n\t// while in reality it isn't empty, which might be thought of as a \"bug\", or just\r\n\t// a \"weird\" behavior.\r\n\t//\r\n\t// if (partial_national_significant_number) {\r\n\t\treturn `+${getCountryCallingCode(country, metadata)}${partial_national_significant_number || ''}`\r\n\t// }\r\n}\r\n\r\n/**\r\n * Trims phone number digits if they exceed the maximum possible length\r\n * for a national (significant) number for the country.\r\n * @param  {string} number - A possibly incomplete phone number digits string. Can be a possibly incomplete E.164 phone number.\r\n * @param  {string} country\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string} Can be empty.\r\n */\r\nexport function trimNumber(number, country, metadata) {\r\n\tconst nationalSignificantNumberPart = getNationalSignificantNumberDigits(number, country, metadata)\r\n\tif (nationalSignificantNumberPart) {\r\n\t\tconst overflowDigitsCount = nationalSignificantNumberPart.length - getMaxNumberLength(country, metadata)\r\n\t\tif (overflowDigitsCount > 0) {\r\n\t\t\treturn number.slice(0, number.length - overflowDigitsCount)\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}\r\n\r\nfunction getMaxNumberLength(country, metadata) {\r\n\t// Get \"possible lengths\" for a phone number of the country.\r\n\tmetadata = new Metadata(metadata)\r\n\tmetadata.selectNumberingPlan(country)\r\n\t// Return the last \"possible length\".\r\n\treturn metadata.numberingPlan.possibleLengths()[metadata.numberingPlan.possibleLengths().length - 1]\r\n}\r\n\r\n// If the phone number being input is an international one\r\n// then tries to derive the country from the phone number.\r\n// (regardless of whether there's any country currently selected)\r\n/**\r\n * @param {string} partialE164Number - A possibly incomplete E.164 phone number.\r\n * @param {string?} country - Currently selected country.\r\n * @param {string[]?} countries - A list of available countries. If not passed then \"all countries\" are assumed.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function getCountryForPartialE164Number(partialE164Number, {\r\n\tcountry,\r\n\tcountries,\r\n\trequired,\r\n\tmetadata\r\n}) {\r\n\tif (partialE164Number === '+') {\r\n\t\t// Don't change the currently selected country yet.\r\n\t\treturn country\r\n\t}\r\n\r\n\tconst derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(partialE164Number, metadata)\r\n\r\n\t// If a phone number is being input in international form\r\n\t// and the country can already be derived from it,\r\n\t// then select that country.\r\n\tif (derived_country && (!countries || (countries.indexOf(derived_country) >= 0))) {\r\n\t\treturn derived_country\r\n\t}\r\n\t// If \"International\" country option has not been disabled\r\n\t// and the international phone number entered doesn't correspond\r\n\t// to the currently selected country then reset the currently selected country.\r\n\telse if (country &&\r\n\t\t!required &&\r\n\t\t!couldNumberBelongToCountry(partialE164Number, country, metadata)) {\r\n\t\treturn undefined\r\n\t}\r\n\r\n\t// Don't change the currently selected country.\r\n\treturn country\r\n}\r\n\r\n/**\r\n * Parses `<input/>` value. Derives `country` from `input`. Derives an E.164 `value`.\r\n * @param  {string?} phoneDigits — Parsed `<input/>` value. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n * @param  {string?} prevPhoneDigits — Previous parsed `<input/>` value. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n * @param  {string?} country - Currently selected country.\r\n * @param  {boolean} countryRequired - Is selecting some country required.\r\n * @param  {function} getAnyCountry - Can be used to get any country when selecting some country required.\r\n * @param  {string[]?} countries - A list of available countries. If not passed then \"all countries\" are assumed.\r\n * @param  {boolean} international - Set to `true` to force international phone number format (leading `+`). Set to `false` to force \"national\" phone number format. Is `undefined` by default.\r\n * @param  {boolean} limitMaxLength — Whether to enable limiting phone number max length.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {object} An object of shape `{ input, country, value }`.\r\n */\r\nexport function onPhoneDigitsChange(phoneDigits, {\r\n\tprevPhoneDigits,\r\n\tcountry,\r\n\tdefaultCountry,\r\n\tcountryRequired,\r\n\tgetAnyCountry,\r\n\tcountries,\r\n\tinternational,\r\n\tlimitMaxLength,\r\n\tcountryCallingCodeEditable,\r\n\tmetadata\r\n}) {\r\n\tif (international && countryCallingCodeEditable === false) {\r\n\t\tif (country) {\r\n\t\t\t// For international phone numbers written with non-editable country calling code,\r\n\t\t\t// the `<input/>` value must always start with that non-editable country calling code.\r\n\t\t\tconst prefix = getInternationalPhoneNumberPrefix(country, metadata)\r\n\t\t\t// If the input value doesn't start with the non-editable country calling code,\r\n\t\t\t// it should be fixed.\r\n\t\t\tif (phoneDigits.indexOf(prefix) !== 0) {\r\n\t\t\t\tlet value\r\n\t\t\t\t// If a phone number input is declared as\r\n\t\t\t\t// `international: true` and `countryCallingCodeEditable: false`,\r\n\t\t\t\t// then the value of the `<input/>` is gonna be non-empty at all times,\r\n\t\t\t\t// even before the user has started to input any digits in the input field,\r\n\t\t\t\t// because the country calling code is always there by design.\r\n\t\t\t\t//\r\n\t\t\t\t// The fact that the input value is always non-empty results in a side effect:\r\n\t\t\t\t// whenever a user tabs into such input field, its value gets automatically selected.\r\n\t\t\t\t// If at that moment in time the user starts typing in the national digits of the phone number,\r\n\t\t\t\t// the selected `<input/>` value gets automatically replaced by those typed-in digits\r\n\t\t\t\t// so the value changes from `+xxx` to `y`, because inputting anything while having\r\n\t\t\t\t// the `<input/>` value selected results in erasing that `<input/>` value.\r\n\t\t\t\t//\r\n\t\t\t\t// This component handles such cases by restoring the `<input/>` value to what\r\n\t\t\t\t// it should be in such cases: `+xxxy`.\r\n\t\t\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/43\r\n\t\t\t\t//\r\n\t\t\t\tconst hasStartedTypingInNationalNumberDigitsHavingInputValueSelected = phoneDigits && phoneDigits[0] !== '+'\r\n\t\t\t\tif (hasStartedTypingInNationalNumberDigitsHavingInputValueSelected) {\r\n\t\t\t\t\t// Fix the input value to what it should be: `y` → `+xxxy`.\r\n\t\t\t\t\tphoneDigits = prefix + phoneDigits\r\n\t\t\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// In other cases, simply reset the `<input/>` value, because there're only two\r\n\t\t\t\t\t// possible cases:\r\n\t\t\t\t\t// * The user has selected the `<input/>` value and then hit Delete/Backspace to erase it.\r\n\t\t\t\t\t// * The user has pasted an international phone number for another country calling code,\r\n\t\t\t\t\t//   which is considered a non-valid value.\r\n\t\t\t\t\tphoneDigits = prefix\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\tphoneDigits,\r\n\t\t\t\t\tvalue,\r\n\t\t\t\t\tcountry\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If `international` property is `false`, then it means\r\n\t// \"enforce national-only format during input\",\r\n\t// so, if that's the case, then remove all `+` characters,\r\n\t// but only if some country is currently selected.\r\n\t// (not if \"International\" country is selected).\r\n\tif (international === false && country && phoneDigits && phoneDigits[0] === '+') {\r\n\t\tphoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata)\r\n\t}\r\n\r\n\t// Trim the input to not exceed the maximum possible number length.\r\n\tif (phoneDigits && country && limitMaxLength) {\r\n\t\tphoneDigits = trimNumber(phoneDigits, country, metadata)\r\n\t}\r\n\r\n\t// If this `onChange()` event was triggered\r\n\t// as a result of selecting \"International\" country,\r\n\t// then force-prepend a `+` sign if the phone number\r\n\t// `<input/>` value isn't in international format.\r\n\t// Also, force-prepend a `+` sign if international\r\n\t// phone number input format is set.\r\n\tif (phoneDigits && phoneDigits[0] !== '+' && (!country || international)) {\r\n\t\tphoneDigits = '+' + phoneDigits\r\n\t}\r\n\r\n\t// If the previously entered phone number\r\n\t// has been entered in international format\r\n\t// and the user decides to erase it,\r\n\t// then also reset the `country`\r\n\t// because it was most likely automatically selected\r\n\t// while the user was typing in the phone number\r\n\t// in international format.\r\n\t// This fixes the issue when a user is presented\r\n\t// with a phone number input with no country selected\r\n\t// and then types in their local phone number\r\n\t// then discovers that the input's messed up\r\n\t// (a `+` has been prepended at the start of their input\r\n\t//  and a random country has been selected),\r\n\t// decides to undo it all by erasing everything\r\n\t// and then types in their local phone number again\r\n\t// resulting in a seemingly correct phone number\r\n\t// but in reality that phone number has incorrect country.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/273\r\n\tif (!phoneDigits && prevPhoneDigits && prevPhoneDigits[0] === '+') {\r\n\t\tif (international) {\r\n\t\t\tcountry = undefined\r\n\t\t} else {\r\n\t\t\tcountry = defaultCountry\r\n\t\t}\r\n\t}\r\n\t// Also resets such \"randomly\" selected country\r\n\t// as soon as the user erases the number\r\n\t// digit-by-digit up to the leading `+` sign.\r\n\tif (phoneDigits === '+' && prevPhoneDigits && prevPhoneDigits[0] === '+' && prevPhoneDigits.length > '+'.length) {\r\n\t\tcountry = undefined\r\n\t}\r\n\r\n\t// Generate the new `value` property.\r\n\tlet value\r\n\tif (phoneDigits) {\r\n\t\tif (phoneDigits[0] === '+') {\r\n\t\t\tif (phoneDigits === '+') {\r\n\t\t\t\tvalue = undefined\r\n\t\t\t} else if (country && getInternationalPhoneNumberPrefix(country, metadata).indexOf(phoneDigits) === 0) {\r\n\t\t\t\t// Selected a `country` and started inputting an\r\n\t\t\t\t// international phone number for this country\r\n\t\t\t\t// but hasn't input any \"national (significant) number\" digits yet.\r\n\t\t\t\t// In that case, assume `value` be `undefined`.\r\n\t\t\t\t//\r\n\t\t\t\t// For example, if selected `country` `\"US\"`\r\n\t\t\t\t// and started inputting phone number `\"+1\"`\r\n\t\t\t\t// then `value` `undefined` will be returned from this function.\r\n\t\t\t\t//\r\n\t\t\t\tvalue = undefined\r\n\t\t\t} else {\r\n\t\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t}\r\n\t}\r\n\r\n\t// Derive the country from the phone number.\r\n\t// (regardless of whether there's any country currently selected,\r\n\t//  because there could be several countries corresponding to one country calling code)\r\n\tif (value) {\r\n\t\tcountry = getCountryForPartialE164Number(value, {\r\n\t\t\tcountry,\r\n\t\t\tcountries,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\t// If `international` property is `false`, then it means\r\n\t\t// \"enforce national-only format during input\",\r\n\t\t// so, if that's the case, then remove all `+` characters,\r\n\t\t// but only if some country is currently selected.\r\n\t\t// (not if \"International\" country is selected).\r\n\t\tif (international === false && country && phoneDigits && phoneDigits[0] === '+') {\r\n\t\t\tphoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata)\r\n\t\t\t// Re-calculate `value` because `phoneDigits` has changed.\r\n\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t}\r\n\t}\r\n\r\n\tif (!country && countryRequired) {\r\n\t\tcountry = defaultCountry || getAnyCountry()\r\n\t}\r\n\r\n\treturn {\r\n\t\tphoneDigits,\r\n\t\tcountry,\r\n\t\tvalue\r\n\t}\r\n}\r\n\r\nfunction convertInternationalPhoneDigitsToNational(input, country, metadata) {\r\n\t// Handle the case when a user might have pasted\r\n\t// a phone number in international format.\r\n\tif (input.indexOf(getInternationalPhoneNumberPrefix(country, metadata)) === 0) {\r\n\t\t// Create \"as you type\" formatter.\r\n\t\tconst formatter = new AsYouType(country, metadata)\r\n\t\t// Input partial national phone number.\r\n\t\tformatter.input(input)\r\n\t\t// Return the parsed partial national phone number.\r\n\t\tconst phoneNumber = formatter.getNumber()\r\n\t\tif (phoneNumber) {\r\n\t\t\t// Transform the number to a national one,\r\n\t\t\t// and remove all non-digits.\r\n\t\t\treturn phoneNumber.formatNational().replace(/\\D/g, '')\r\n\t\t} else {\r\n\t\t\treturn ''\r\n\t\t}\r\n\t} else {\r\n\t\t// Just remove the `+` sign.\r\n\t\treturn input.replace(/\\D/g, '')\r\n\t}\r\n}\r\n\r\n/**\r\n * Determines the country for a given (possibly incomplete) E.164 phone number.\r\n * @param  {string} number - A possibly incomplete E.164 phone number.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function getCountryFromPossiblyIncompleteInternationalPhoneNumber(number, metadata) {\r\n\tconst formatter = new AsYouType(null, metadata)\r\n\tformatter.input(number)\r\n\t// // `001` is a special \"non-geograpical entity\" code\r\n\t// // in Google's `libphonenumber` library.\r\n\t// if (formatter.getCountry() === '001') {\r\n\t// \treturn\r\n\t// }\r\n\treturn formatter.getCountry()\r\n}\r\n\r\n/**\r\n * Compares two strings.\r\n * A helper for `Array.sort()`.\r\n * @param {string} a — First string.\r\n * @param {string} b — Second string.\r\n * @param {(string[]|string)} [locales] — The `locales` argument of `String.localeCompare`.\r\n */\r\nexport function compareStrings(a, b, locales) {\r\n  // Use `String.localeCompare` if it's available.\r\n  // https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Global_Objects/String/localeCompare\r\n  // Which means everyone except IE <= 10 and Safari <= 10.\r\n  // `localeCompare()` is available in latest Node.js versions.\r\n  /* istanbul ignore else */\r\n  if (String.prototype.localeCompare) {\r\n    return a.localeCompare(b, locales);\r\n  }\r\n  /* istanbul ignore next */\r\n  return a < b ? -1 : (a > b ? 1 : 0);\r\n}\r\n\r\n/**\r\n * Strips `+${countryCallingCode}` prefix from an E.164 phone number.\r\n * @param {string} number - (possibly incomplete) E.164 phone number.\r\n * @param {string?} country - A possible country for this phone number.\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string}\r\n */\r\nexport function stripCountryCallingCode(number, country, metadata) {\r\n\t// Just an optimization, so that it\r\n\t// doesn't have to iterate through all country calling codes.\r\n\tif (country) {\r\n\t\tconst countryCallingCodePrefix = '+' + getCountryCallingCode(country, metadata)\r\n\r\n\t\t// If `country` fits the actual `number`.\r\n\t\tif (number.length < countryCallingCodePrefix.length) {\r\n\t\t\tif (countryCallingCodePrefix.indexOf(number) === 0) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (number.indexOf(countryCallingCodePrefix) === 0) {\r\n\t\t\t\treturn number.slice(countryCallingCodePrefix.length)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If `country` doesn't fit the actual `number`.\r\n\t// Try all available country calling codes.\r\n\tfor (const country_calling_code of Object.keys(metadata.country_calling_codes)) {\r\n\t\tif (number.indexOf(country_calling_code) === '+'.length) {\r\n\t\t\treturn number.slice('+'.length + country_calling_code.length)\r\n\t\t}\r\n\t}\r\n\r\n\treturn ''\r\n}\r\n\r\n/**\r\n * Parses a partially entered national phone number digits\r\n * (or a partially entered E.164 international phone number)\r\n * and returns the national significant number part.\r\n * National significant number returned doesn't come with a national prefix.\r\n * @param {string} number - National number digits. Or possibly incomplete E.164 phone number.\r\n * @param {string?} country\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string} [result]\r\n */\r\nexport function getNationalSignificantNumberDigits(number, country, metadata) {\r\n\t// Create \"as you type\" formatter.\r\n\tconst formatter = new AsYouType(country, metadata)\r\n\t// Input partial national phone number.\r\n\tformatter.input(number)\r\n\t// Return the parsed partial national phone number.\r\n\tconst phoneNumber = formatter.getNumber()\r\n\treturn phoneNumber && phoneNumber.nationalNumber\r\n}\r\n\r\n/**\r\n * Checks if a partially entered E.164 phone number could belong to a country.\r\n * @param  {string} number\r\n * @param  {string} country\r\n * @return {boolean}\r\n */\r\nexport function couldNumberBelongToCountry(number, country, metadata) {\r\n\tconst intlPhoneNumberPrefix = getInternationalPhoneNumberPrefix(country, metadata)\r\n\tlet i = 0\r\n\twhile (i < number.length && i < intlPhoneNumberPrefix.length) {\r\n\t\tif (number[i] !== intlPhoneNumberPrefix[i]) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n\r\n/**\r\n * Gets initial \"phone digits\" (including `+`, if using international format).\r\n * @return {string} [phoneDigits] Returns `undefined` if there should be no initial \"phone digits\".\r\n */\r\nexport function getInitialPhoneDigits({\r\n\tvalue,\r\n\tphoneNumber,\r\n\tdefaultCountry,\r\n\tinternational,\r\n\tuseNationalFormat,\r\n\tmetadata\r\n}) {\r\n\t// If the `value` (E.164 phone number)\r\n\t// belongs to the currently selected country\r\n\t// and `useNationalFormat` is `true`\r\n\t// then convert `value` (E.164 phone number)\r\n\t// to a local phone number digits.\r\n\t// E.g. '+78005553535' -> '88005553535'.\r\n\tif ((international === false || useNationalFormat) && phoneNumber && phoneNumber.country) {\r\n\t\treturn generateNationalNumberDigits(phoneNumber)\r\n\t}\r\n\t// If `international` property is `true`,\r\n\t// meaning \"enforce international phone number format\",\r\n\t// then always show country calling code in the input field.\r\n\tif (!value && international && defaultCountry) {\r\n\t\treturn getInternationalPhoneNumberPrefix(defaultCountry, metadata)\r\n\t}\r\n\treturn value\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAMA;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,qBAAT,OAQJ;EAAA,IAPFC,KAOE,QAPFA,KAOE;EAAA,IANFC,WAME,QANFA,WAME;EAAA,IALFC,cAKE,QALFA,cAKE;EAAA,IAJFC,aAIE,QAJFA,aAIE;EAAA,IAHFC,SAGE,QAHFA,SAGE;EAAA,IAFFC,QAEE,QAFFA,QAEE;EAAA,IADFC,QACE,QADFA,QACE;EACF,IAAIC,OAAJ,CADE,CAGF;EACA;;EACA,IAAIN,WAAW,IAAIA,WAAW,CAACM,OAA/B,EAAwC;IACvC;IACAA,OAAO,GAAGN,WAAW,CAACM,OAAtB;EACA,CAHD,MAGO,IAAIL,cAAJ,EAAoB;IAC1B,IAAI,CAACF,KAAD,IAAUQ,0BAA0B,CAACR,KAAD,EAAQE,cAAR,EAAwBI,QAAxB,CAAxC,EAA2E;MAC1EC,OAAO,GAAGL,cAAV;IACA;EACD,CAZC,CAcF;;;EACA,IAAIE,SAAS,IAAIA,SAAS,CAACK,OAAV,CAAkBF,OAAlB,IAA6B,CAA9C,EAAiD;IAChDA,OAAO,GAAGG,SAAV;EACA,CAjBC,CAmBF;EACA;EACA;EACA;;;EACA,IAAI,CAACH,OAAD,IAAYF,QAAZ,IAAwBD,SAAxB,IAAqCA,SAAS,CAACO,MAAV,GAAmB,CAA5D,EAA+D;IAC9DJ,OAAO,GAAGJ,aAAa,EAAvB,CAD8D,CAE9D;EACA;;EAED,OAAOI,OAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASK,uBAAT,QAQJ;EAAA,IAPFR,SAOE,SAPFA,SAOE;EAAA,IANFS,YAME,SANFA,YAME;EAAA,IALFC,sBAKE,SALFA,sBAKE;EAAA,IAFFC,qBAEE,SAFFA,qBAEE;EAAA,IADcC,eACd,SADFC,cACE;;EACF;EACA,IAAI,CAACD,eAAL,EAAsB;IACrBA,eAAe,GAAGC,cAAlB;EACA,CAJC,CAMF;;;EACA,IAAMC,oBAAoB,GAAGd,SAAS,CAACe,GAAV,CAAc,UAACZ,OAAD;IAAA,OAAc;MACxDP,KAAK,EAAEO,OADiD;MAExD;MACA;MACA;MACA;MACA;MACA;MACAa,KAAK,EAAEP,YAAY,CAACN,OAAD,CAAZ,IAAyBA;IARwB,CAAd;EAAA,CAAd,CAA7B,CAPE,CAkBF;;EACAW,oBAAoB,CAACG,IAArB,CAA0B,UAACC,CAAD,EAAIC,CAAJ;IAAA,OAAUP,eAAe,CAACM,CAAC,CAACF,KAAH,EAAUG,CAAC,CAACH,KAAZ,EAAmBL,qBAAnB,CAAzB;EAAA,CAA1B,EAnBE,CAqBF;;EACA,IAAID,sBAAJ,EAA4B;IAC3BI,oBAAoB,CAACM,OAArB,CAA6B;MAC5BJ,KAAK,EAAEP,YAAY,CAACY;IADQ,CAA7B;EAGA;;EAED,OAAOP,oBAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASQ,gBAAT,CAA0B1B,KAA1B,EAAiCM,QAAjC,EAA2C;EACjD,OAAO,IAAAqB,gBAAA,EAAkB3B,KAAK,IAAI,EAA3B,EAA+BM,QAA/B,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASsB,4BAAT,CAAsC3B,WAAtC,EAAmD;EACzD,OAAOA,WAAW,CAAC4B,cAAZ,GAA6BC,OAA7B,CAAqC,KAArC,EAA4C,EAA5C,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASC,2BAAT,CAAqCC,WAArC,SAKJ;EAAA,IAJFC,WAIE,SAJFA,WAIE;EAAA,IAHFC,UAGE,SAHFA,UAGE;EAAA,IAFF5B,QAEE,SAFFA,QAEE;EAAA,IADF6B,iBACE,SADFA,iBACE;;EACF,IAAIF,WAAW,KAAKC,UAApB,EAAgC;IAC/B,OAAOF,WAAP;EACA,CAHC,CAKF;EACA;;;EACA,IAAI,CAACA,WAAL,EAAkB;IACjB,IAAIG,iBAAJ,EAAuB;MACtB,OAAO,EAAP;IACA,CAFD,MAEO;MACN,IAAID,UAAJ,EAAgB;QACf;QACA;QACA,OAAO,IAAAE,6CAAA,EAAkCF,UAAlC,EAA8C5B,QAA9C,CAAP;MACA;;MACD,OAAO,EAAP;IACA;EACD,CAlBC,CAoBF;EACA;EACA;EACA;;;EACA,IAAI4B,UAAJ,EAAgB;IACf;IACA;IACA;IACA;IACA;IACA,IAAIF,WAAW,CAAC,CAAD,CAAX,KAAmB,GAAvB,EAA4B;MAC3B;MACA;MACA,IAAIG,iBAAJ,EAAuB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIH,WAAW,CAACvB,OAAZ,CAAoB,MAAM,IAAA4B,2BAAA,EAAsBH,UAAtB,EAAkC5B,QAAlC,CAA1B,MAA2E,CAA/E,EAAkF;UACjF,OAAOgC,uBAAuB,CAACN,WAAD,EAAcE,UAAd,EAA0B5B,QAA1B,CAA9B;QACA,CAjBqB,CAmBtB;QACA;QACA;QACA;QACA;;;QACA,OAAO,EAAP,CAxBsB,CA0BtB;QACA;QACA;QACA;MACA;;MAED,IAAI2B,WAAJ,EAAiB;QAChB,IAAMM,gBAAgB,GAAG,IAAAH,6CAAA,EAAkCF,UAAlC,EAA8C5B,QAA9C,CAAzB;;QACA,IAAI0B,WAAW,CAACvB,OAAZ,CAAoB8B,gBAApB,MAA0C,CAA9C,EAAiD;UAChD,OAAOP,WAAP;QACA,CAFD,MAEO;UACN,OAAOO,gBAAP;QACA;MACD,CAPD,MAOO;QACN,IAAMC,YAAY,GAAG,IAAAJ,6CAAA,EAAkCF,UAAlC,EAA8C5B,QAA9C,CAArB,CADM,CAEN;QACA;;QACA,IAAI0B,WAAW,CAACvB,OAAZ,CAAoB+B,YAApB,MAAsC,CAA1C,EAA6C;UAC5C,OAAOR,WAAP;QACA,CANK,CAON;QACA;QACA;;;QACA,OAAOQ,YAAP;MACA,CArD0B,CAuD3B;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;;IACA;EACD,CAzED,CA0EA;EA1EA,KA2EK;IACJ;IACA,IAAIR,WAAW,CAAC,CAAD,CAAX,KAAmB,GAAvB,EAA4B;MAC3B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA;MACA,OAAOS,IAAI,CAACT,WAAD,EAAcC,WAAd,EAA2B3B,QAA3B,CAAJ,IAA4C,EAAnD;IACA;EACD;;EAED,OAAO0B,WAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASS,IAAT,CAAcC,MAAd,EAAsBnC,OAAtB,EAA+BD,QAA/B,EAAyC;EAC/C,IAAI,CAACoC,MAAL,EAAa;IACZ;EACA,CAH8C,CAI/C;;;EACA,IAAIA,MAAM,CAAC,CAAD,CAAN,KAAc,GAAlB,EAAuB;IACtB;IACA,IAAIA,MAAM,KAAK,GAAf,EAAoB;MACnB;IACA,CAJqB,CAKtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAMC,SAAS,GAAG,IAAIC,eAAJ,CAAcrC,OAAd,EAAuBD,QAAvB,CAAlB;IACAqC,SAAS,CAACE,KAAV,CAAgBH,MAAhB,EAdsB,CAetB;IACA;;IACA,OAAOC,SAAS,CAACG,cAAV,EAAP;EACA,CAvB8C,CAwB/C;EACA;EACA;EACA;EACA;;;EACA,IAAI,CAACvC,OAAL,EAAc;IACb;EACA;;EACD,IAAMwC,mCAAmC,GAAGC,kCAAkC,CAACN,MAAD,EAASnC,OAAT,EAAkBD,QAAlB,CAA9E,CAhC+C,CAiC/C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACC,kBAAW,IAAA+B,2BAAA,EAAsB9B,OAAtB,EAA+BD,QAA/B,CAAX,SAAsDyC,mCAAmC,IAAI,EAA7F,EA9C8C,CA+C/C;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASE,UAAT,CAAoBP,MAApB,EAA4BnC,OAA5B,EAAqCD,QAArC,EAA+C;EACrD,IAAM4C,6BAA6B,GAAGF,kCAAkC,CAACN,MAAD,EAASnC,OAAT,EAAkBD,QAAlB,CAAxE;;EACA,IAAI4C,6BAAJ,EAAmC;IAClC,IAAMC,mBAAmB,GAAGD,6BAA6B,CAACvC,MAA9B,GAAuCyC,kBAAkB,CAAC7C,OAAD,EAAUD,QAAV,CAArF;;IACA,IAAI6C,mBAAmB,GAAG,CAA1B,EAA6B;MAC5B,OAAOT,MAAM,CAACW,KAAP,CAAa,CAAb,EAAgBX,MAAM,CAAC/B,MAAP,GAAgBwC,mBAAhC,CAAP;IACA;EACD;;EACD,OAAOT,MAAP;AACA;;AAED,SAASU,kBAAT,CAA4B7C,OAA5B,EAAqCD,QAArC,EAA+C;EAC9C;EACAA,QAAQ,GAAG,IAAIgD,cAAJ,CAAahD,QAAb,CAAX;EACAA,QAAQ,CAACiD,mBAAT,CAA6BhD,OAA7B,EAH8C,CAI9C;;EACA,OAAOD,QAAQ,CAACkD,aAAT,CAAuBC,eAAvB,GAAyCnD,QAAQ,CAACkD,aAAT,CAAuBC,eAAvB,GAAyC9C,MAAzC,GAAkD,CAA3F,CAAP;AACA,C,CAED;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAAS+C,8BAAT,CAAwCC,iBAAxC,SAKJ;EAAA,IAJFpD,OAIE,SAJFA,OAIE;EAAA,IAHFH,SAGE,SAHFA,SAGE;EAAA,IAFFC,QAEE,SAFFA,QAEE;EAAA,IADFC,QACE,SADFA,QACE;;EACF,IAAIqD,iBAAiB,KAAK,GAA1B,EAA+B;IAC9B;IACA,OAAOpD,OAAP;EACA;;EAED,IAAMqD,eAAe,GAAGC,wDAAwD,CAACF,iBAAD,EAAoBrD,QAApB,CAAhF,CANE,CAQF;EACA;EACA;;EACA,IAAIsD,eAAe,KAAK,CAACxD,SAAD,IAAeA,SAAS,CAACK,OAAV,CAAkBmD,eAAlB,KAAsC,CAA1D,CAAnB,EAAkF;IACjF,OAAOA,eAAP;EACA,CAFD,CAGA;EACA;EACA;EALA,KAMK,IAAIrD,OAAO,IACf,CAACF,QADO,IAER,CAACG,0BAA0B,CAACmD,iBAAD,EAAoBpD,OAApB,EAA6BD,QAA7B,CAFvB,EAE+D;IACnE,OAAOI,SAAP;EACA,CArBC,CAuBF;;;EACA,OAAOH,OAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASuD,mBAAT,CAA6B9B,WAA7B,SAWJ;EAAA,IAVF+B,eAUE,SAVFA,eAUE;EAAA,IATFxD,OASE,SATFA,OASE;EAAA,IARFL,cAQE,SARFA,cAQE;EAAA,IAPF8D,eAOE,SAPFA,eAOE;EAAA,IANF7D,aAME,SANFA,aAME;EAAA,IALFC,SAKE,SALFA,SAKE;EAAA,IAJF6D,aAIE,SAJFA,aAIE;EAAA,IAHFC,cAGE,SAHFA,cAGE;EAAA,IAFFC,0BAEE,SAFFA,0BAEE;EAAA,IADF7D,QACE,SADFA,QACE;;EACF,IAAI2D,aAAa,IAAIE,0BAA0B,KAAK,KAApD,EAA2D;IAC1D,IAAI5D,OAAJ,EAAa;MACZ;MACA;MACA,IAAM6D,MAAM,GAAG,IAAAhC,6CAAA,EAAkC7B,OAAlC,EAA2CD,QAA3C,CAAf,CAHY,CAIZ;MACA;;MACA,IAAI0B,WAAW,CAACvB,OAAZ,CAAoB2D,MAApB,MAAgC,CAApC,EAAuC;QACtC,IAAIpE,MAAJ,CADsC,CAEtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;;QACA,IAAMqE,8DAA8D,GAAGrC,WAAW,IAAIA,WAAW,CAAC,CAAD,CAAX,KAAmB,GAAzG;;QACA,IAAIqC,8DAAJ,EAAoE;UACnE;UACArC,WAAW,GAAGoC,MAAM,GAAGpC,WAAvB;UACAhC,MAAK,GAAGyC,IAAI,CAACT,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAZ;QACA,CAJD,MAIO;UACN;UACA;UACA;UACA;UACA;UACA0B,WAAW,GAAGoC,MAAd;QACA;;QACD,OAAO;UACNpC,WAAW,EAAXA,WADM;UAENhC,KAAK,EAALA,MAFM;UAGNO,OAAO,EAAPA;QAHM,CAAP;MAKA;IACD;EACD,CA/CC,CAiDF;EACA;EACA;EACA;EACA;;;EACA,IAAI0D,aAAa,KAAK,KAAlB,IAA2B1D,OAA3B,IAAsCyB,WAAtC,IAAqDA,WAAW,CAAC,CAAD,CAAX,KAAmB,GAA5E,EAAiF;IAChFA,WAAW,GAAGsC,yCAAyC,CAACtC,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAvD;EACA,CAxDC,CA0DF;;;EACA,IAAI0B,WAAW,IAAIzB,OAAf,IAA0B2D,cAA9B,EAA8C;IAC7ClC,WAAW,GAAGiB,UAAU,CAACjB,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAxB;EACA,CA7DC,CA+DF;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAI0B,WAAW,IAAIA,WAAW,CAAC,CAAD,CAAX,KAAmB,GAAlC,KAA0C,CAACzB,OAAD,IAAY0D,aAAtD,CAAJ,EAA0E;IACzEjC,WAAW,GAAG,MAAMA,WAApB;EACA,CAvEC,CAyEF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAI,CAACA,WAAD,IAAgB+B,eAAhB,IAAmCA,eAAe,CAAC,CAAD,CAAf,KAAuB,GAA9D,EAAmE;IAClE,IAAIE,aAAJ,EAAmB;MAClB1D,OAAO,GAAGG,SAAV;IACA,CAFD,MAEO;MACNH,OAAO,GAAGL,cAAV;IACA;EACD,CAjGC,CAkGF;EACA;EACA;;;EACA,IAAI8B,WAAW,KAAK,GAAhB,IAAuB+B,eAAvB,IAA0CA,eAAe,CAAC,CAAD,CAAf,KAAuB,GAAjE,IAAwEA,eAAe,CAACpD,MAAhB,GAAyB,IAAIA,MAAzG,EAAiH;IAChHJ,OAAO,GAAGG,SAAV;EACA,CAvGC,CAyGF;;;EACA,IAAIV,KAAJ;;EACA,IAAIgC,WAAJ,EAAiB;IAChB,IAAIA,WAAW,CAAC,CAAD,CAAX,KAAmB,GAAvB,EAA4B;MAC3B,IAAIA,WAAW,KAAK,GAApB,EAAyB;QACxBhC,KAAK,GAAGU,SAAR;MACA,CAFD,MAEO,IAAIH,OAAO,IAAI,IAAA6B,6CAAA,EAAkC7B,OAAlC,EAA2CD,QAA3C,EAAqDG,OAArD,CAA6DuB,WAA7D,MAA8E,CAA7F,EAAgG;QACtG;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAhC,KAAK,GAAGU,SAAR;MACA,CAXM,MAWA;QACNV,KAAK,GAAGyC,IAAI,CAACT,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAZ;MACA;IACD,CAjBD,MAiBO;MACNN,KAAK,GAAGyC,IAAI,CAACT,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAZ;IACA;EACD,CAhIC,CAkIF;EACA;EACA;;;EACA,IAAIN,KAAJ,EAAW;IACVO,OAAO,GAAGmD,8BAA8B,CAAC1D,KAAD,EAAQ;MAC/CO,OAAO,EAAPA,OAD+C;MAE/CH,SAAS,EAATA,SAF+C;MAG/CE,QAAQ,EAARA;IAH+C,CAAR,CAAxC,CADU,CAMV;IACA;IACA;IACA;IACA;;IACA,IAAI2D,aAAa,KAAK,KAAlB,IAA2B1D,OAA3B,IAAsCyB,WAAtC,IAAqDA,WAAW,CAAC,CAAD,CAAX,KAAmB,GAA5E,EAAiF;MAChFA,WAAW,GAAGsC,yCAAyC,CAACtC,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAvD,CADgF,CAEhF;;MACAN,KAAK,GAAGyC,IAAI,CAACT,WAAD,EAAczB,OAAd,EAAuBD,QAAvB,CAAZ;IACA;EACD;;EAED,IAAI,CAACC,OAAD,IAAYyD,eAAhB,EAAiC;IAChCzD,OAAO,GAAGL,cAAc,IAAIC,aAAa,EAAzC;EACA;;EAED,OAAO;IACN6B,WAAW,EAAXA,WADM;IAENzB,OAAO,EAAPA,OAFM;IAGNP,KAAK,EAALA;EAHM,CAAP;AAKA;;AAED,SAASsE,yCAAT,CAAmDzB,KAAnD,EAA0DtC,OAA1D,EAAmED,QAAnE,EAA6E;EAC5E;EACA;EACA,IAAIuC,KAAK,CAACpC,OAAN,CAAc,IAAA2B,6CAAA,EAAkC7B,OAAlC,EAA2CD,QAA3C,CAAd,MAAwE,CAA5E,EAA+E;IAC9E;IACA,IAAMiE,SAAS,GAAG,IAAI3B,eAAJ,CAAcrC,OAAd,EAAuBD,QAAvB,CAAlB,CAF8E,CAG9E;;IACAiE,SAAS,CAAC1B,KAAV,CAAgBA,KAAhB,EAJ8E,CAK9E;;IACA,IAAM5C,WAAW,GAAGsE,SAAS,CAACC,SAAV,EAApB;;IACA,IAAIvE,WAAJ,EAAiB;MAChB;MACA;MACA,OAAOA,WAAW,CAAC4B,cAAZ,GAA6BC,OAA7B,CAAqC,KAArC,EAA4C,EAA5C,CAAP;IACA,CAJD,MAIO;MACN,OAAO,EAAP;IACA;EACD,CAdD,MAcO;IACN;IACA,OAAOe,KAAK,CAACf,OAAN,CAAc,KAAd,EAAqB,EAArB,CAAP;EACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACO,SAAS+B,wDAAT,CAAkEnB,MAAlE,EAA0EpC,QAA1E,EAAoF;EAC1F,IAAMiE,SAAS,GAAG,IAAI3B,eAAJ,CAAc,IAAd,EAAoBtC,QAApB,CAAlB;EACAiE,SAAS,CAAC1B,KAAV,CAAgBH,MAAhB,EAF0F,CAG1F;EACA;EACA;EACA;EACA;;EACA,OAAO6B,SAAS,CAACE,UAAV,EAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASxD,cAAT,CAAwBK,CAAxB,EAA2BC,CAA3B,EAA8BmD,OAA9B,EAAuC;EAC5C;EACA;EACA;EACA;;EACA;EACA,IAAIC,MAAM,CAACC,SAAP,CAAiBC,aAArB,EAAoC;IAClC,OAAOvD,CAAC,CAACuD,aAAF,CAAgBtD,CAAhB,EAAmBmD,OAAnB,CAAP;EACD;EACD;;;EACA,OAAOpD,CAAC,GAAGC,CAAJ,GAAQ,CAAC,CAAT,GAAcD,CAAC,GAAGC,CAAJ,GAAQ,CAAR,GAAY,CAAjC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASe,uBAAT,CAAiCI,MAAjC,EAAyCnC,OAAzC,EAAkDD,QAAlD,EAA4D;EAClE;EACA;EACA,IAAIC,OAAJ,EAAa;IACZ,IAAMuE,wBAAwB,GAAG,MAAM,IAAAzC,2BAAA,EAAsB9B,OAAtB,EAA+BD,QAA/B,CAAvC,CADY,CAGZ;;IACA,IAAIoC,MAAM,CAAC/B,MAAP,GAAgBmE,wBAAwB,CAACnE,MAA7C,EAAqD;MACpD,IAAImE,wBAAwB,CAACrE,OAAzB,CAAiCiC,MAAjC,MAA6C,CAAjD,EAAoD;QACnD,OAAO,EAAP;MACA;IACD,CAJD,MAIO;MACN,IAAIA,MAAM,CAACjC,OAAP,CAAeqE,wBAAf,MAA6C,CAAjD,EAAoD;QACnD,OAAOpC,MAAM,CAACW,KAAP,CAAayB,wBAAwB,CAACnE,MAAtC,CAAP;MACA;IACD;EACD,CAhBiE,CAkBlE;EACA;;;EACA,gCAAmCoE,MAAM,CAACC,IAAP,CAAY1E,QAAQ,CAAC2E,qBAArB,CAAnC,kCAAgF;IAA3E,IAAMC,oBAAoB,mBAA1B;;IACJ,IAAIxC,MAAM,CAACjC,OAAP,CAAeyE,oBAAf,MAAyC,IAAIvE,MAAjD,EAAyD;MACxD,OAAO+B,MAAM,CAACW,KAAP,CAAa,IAAI1C,MAAJ,GAAauE,oBAAoB,CAACvE,MAA/C,CAAP;IACA;EACD;;EAED,OAAO,EAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASqC,kCAAT,CAA4CN,MAA5C,EAAoDnC,OAApD,EAA6DD,QAA7D,EAAuE;EAC7E;EACA,IAAMiE,SAAS,GAAG,IAAI3B,eAAJ,CAAcrC,OAAd,EAAuBD,QAAvB,CAAlB,CAF6E,CAG7E;;EACAiE,SAAS,CAAC1B,KAAV,CAAgBH,MAAhB,EAJ6E,CAK7E;;EACA,IAAMzC,WAAW,GAAGsE,SAAS,CAACC,SAAV,EAApB;EACA,OAAOvE,WAAW,IAAIA,WAAW,CAACkF,cAAlC;AACA;AAED;AACA;AACA;AACA;AACA;AACA;;;AACO,SAAS3E,0BAAT,CAAoCkC,MAApC,EAA4CnC,OAA5C,EAAqDD,QAArD,EAA+D;EACrE,IAAM8E,qBAAqB,GAAG,IAAAhD,6CAAA,EAAkC7B,OAAlC,EAA2CD,QAA3C,CAA9B;EACA,IAAI+E,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAG3C,MAAM,CAAC/B,MAAX,IAAqB0E,CAAC,GAAGD,qBAAqB,CAACzE,MAAtD,EAA8D;IAC7D,IAAI+B,MAAM,CAAC2C,CAAD,CAAN,KAAcD,qBAAqB,CAACC,CAAD,CAAvC,EAA4C;MAC3C,OAAO,KAAP;IACA;;IACDA,CAAC;EACD;;EACD,OAAO,IAAP;AACA;AAED;AACA;AACA;AACA;;;AACO,SAASC,qBAAT,QAOJ;EAAA,IANFtF,KAME,SANFA,KAME;EAAA,IALFC,WAKE,SALFA,WAKE;EAAA,IAJFC,cAIE,SAJFA,cAIE;EAAA,IAHF+D,aAGE,SAHFA,aAGE;EAAA,IAFF9B,iBAEE,SAFFA,iBAEE;EAAA,IADF7B,QACE,SADFA,QACE;;EACF;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC2D,aAAa,KAAK,KAAlB,IAA2B9B,iBAA5B,KAAkDlC,WAAlD,IAAiEA,WAAW,CAACM,OAAjF,EAA0F;IACzF,OAAOqB,4BAA4B,CAAC3B,WAAD,CAAnC;EACA,CATC,CAUF;EACA;EACA;;;EACA,IAAI,CAACD,KAAD,IAAUiE,aAAV,IAA2B/D,cAA/B,EAA+C;IAC9C,OAAO,IAAAkC,6CAAA,EAAkClC,cAAlC,EAAkDI,QAAlD,CAAP;EACA;;EACD,OAAON,KAAP;AACA"}