{"name": "flatted", "version": "3.2.7", "description": "A super light and fast circular JSON parser.", "unpkg": "min.js", "types": "types.d.ts", "main": "./cjs/index.js", "scripts": {"build": "npm run cjs && npm run rollup:esm && npm run rollup:es && npm run rollup:babel && npm run min && npm run test && npm run size", "cjs": "ascjs esm cjs", "rollup:es": "rollup --config rollup/es.config.js && sed -i.bck 's/^var /self./' es.js && rm -rf es.js.bck", "rollup:esm": "rollup --config rollup/esm.config.js", "rollup:babel": "rollup --config rollup/babel.config.js && sed -i.bck 's/^var /self./' index.js && rm -rf index.js.bck", "min": "terser index.js -c -m -o min.js", "size": "cat index.js | wc -c;cat min.js | wc -c;gzip -c9 min.js | wc -c;cat min.js | brotli | wc -c; cat es.js | brotli | wc -c; cat esm.js | brotli | wc -c", "test": "c8 node test/index.js", "test:php": "php php/test.php", "coverage": "mkdir -p ./coverage; c8 report --reporter=text-lcov > ./coverage/lcov.info"}, "repository": {"type": "git", "url": "git+https://github.com/WebReflection/flatted.git"}, "keywords": ["circular", "JSON", "fast", "parser", "minimal"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/WebReflection/flatted/issues"}, "homepage": "https://github.com/WebReflection/flatted#readme", "devDependencies": {"@babel/core": "^7.18.10", "@babel/preset-env": "^7.18.10", "@ungap/structured-clone": "^1.0.1", "ascjs": "^5.0.1", "c8": "^7.12.0", "circular-json": "^0.5.9", "circular-json-es6": "^2.0.2", "jsan": "^3.1.14", "rollup": "^2.78.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^7.0.2", "terser": "^5.14.2"}, "module": "./esm/index.js", "type": "module", "exports": {".": {"types": "./types.d.ts", "import": "./esm/index.js", "default": "./cjs/index.js"}, "./esm": "./esm.js", "./package.json": "./package.json"}}