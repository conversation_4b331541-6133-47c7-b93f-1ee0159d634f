import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useSelector } from "react-redux";

// Skeleton component for individual package cards
const SkeletonPackageCard = ({ cols }) => (
  <div className="bg-white shadow-[0_3px_10px_rgb(0,0,0,0.2)]">
    <div className="grid grid-cols-12 px-5 py-3 bg-gray-200">
      <div
        className={`${cols === 3 ? "col-span-12" : "col-span-9"} flex flex-col`}
      >
        <div className="flex flex-row gap-3">
          <Skeleton width={200} height={28} />
          <Skeleton width={100} height={24} />
        </div>
        <Skeleton width={180} height={20} className="mt-2" />
      </div>
      {cols !== 3 && (
        <div className="col-span-3 flex flex-col">
          <Skeleton width={80} height={32} className="ml-auto" />
          <Skeleton width={60} height={16} className="ml-auto mt-1" />
        </div>
      )}
    </div>

    <div className="p-5">
      <div className="space-y-2 mt-5">
        {[1, 2, 3].map((i) => (
          <Skeleton key={i} width="90%" height={16} />
        ))}
      </div>

      <div className="mt-6 space-y-2">
        <Skeleton width="100%" height={16} />
        <Skeleton width="80%" height={16} />
        <Skeleton width="70%" height={16} />
      </div>
    </div>

    <div className="flex flex-wrap gap-4 py-4 px-5">
      {[1, 2, 3, 4].map((i) => (
        <Skeleton key={i} width={100} height={16} />
      ))}
    </div>
  </div>
);

// Main skeleton loading component
const PackagesSkeleton = () => {
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const cols =
    user_package_check === "free"
      ? 3
      : user_package_check === "Enterprise"
      ? 1
      : 2;
  return (
    <SkeletonTheme baseColor="#f3f4f6" highlightColor="#e5e7eb">
      <div
        className={`grid md:grid-cols-${cols} md:gap-4 ${
          cols === 1 && "w-1/2"
        }`}
      >
        {Array.from({ length: cols === 3 ? 3 : cols === 1 ? 1 : 2 }).map(
          (_, i) => (
            <SkeletonPackageCard key={i} cols={cols} />
          )
        )}
      </div>
    </SkeletonTheme>
  );
};

export default PackagesSkeleton;
