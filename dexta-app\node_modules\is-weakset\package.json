{"name": "is-weakset", "version": "2.0.2", "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es6-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "posttest": "aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakset.git"}, "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "homepage": "https://github.com/inspect-js/is-weakset#readme", "devDependencies": {"@ljharb/eslint-config": "^20.0.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "core-js": "^2.6.12", "es5-shim": "^4.6.2", "es6-shim": "^0.35.6", "eslint": "^8.4.1", "for-each": "^0.3.3", "nyc": "^10.3.2", "object-inspect": "^1.11.1", "safe-publish-latest": "^2.0.0", "tape": "^5.3.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}}