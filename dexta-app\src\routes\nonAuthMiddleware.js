import React, { useState, useEffect } from "react";
import { Navigate } from "react-router-dom";
import { isRecruiterAuthenticated } from "../Helpers/Auth";

const NonAuthMiddleware = (props) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        const authResult = await isRecruiterAuthenticated();
        setIsAuthenticated(authResult);
      } catch (error) {
        console.error("Authentication check error:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthentication();
  }, []);

  if (isLoading) {
    return null;
  }

  if (isAuthenticated) {
    if (
      !localStorage.getItem("Registration") &&
      localStorage.getItem("page") !== "settings"
    ) {
      return <Navigate to={{ pathname: "/dashboard" }} />;
    }
  }

  return <React.Fragment>{props.children}</React.Fragment>;
};

export default NonAuthMiddleware;
