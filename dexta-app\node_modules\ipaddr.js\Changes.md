### 2.1.0 - 2023-05-23

- un-deprecate IPv6.toString() and make it an alias to toRFC5952String()
- add reserved **********/15 block
- add reserved blocks in 2001: space


### 2.0.1 - 2020-01-06

- add support for deprecated IPv4 compatible IPv6 addresses #142
- drop node 8 testing, add v14
- fix parseInt for decimal, octal, hex
- add support for classful (2,3 octet) IPv4


### 2.0.0 - 2019-10-13

- use es6 templates instead of concatenated strings
- lint: update tests with no-var
- lint: allow no-cond-assign with extra parens
- es6: replace var with const/let
- update README with es6 examples #125
- replace nodeunit with mocha
- rewrite in JS, drop CoffeeScript

### 1.9.1 - 2019-07-03
