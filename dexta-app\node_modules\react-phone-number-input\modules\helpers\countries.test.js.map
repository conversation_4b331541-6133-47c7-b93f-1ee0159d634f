{"version": 3, "file": "countries.test.js", "names": ["metadata", "sortCountryOptions", "getSupportedCountryOptions", "isCountrySupportedWithError", "getSupportedCountries", "describe", "it", "value", "label", "should", "deep", "equal", "divider", "expect", "to", "be", "undefined"], "sources": ["../../source/helpers/countries.test.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport {\r\n\tsortCountryOptions,\r\n\tgetSupportedCountryOptions,\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries\r\n} from './countries.js'\r\n\r\ndescribe('helpers/countries', () => {\r\n\tit('should sort country options (no `order`)', () => {\r\n\t\tsortCountryOptions([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t}\r\n\t\t]).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with a divider)', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '|', 'RU']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdivider: true\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with \"...\")', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '|', '...']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdivider: true\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with \"…\")', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '|', '…']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdivider: true\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with \"🌐\")', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: 'International'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '🌐', '…']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tlabel: 'International'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should get supported country options', () => {\r\n\t\tgetSupportedCountryOptions([\r\n\t\t\t'🌐',\r\n\t\t\t'RU',\r\n\t\t\t'XX',\r\n\t\t\t'@',\r\n\t\t\t'|',\r\n\t\t\t'…',\r\n\t\t\t'...',\r\n\t\t\t'.'\r\n\t\t], metadata).should.deep.equal([\r\n\t\t\t'🌐',\r\n\t\t\t'RU',\r\n\t\t\t'|',\r\n\t\t\t'…',\r\n\t\t\t'...'\r\n\t\t])\r\n\t})\r\n\r\n\tit('should get supported country options (none supported)', () => {\r\n\t\texpect(getSupportedCountryOptions([\r\n\t\t\t'XX',\r\n\t\t\t'@',\r\n\t\t\t'.'\r\n\t\t], metadata)).to.be.undefined\r\n\t})\r\n\r\n\tit('should get supported country options (none supplied)', () => {\r\n\t\texpect(getSupportedCountryOptions(undefined, metadata)).to.be.undefined\r\n\t})\r\n\r\n\tit('should tell is country is supported with error', () => {\r\n\t\tisCountrySupportedWithError('RU', metadata).should.equal(true)\r\n\t\tisCountrySupportedWithError('XX', metadata).should.equal(false)\r\n\t})\r\n\r\n\tit('should get supported countries', () => {\r\n\t\tgetSupportedCountries(['RU', 'XX'], metadata).should.deep.equal(['RU'])\r\n\t})\r\n\r\n\tit('should get supported countries (none supported)', () => {\r\n\t\texpect(getSupportedCountries(['XX'], metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gCAArB;AAEA,SACCC,kBADD,EAECC,0BAFD,EAGCC,2BAHD,EAICC,qBAJD,QAKO,gBALP;AAOAC,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,0CAAD,EAA6C,YAAM;IACpDL,kBAAkB,CAAC,CAClB;MACCM,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADkB,EAKlB;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALkB,CAAD,CAAlB,CASGC,MATH,CASUC,IATV,CASeC,KATf,CASqB,CACpB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADoB,EAKpB;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALoB,CATrB;EAmBA,CApBC,CAAF;EAsBAF,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxDL,kBAAkB,CACjB,CACC;MACCM,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALD,CADiB,EAWjB,CAAC,IAAD,EAAO,GAAP,EAAY,IAAZ,CAXiB,CAAlB,CAYEC,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCI,OAAO,EAAE;IADV,CALmB,EAQnB;MACCL,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAZpB;EAyBA,CA1BC,CAAF;EA4BAF,EAAE,CAAC,0CAAD,EAA6C,YAAM;IACpDL,kBAAkB,CACjB,CACC;MACCM,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALD,CADiB,EAWjB,CAAC,IAAD,EAAO,GAAP,EAAY,KAAZ,CAXiB,CAAlB,CAYEC,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCI,OAAO,EAAE;IADV,CALmB,EAQnB;MACCL,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAZpB;EAyBA,CA1BC,CAAF;EA4BAF,EAAE,CAAC,wCAAD,EAA2C,YAAM;IAClDL,kBAAkB,CACjB,CACC;MACCM,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALD,CADiB,EAWjB,CAAC,IAAD,EAAO,GAAP,EAAY,GAAZ,CAXiB,CAAlB,CAYEC,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCI,OAAO,EAAE;IADV,CALmB,EAQnB;MACCL,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAZpB;EAyBA,CA1BC,CAAF;EA4BAF,EAAE,CAAC,yCAAD,EAA4C,YAAM;IACnDL,kBAAkB,CACjB,CACC;MACCM,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCA,KAAK,EAAE;IADR,CALD,EAQC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARD,CADiB,EAcjB,CAAC,IAAD,EAAO,IAAP,EAAa,GAAb,CAdiB,CAAlB,CAeEC,MAfF,CAeSC,IAfT,CAecC,KAfd,CAeoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCA,KAAK,EAAE;IADR,CALmB,EAQnB;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAfpB;EA4BA,CA7BC,CAAF;EA+BAF,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChDJ,0BAA0B,CAAC,CAC1B,IAD0B,EAE1B,IAF0B,EAG1B,IAH0B,EAI1B,GAJ0B,EAK1B,GAL0B,EAM1B,GAN0B,EAO1B,KAP0B,EAQ1B,GAR0B,CAAD,EASvBF,QATuB,CAA1B,CASaS,MATb,CASoBC,IATpB,CASyBC,KATzB,CAS+B,CAC9B,IAD8B,EAE9B,IAF8B,EAG9B,GAH8B,EAI9B,GAJ8B,EAK9B,KAL8B,CAT/B;EAgBA,CAjBC,CAAF;EAmBAL,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjEO,MAAM,CAACX,0BAA0B,CAAC,CACjC,IADiC,EAEjC,GAFiC,EAGjC,GAHiC,CAAD,EAI9BF,QAJ8B,CAA3B,CAAN,CAIcc,EAJd,CAIiBC,EAJjB,CAIoBC,SAJpB;EAKA,CANC,CAAF;EAQAV,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChEO,MAAM,CAACX,0BAA0B,CAACc,SAAD,EAAYhB,QAAZ,CAA3B,CAAN,CAAwDc,EAAxD,CAA2DC,EAA3D,CAA8DC,SAA9D;EACA,CAFC,CAAF;EAIAV,EAAE,CAAC,gDAAD,EAAmD,YAAM;IAC1DH,2BAA2B,CAAC,IAAD,EAAOH,QAAP,CAA3B,CAA4CS,MAA5C,CAAmDE,KAAnD,CAAyD,IAAzD;IACAR,2BAA2B,CAAC,IAAD,EAAOH,QAAP,CAA3B,CAA4CS,MAA5C,CAAmDE,KAAnD,CAAyD,KAAzD;EACA,CAHC,CAAF;EAKAL,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1CF,qBAAqB,CAAC,CAAC,IAAD,EAAO,IAAP,CAAD,EAAeJ,QAAf,CAArB,CAA8CS,MAA9C,CAAqDC,IAArD,CAA0DC,KAA1D,CAAgE,CAAC,IAAD,CAAhE;EACA,CAFC,CAAF;EAIAL,EAAE,CAAC,iDAAD,EAAoD,YAAM;IAC3DO,MAAM,CAACT,qBAAqB,CAAC,CAAC,IAAD,CAAD,EAASJ,QAAT,CAAtB,CAAN,CAAgDc,EAAhD,CAAmDC,EAAnD,CAAsDC,SAAtD;EACA,CAFC,CAAF;AAGA,CArLO,CAAR"}