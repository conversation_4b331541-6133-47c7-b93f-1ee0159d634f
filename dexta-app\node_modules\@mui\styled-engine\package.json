{"name": "@mui/styled-engine", "version": "5.14.12", "private": false, "author": "MUI Team", "description": "styled() API wrapper package for emotion.", "main": "./node/index.js", "keywords": ["react", "react-component", "mui", "emotion"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-styled-engine"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/system/styled/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "dependencies": {"@babel/runtime": "^7.23.1", "@emotion/cache": "^11.11.0", "csstype": "^3.1.2", "prop-types": "^15.8.1"}, "peerDependencies": {"@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "react": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public"}, "engines": {"node": ">=12.0.0"}, "module": "./index.js", "types": "./index.d.ts"}