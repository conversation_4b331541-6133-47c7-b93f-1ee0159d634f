export const getStepsCandidates = (t) => [
  {
    title: t("candidatesSteps.candidatesTitle"),
    element: "#cad2",
    intro: (
      <div>
        <p>{t("candidatesSteps.candidatesIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("candidatesSteps.candidatesList1")}</li>
          <li>{t("candidatesSteps.candidatesList2")}</li>
        </ul>
      </div>
    ),
  },
  {
    title: t("candidatesSteps.filterCandidatesTitle"),
    element: "#cad3",
    intro: (
      <div>
        <p>{t("candidatesSteps.filterCandidatesIntro")}</p>
        <ul className="list-disc px-4">
          <li>{t("candidatesSteps.filterCandidatesList1")}</li>
          <li>{t("candidatesSteps.filterCandidatesList2")}</li>
          <li>{t("candidatesSteps.filterCandidatesList3")}</li>
        </ul>
      </div>
    ),
  },
  {
    title: t("candidatesSteps.searchCandidatesTitle"),
    element: "#cad4",
    intro: (
      <div>
        <p>{t("candidatesSteps.searchCandidatesIntro")}</p>
      </div>
    ),
  },
];

export const assessment_statuses = [
  {
    name: "All",
    value: "",
  },
  {
    name: "Invited",
    value: "invited,draft",
  },
  {
    name: "In Progress",
    value: "InProgress,started",
  },
  {
    name: "Completed",
    value: "completed",
  },
];
