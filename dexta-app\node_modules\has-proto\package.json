{"name": "has-proto", "version": "1.0.1", "description": "Does this environment have the ability to get the [[Prototype]] of an object on creation with `__proto__`?", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-proto.git"}, "keywords": ["prototype", "proto", "set", "get", "__proto__", "getPrototypeOf", "setPrototypeOf", "has"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/has-proto/issues"}, "homepage": "https://github.com/inspect-js/has-proto#readme", "testling": {"files": "test/index.js"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}