// Function to darken a hex color
export function darkenHexColor(hex, percent) {
  // Ensure the hex code is in the correct format
  hex = hex?.replace(/^#/, "");

  // Convert 3-digit hex to 6-digit hex
  if (hex?.length === 3) {
    hex = hex
      .split("")
      .map((char) => char + char)
      .join("");
  }

  // Convert hex to RGB
  let r = parseInt(hex?.substring(0, 2), 16);
  let g = parseInt(hex?.substring(2, 4), 16);
  let b = parseInt(hex?.substring(4, 6), 16);

  // Calculate the darkened color
  r = Math.floor(r * (1 - percent / 100));
  g = Math.floor(g * (1 - percent / 100));
  b = Math.floor(b * (1 - percent / 100));

  // Convert RGB back to hex
  const darkenedHex = `#${r.toString(16).padStart(2, "0")}${g
    .toString(16)
    .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;

  return darkenedHex;
}

// Function to get button style
export const buttonStyle = (isHovered, originalColor, secColor) => ({
  background: isHovered ? darkenHexColor(originalColor, 15) : originalColor,
  transition: "background-color 0.3s, transform 0.6s",
  transform: isHovered ? "scale(1.02)" : "scale(1)",
  color: secColor,
  border: `1px solid ${secColor}`,
  fontFamily: "Archia Semibold",
});
