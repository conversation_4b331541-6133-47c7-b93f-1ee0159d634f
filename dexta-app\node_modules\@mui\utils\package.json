{"name": "@mui/utils", "version": "5.14.12", "private": false, "author": "MUI Team", "description": "Utility functions for React components.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "utils"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-utils"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "private package", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "dependencies": {"@babel/runtime": "^7.23.1", "@types/prop-types": "^15.7.7", "prop-types": "^15.8.1", "react-is": "^18.2.0"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public"}, "engines": {"node": ">=12.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}