import { Fragment, useRef, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import CloseModal from "../../Dexta_assets/closeModal.png";

export default function NoContentModal({
  noContentModal,
  setNoContentNodal,
  componentChild,
  heading,
  customWidth,
  padding,
  button = null,
  textCenter = false,
  descriptionWidth = null,
  textSize,
}) {
  const cancelButtonRef = useRef(null);
  return (
    <Transition.Root show={noContentModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-40"
        initialFocus={cancelButtonRef}
        onClose={() => {
          setNoContentNodal(false);
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enterTo="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-100"
          leaveFrom="opacity-100 translate-y-0 sm:scale-100"
          leaveTo="opacity-0 translate-y-2 sm:translate-y-0 sm:scale-95"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-40 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel
                className={`relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 ${
                  customWidth ? customWidth : "sm:w-full sm:max-w-xl"
                }`}
              >
                <div className="bg-white px-4 pb-4 pt-5 sm:p-6">
                  <img
                    src={CloseModal}
                    className="absolute top-4 right-5 z-20 w-6 h-6 cursor-pointer"
                    onClick={() => setNoContentNodal(false)}
                  />
                  <div className="">
                    <div
                      className={`mt-3 ${
                        textCenter ? textCenter : "text-left sm:text-left"
                      }  sm:mt-0 `}
                    >
                      <Dialog.Title
                        as="h1"
                        className={`text-2xl ${
                          textCenter ? textCenter : "text-left"
                        }  mt-2 ${
                          padding ? padding : "p-2"
                        } font-semibold leading-6 text-gray-900`}
                        style={{ fontFamily: "Archia Bold" }}
                      >
                        {heading}
                      </Dialog.Title>
                      <div
                        className={`mt-2 ${
                          textSize ? textSize : ""
                        } text-black ${descriptionWidth && descriptionWidth}`}
                        style={{ fontFamily: "Silka" }}
                      >
                        {componentChild}
                      </div>

                      {button && (
                        <div className="mt-4 mx-auto flex justify-center">
                          <button
                            type="button"
                            className="mt-3 inline-flex w-full justify-center hover:bg-primaryGreen hover:text-black rounded-md bg-coalColor px-5 py-2 text-sm font-semibold text-white shadow-sm ring-1 ring-inset  sm:mt-0 sm:w-auto"
                            onClick={() => {
                              setNoContentNodal(false);
                            }}
                            style={{ fontFamily: "Silka" }}
                          >
                            Close
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
