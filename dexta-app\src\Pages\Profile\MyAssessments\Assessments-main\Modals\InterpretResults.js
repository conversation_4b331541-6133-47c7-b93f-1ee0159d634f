import React, { useRef, useState } from "react";
import Scrollbars from "react-custom-scrollbars";
import VerticalBar from "../../../../../Components/VerticalBar/VerticalBar";
import { dummy_sections, graph_filters } from "../data";
import { RiArrowDropDownLine } from "react-icons/ri";
import CustomButton from "../../../../../Components/CustomButton/CustomButton";
import { useDispatch, useSelector } from "react-redux";
import graph1 from "../../../../../Dexta_assets/graph3.0.png";
import graph2 from "../../../../../Dexta_assets/graph4.0.png";
import graph3 from "../../../../../Dexta_assets/graph3.png";
import DropdownListInterpret from "../../../../../Components/Dropdown/DropdownInterpret";
import closeIcon from "../../../../../Dexta_assets/closeModal.png";
import { useTranslation } from "react-i18next";

const InterpretResults = ({ helpModal, setHelp }) => {
  const { t } = useTranslation();
  const [selectedGraphFilter, setSelectedGraphFilter] = useState("Your candidate pool average");
  console.log(selectedGraphFilter, "selectedGraphFilter")
  const [graphDropdown, setGraphDropdown] = useState(false);
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const graphRef = useRef(null);
  const scoresArray = [10, 20, 30, 40, 50, 60, 70, 80];
  const handleGraphFilterClick = (title) => {
    setSelectedGraphFilter(title);
    setGraphDropdown(false);
  };
  return (
    <main className="fixed inset-0 flex items-center justify-center z-50">
      <div
        className="bg-black opacity-80 absolute inset-0"
        onClick={() => setHelp(false)}
      ></div>
      <div className="bg-white rounded-lg sm:mx-auto sm:w-full 2xl:w-2/5 xl:w-3/6 lg:w-3/6 h-5/6 overflow-hidden overflow-y-auto mx-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="bg-white rounded-lg enable-scrollbar2">
          <Scrollbars
            autoHeight
            autoHide
            autoHeightMax="760px"
            style={{ width: "100%", height: "100%" }}
          >
            <img
              src={closeIcon}
              className=" top-3 ml-auto sticky z-20 w-6 h-6 mr-2 cursor-pointer"
              onClick={() => setHelp(false)}
            />
            <div className="px-4 py-1">
              <div className="mt-4 text-left md:text-left">
                <p
                  className="text-xl text-coalColor"
                  style={{ fontFamily: "Archia Bold" }}
                >
                  {t("details.interpret_results.how_to_interpret_test_results")}
                </p>
              </div>
              <div
                className="text-left text-sm md:text-left mt-5"
                style={{ fontFamily: "Silka" }}
              >
                <p className="text-coalColor mt-0">
                  {t("details.interpret_results.test_results_page_description")}
                </p>
              </div>
              <div className="text-left md:text-left mt-5">
                <p
                  className="text-xl text-coalColor"
                  style={{ fontFamily: "Archia Bold" }}
                >
                  {t("details.interpret_results.key_elements_explained")}
                </p>
              </div>

              <ol
                class="list-decimal pl-8 mt-5 text-lg text-coalColor"
                style={{ fontFamily: "Archia Bold" }}
              >
                {/*GRID ONE*/}
                <div className="md:grid grid-cols-5">
                  <li className="col-span-2">
                    <div className="flex items-center">{t("details.interpret_results.overall_scores")}</div>
                    <ul
                      className="list-disc pl-2 mt-2 text-base"
                      style={{ fontFamily: "Silka" }}
                    >
                      <li>
                        <strong>{t("details.interpret_results.total_average_score")}</strong> {t("details.interpret_results.total_average_score_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.candidate_pool_average_score")}</strong> {t("details.interpret_results.candidate_pool_average_score_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.highest_candidate_score")}</strong> {t("details.interpret_results.highest_candidate_score_desc")}
                      </li>
                    </ul>
                  </li>
                  <div className="px-2 col-span-3">
                    <div
                      className="pt-14 p w-full px-5 bg-[#DBD9C0] mt-4 mx-auto justify-center rounded flex flex-row sm:gap-14 md:gap-14"
                      style={{ fontFamily: "Silka" }}
                    >
                      <VerticalBar
                        heightOfCell="h-[13rem]"
                        EmptyCellColor="bg-[#FFF8E8]"
                        FilledCellHeight={74}
                        tooltip1Text={t("details.interpret_results.your_best")}
                        FilledCellColor="bg-[#FFB500]"
                        tooltip2Text={t("details.interpret_results.candidate")}
                        tooltip4Text={t("details.interpret_results.score")}
                        tooltip3Text={74 + "%"}
                        paddingTooltip="py-[4px]"
                        marginTopTooltip="-mt-[100px]"
                        widthtooltip="w-[5.8rem]"
                        tooltip1TextSize="text-[10px]"
                        spaceY="-space-y-2"
                      />
                      <VerticalBar
                        heightOfCell="h-[13rem]"
                        EmptyCellColor="bg-[#FFF8E8]"
                        FilledCellColor="bg-[#FF5812]"
                        FilledCellHeight={65}
                        tooltip1Text={t("details.interpret_results.john_doe_total")}
                        tooltip2Text={t("details.interpret_results.total")}
                        tooltip4Text={t("details.interpret_results.score")}
                        tooltip3Text={65 + "%"}
                        paddingTooltip="py-[4px]"
                        marginTopTooltip="-mt-[100px]"
                        widthtooltip="w-[5.8rem]"
                        tooltip1TextSize="text-[10px]"
                        spaceY="-space-y-2"
                      />
                      <VerticalBar
                        heightOfCell="h-[13rem]"
                        EmptyCellColor="bg-[#FFF8E8]"
                        FilledCellHeight={68}
                        tooltip1Text={t("details.interpret_results.your_candidate")}
                        FilledCellColor="bg-[#C0FF06]"
                        tooltip2Text={t("details.interpret_results.pool_average")}
                        tooltip4Text={t("details.interpret_results.score")}
                        tooltip3Text={68 + "%"}
                        paddingTooltip="py-[4px]"
                        marginTopTooltip="-mt-[100px]"
                        widthtooltip="w-[5.8rem]"
                        tooltip1TextSize="text-[10px]"
                        spaceY="-space-y-2"
                      />
                    </div>
                  </div>
                </div>
                <hr className="w-full mt-8 bg-gray-900 border-1" />
                {/*GRID TWO*/}
                <div className="md:grid grid-cols-2 mt-5">
                  <li>
                    <div className="flex items-center mt-5">
                      {t("details.interpret_results.module_specific_results")}
                    </div>

                    <ul
                      className="list-disc pl-2 mt-2 text-base"
                      style={{ fontFamily: "Silka" }}
                    >
                      <li>
                        <strong>{t("details.interpret_results.module_title")}</strong> {t("details.interpret_results.module_title_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.proficiency_level")}</strong> {t("details.interpret_results.proficiency_level_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.candidate_score")}</strong> {t("details.interpret_results.candidate_score_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.average_score")}</strong> {t("details.interpret_results.average_score_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.time_spent")}</strong> {t("details.interpret_results.time_spent_desc")}
                      </li>
                    </ul>
                  </li>
                  <div
                    className="flex flex-col gap-3 px-10 my-auto"
                    style={{ fontFamily: "Silka" }}
                  >
                    {dummy_sections?.slice(0, 1).map((i, index) => (
                      <div>
                        <div className="bg-[#DBD9C0] h-[18rem] rounded flex flex-col">
                          <p
                            className="px-5 mt-3 h-20 text-base"
                            style={{ fontFamily: "Silka Light" }}
                          >
                            {i?.name}
                          </p>
                          <div className="grid grid-cols-2 px-5 gap-3">
                            <p
                              className={`border rounded-2xl text-[11px] bg-white my-auto text-center ${
                                i?.experience === "Beginner" &&
                                "border-[#0B5B23]"
                              } ${
                                i?.experience === "Intermediate" &&
                                "border-[#FFB500]"
                              } ${
                                i?.experience === "Advanced" &&
                                "border-[#FF5812]"
                              } ${i?.experience === "" && "border-[#C0FF06]"}`}
                              style={{ fontFamily: "Silka" }}
                            >
                              {i?.experience != "" ? i?.experience : "custom"}
                            </p>
                            <div>
                              <p
                                className="text-center text-[11px] py-1 flex"
                                style={{ fontFamily: "Silka" }}
                              >
                                {i?.time}
                              </p>
                            </div>
                          </div>

                          <div className="flex-row gap-20 mt-20 px-10 flex justify-center ">
                            <VerticalBar
                              heightOfCell="h-20"
                              EmptyCellColor="bg-[#DBD9C0]"
                              FilledCellColor="bg-[#FF5812]"
                              FilledCellHeight={50}
                              tooltip1Text={t("details.interpret_results.graph_tooltip_scored")}
                              tooltip3Text={50 + "%"}
                              paddingTooltip="py-2"
                              marginTopTooltip="-mt-[70px]"
                              widthtooltip="w-[6rem]"
                            />
                            <VerticalBar
                              heightOfCell="h-20"
                              EmptyCellColor="bg-[#DBD9C0]"
                              FilledCellColor="bg-[#FFB500]"
                              FilledCellHeight={60}
                              tooltip1Text={t("details.interpret_results.graph_tooltip_highest_score")}
                              tooltip3Text={60 + "%"}
                              paddingTooltip="py-2"
                              marginTopTooltip="-mt-[70px]"
                              widthtooltip="w-[6rem]"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <hr className="w-full mt-8 bg-gray-900 border-1" />
                {/*GRID THREE*/}
                <div className="md:grid grid-cols-2 mt-5">
                  <li>
                    <div className="flex items-center mt-5">
                      {t("details.interpret_results.module_specific_comparative_data_points")}
                    </div>
                    <p
                      className="text-base mt-3"
                      style={{ fontFamily: "Silka" }}
                    >
                      {t("details.interpret_results.module_specific_comparative_data_points_desc")}
                    </p>
                    <ul
                      className="list-disc pl-2 text-base mt-5"
                      style={{ fontFamily: "Silka" }}
                    >
                      <li>
                        <strong>{t("details.interpret_results.your_best_performing_candidate")}</strong>
                        <ul className="custom-list pl-5 mt-2">
                          <li>{t("details.interpret_results.your_best_performing_candidate_desc1")}</li>
                          <li>{t("details.interpret_results.your_best_performing_candidate_desc2")}</li>
                        </ul>
                      </li>
                      <li className="mt-3">
                        <strong>{t("details.interpret_results.your_candidate_pool_average")}</strong>
                        <ul className="custom-list pl-5 mt-2">
                          <li>{t("details.interpret_results.your_candidate_pool_average_desc1")}</li>
                          <li>{t("details.interpret_results.your_candidate_pool_average_desc2")}</li>
                        </ul>
                      </li>
                      <li className="mt-3">
                        <strong>{t("details.interpret_results.dexta_candidate_pool_average")}</strong>
                        <ul className="custom-list pl-5 mt-2">
                          <li>{t("details.interpret_results.dexta_candidate_pool_average_desc1")}</li>
                          <li>{t("details.interpret_results.dexta_candidate_pool_average_desc2")}</li>
                        </ul>
                      </li>
                    </ul>
                  </li>
                  <div>
                    <div className="px-4">
                      <p
                        className="text-coalColor text-lg mt-5 col-span-4"
                        style={{ fontFamily: "Archia Semibold" }}
                      >
                        {t("details.interpret_results.module_scores")}
                      </p>
                      <div
                        className="my-auto w-full sm:justify-center md:justify-end col-span-8"
                        style={{ fontFamily: "Silka" }}
                      >
                        <div className="relative md:w-full">
                          <CustomButton
                            label={t(`details.interpret_results.${selectedGraphFilter.toLowerCase()}`)}
                            id="graphID"
                            borderCustom="border border-black graphClass text-xs"
                            paddingY="0.3rem"
                            iconR={RiArrowDropDownLine}
                            iconWidth="w-6 h-6"
                            justifyContent="justify-between"
                            paddingx="px-2"
                            onClickButton={() =>
                              setGraphDropdown(!graphDropdown)
                            }
                          />
                          {graphDropdown && (
                            <div
                              className="absolute z-40 border border-coalColor graphClass right-0 top-full text-xs h-auto overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] w-full mt-2"
                              ref={graphRef}
                            >
                              {graph_filters?.map((i) => (
                                <DropdownListInterpret
                                  key={i?.title}
                                  title={
                                    i?.title === "Your best performing candidate"
                                      ? t("details.interpret_results.your_best_performing_candidate")
                                      : i?.title === "Your candidate pool average"
                                      ? t("details.interpret_results.your_candidate_pool_average")
                                      : i?.title === "Dexta candidate pool average"
                                      ? t("details.interpret_results.dexta_candidate_pool_average")
                                      : i?.title === "Dexta best performing candidate"
                                      ? t("details.interpret_results.dexta_best_performing_candidate")
                                      : i?.title
                                  }
                                  textsize="text-xs"
                                  onClick={() =>
                                    handleGraphFilterClick(i?.title)
                                  }
                                  user_package={user_package_check}
                                />
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-3 px-10 mt-5">
                      {dummy_sections?.slice(0, 2).map((i, index) => (
                        <div>
                          <div className="bg-[#DBD9C0] h-[17rem] rounded flex flex-col">
                            <p
                              className="px-5 mt-3 h-20 text-base"
                              style={{ fontFamily: "Silka Light" }}
                            >
                              {i?.name}
                            </p>
                            <div className="grid grid-cols-2 px-5 gap-3">
                              <p
                                className={`border rounded-2xl text-[11px] bg-white my-auto text-center ${
                                  i?.experience === "Beginner" &&
                                  "border-[#0B5B23]"
                                } ${
                                  i?.experience === "Intermediate" &&
                                  "border-[#FFB500]"
                                } ${
                                  i?.experience === "Advanced" &&
                                  "border-[#FF5812]"
                                } ${
                                  i?.experience === "" && "border-[#C0FF06]"
                                }`}
                                style={{ fontFamily: "Silka" }}
                              >
                                {i?.experience != "" ? i?.experience : "custom"}
                              </p>
                              <div>
                                <p
                                  className="text-center text-xs py-1 flex"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  {i?.time}
                                </p>
                              </div>
                            </div>
                            <div className="flex-row gap-20 mt-[80px] px-10 flex justify-center ">
                              <VerticalBar
                                heightOfCell="h-[80px]"
                                EmptyCellColor="bg-[#DBD9C0]"
                                FilledCellColor="bg-[#FF5812]"
                                FilledCellHeight={65}
                                tooltip1Text={t("details.interpret_results.graph_tooltip_scored")}
                                tooltip3Text={65 + "%"}
                                paddingTooltip="py-2"
                                marginTopTooltip="-mt-[70px]"
                                widthtooltip="w-[6rem]"
                              />
                              {i?.experience !== "" && (
                                <>
                                  {selectedGraphFilter ===
                                    "Your best performing candidate" && (
                                    <VerticalBar
                                      filter={selectedGraphFilter}
                                      heightOfCell="h-[80px]"
                                      EmptyCellColor="bg-[#DBD9C0]"
                                      FilledCellColor="bg-[#FFB500]"
                                      FilledCellHeight={74}
                                      tooltip1Text={t("details.interpret_results.graph_tooltip_highest_score")}
                                      tooltip3Text={74 + "%"}
                                      paddingTooltip="py-2"
                                      marginTopTooltip="-mt-[70px]"
                                      widthtooltip="w-[6rem]"
                                    />
                                  )}
                                  {selectedGraphFilter ===
                                    "Your candidate pool average" && (
                                    <VerticalBar
                                      filter={selectedGraphFilter}
                                      heightOfCell="h-[80px]"
                                      EmptyCellColor="bg-[#DBD9C0]"
                                      FilledCellColor="bg-[#C0FF06]"
                                      FilledCellHeight={68}
                                      tooltip1Text={t("details.interpret_results.graph_tooltip_average")}
                                      tooltip3Text={68 + "%"}
                                      paddingTooltip="py-2"
                                      marginTopTooltip="-mt-[70px]"
                                      widthtooltip="w-[6rem]"
                                    />
                                  )}
                                  {selectedGraphFilter ===
                                    "Dexta candidate pool average" && (
                                    <VerticalBar
                                      filter={selectedGraphFilter}
                                      heightOfCell="h-[80px]"
                                      EmptyCellColor="bg-[#DBD9C0]"
                                      FilledCellColor="bg-[#252E3A]"
                                      FilledCellHeight={72}
                                      tooltip1Text={t("details.interpret_results.graph_tooltip_dexta_average")}
                                      tooltip3Text={72 + "%"}
                                      paddingTooltip="py-2"
                                      marginTopTooltip="-mt-[70px]"
                                      widthtooltip="w-[6rem]"
                                    />
                                  )}
                                  {selectedGraphFilter ===
                                    "Dexta best performing candidate" && (
                                    <VerticalBar
                                      filter={selectedGraphFilter}
                                      heightOfCell="h-[80px]"
                                      EmptyCellColor="bg-[#DBD9C0]"
                                      FilledCellColor="bg-[#A16207]"
                                      FilledCellHeight={85}
                                      tooltip1Text={t("details.interpret_results.graph_tooltip_dexta_best")}
                                      tooltip3Text={85 + "%"}
                                      paddingTooltip="py-2"
                                      marginTopTooltip="-mt-[70px]"
                                      widthtooltip="w-[6rem]"
                                    />
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <hr className="w-full mt-8 bg-gray-900 border-1" />
                {/*GRID FOUR*/}
                <li>
                  <div className="md:grid grid-cols-2">
                    <div className="flex items-center mt-5">
                      {t("details.interpret_results.performance_completion_time_insights")}
                    </div>
                    <div></div>
                  </div>
                  <div className="md:grid grid-cols-2">
                    <div>
                      <p
                        className="text-base mt-3"
                        style={{ fontFamily: "Silka" }}
                      >
                        {t("details.interpret_results.performance_completion_time_insights_desc")}
                      </p>
                      <ul
                        className="list-disc pl-2 text-base mt-5"
                        style={{ fontFamily: "Silka" }}
                      >
                        <li>
                          <strong>{t("details.interpret_results.performance_insights")}</strong>
                          <ul className="custom-list pl-5 mt-2">
                            <li>
                              <strong>{t("details.interpret_results.box_plot")}</strong> {t("details.interpret_results.box_plot_desc")}
                            </li>
                            <li>
                              <strong>{t("details.interpret_results.distribution_curve")}</strong> {t("details.interpret_results.distribution_curve_desc")}
                            </li>
                            <li>
                              <strong>{t("details.interpret_results.candidate_score_marker")}</strong> {t("details.interpret_results.candidate_score_marker_desc")}
                            </li>
                          </ul>
                        </li>
                      </ul>
                    </div>
                    <img src={graph1} className="my-auto rounded-lg" />
                  </div>
                  <div className="md:grid grid-cols-2">
                    <ul
                      className="list-disc pl-2 text-base mt-5"
                      style={{ fontFamily: "Silka" }}
                    >
                      <li>
                        <strong>{t("details.interpret_results.completion_time_insights")}</strong>
                        <ul className="custom-list pl-5 mt-2">
                          <li>
                            <strong>{t("details.interpret_results.box_plot")}</strong> {t("details.interpret_results.completion_time_box_plot_desc")}
                          </li>
                          <li>
                            <strong>{t("details.interpret_results.distribution_curve")}</strong> {t("details.interpret_results.completion_time_distribution_curve_desc")}
                          </li>
                          <li>
                            <strong>{t("details.interpret_results.candidate_score_marker")}</strong> {t("details.interpret_results.completion_time_marker_desc")}
                          </li>
                        </ul>
                      </li>
                    </ul>
                    <img src={graph2} className="my-auto rounded-lg" />
                  </div>
                </li>
                <hr className="w-full mt-8 bg-gray-900 border-1" />
                {/*GRID FIVE*/}
                <div className="md:grid grid-cols-2 mt-8">
                  <li>
                    <div className="flex items-center">
                      {t("details.interpret_results.anti_cheating_monitor")}
                    </div>
                    <ul
                      className="list-disc pl-2 mt-2 text-base"
                      style={{ fontFamily: "Silka" }}
                    >
                      <li>
                        <strong>{t("details.interpret_results.device_used")}</strong> {t("details.interpret_results.device_used_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.location")}</strong> {t("details.interpret_results.location_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.ip_address_check")}</strong> {t("details.interpret_results.ip_address_check_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.webcam_enabled")}</strong> {t("details.interpret_results.webcam_enabled_desc")}
                      </li>
                      <li>
                        <strong>{t("details.interpret_results.fullscreen_mode_active")}</strong> {t("details.interpret_results.fullscreen_mode_active_desc")}
                      </li>
                    </ul>
                  </li>
                  <img src={graph3} className="my-auto" />
                </div>
              </ol>
            </div>
          </Scrollbars>
        </div>
      </div>
    </main>
  );
};

export default InterpretResults;
