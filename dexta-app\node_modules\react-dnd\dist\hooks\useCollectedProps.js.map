{"version": 3, "sources": ["../../src/hooks/useCollectedProps.ts"], "sourcesContent": ["import type { Connector } from '../internals/index.js'\nimport type { <PERSON>lerManager, MonitorEventEmitter } from '../types/index.js'\nimport { useMonitorOutput } from './useMonitorOutput.js'\n\nexport function useCollectedProps<Collected, Monitor extends HandlerManager>(\n\tcollector: ((monitor: Monitor) => Collected) | undefined,\n\tmonitor: Monitor & MonitorEventEmitter,\n\tconnector: Connector,\n) {\n\treturn useMonitorOutput(monitor, collector || (() => ({} as Collected)), () =>\n\t\tconnector.reconnect(),\n\t)\n}\n"], "names": ["useMonitorOutput", "useCollectedProps", "collector", "monitor", "connector", "reconnect"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,uBAAuB,CAAA;AAExD,OAAO,SAASC,iBAAiB,CAChCC,SAAwD,EACxDC,OAAsC,EACtCC,SAAoB,EACnB;IACD,OAAOJ,gBAAgB,CAACG,OAAO,EAAED,SAAS,IAAI,CAAC,IAAM,CAAC,EAAE,CAAc;IAAA,CAAC,EAAE,IACxEE,SAAS,CAACC,SAAS,EAAE;IAAA,CACrB,CAAA;CACD"}