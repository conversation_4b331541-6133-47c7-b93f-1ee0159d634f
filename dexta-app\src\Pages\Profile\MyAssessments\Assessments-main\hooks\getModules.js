import http from "../../../../../http";

export const getModules = async (exp, job, search, page, language) => {
  const response = await http.get(
    `/section?page=${page}&take=200&experience=${exp}&jobIds=${job}&name=${search}&status=active,coming_soon&order=ASC&orderBy=name&mainIndustryStatus=active&returnAllModules=true&language=${language}`
  );
  return response.data;
};

export const getCompanyModules = async (exp, job, search, page, language) => {
  const response = await http.get(
    `/section?page=${page}&take=200&&status=active,coming_soon&order=ASC&orderBy=name&onlyCreatedByCompany=true&type=custom-questions&returnAllModules=false&language=${language}`
  );
  return response.data;
};
