{"version": 3, "file": "getPhoneInputWithCountryStateUpdateFromNewProps.test.js", "names": ["getPhoneInputWithCountryStateUpdateFromNewProps", "newProps", "prevProps", "state", "_getPhoneInputWithCountryStateUpdateFromNewProps", "metadata", "describe", "it", "reset", "defaultCountry", "should", "deep", "equal", "phoneDigits", "undefined", "value", "country", "hasUserSelectedACountry", "international", "expect", "to", "be"], "sources": ["../../source/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.test.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport _getPhoneInputWithCountryStateUpdateFromNewProps from './getPhoneInputWithCountryStateUpdateFromNewProps.js'\r\n\r\nfunction getPhoneInputWithCountryStateUpdateFromNewProps(newProps, prevProps, state) {\r\n\treturn _getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t{\r\n\t\t\t...newProps,\r\n\t\t\tmetadata\r\n\t\t},\r\n\t\t{\r\n\t\t\t...prevProps,\r\n\t\t\tmetadata\r\n\t\t},\r\n\t\tstate\r\n\t)\r\n}\r\n\r\ndescribe('getPhoneInputWithCountryStateUpdateFromNewProps', () => {\r\n\tit('should get state update from new props (reset)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\treset: true,\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{},\r\n\t\t\t{}\r\n\t\t).should.deep.equal({\r\n\t\t\tphoneDigits: undefined,\r\n\t\t\tvalue: undefined,\r\n\t\t\tcountry: 'RU',\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (reset) (international)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\treset: true,\r\n\t\t\t\tinternational: true,\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{},\r\n\t\t\t{}\r\n\t\t).should.deep.equal({\r\n\t\t\tphoneDigits: '+7',\r\n\t\t\tvalue: undefined,\r\n\t\t\tcountry: 'RU',\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (default country did not change)', () => {\r\n\t\texpect(getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t)).to.be.undefined\r\n\t})\r\n\r\n\tit('should get state update from new props (default country changed) (no `value`)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'US'\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t).should.deep.equal({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tphoneDigits: undefined,\r\n\t\t\tvalue: undefined\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (default country changed) (no `value`) (new country not supported)', () => {\r\n\t\texpect(getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'XX'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'US'\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t)).to.be.undefined\r\n\t})\r\n\r\n\tit('should get state update from new props (default country changed) (`value` is intl prefix)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tinternational: true,\r\n\t\t\t\tdefaultCountry: 'CA'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tinternational: true,\r\n\t\t\t\tdefaultCountry: 'US'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: '+1'\r\n\t\t\t}\r\n\t\t).should.deep.equal({\r\n\t\t\tcountry: 'CA',\r\n\t\t\tphoneDigits: '+1',\r\n\t\t\tvalue: undefined\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (default country changed) (has `value`)', () => {\r\n\t\texpect(getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535',\r\n\t\t\t\tdefaultCountry: 'FR'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535',\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t)).to.be.undefined\r\n\t})\r\n\r\n\tit('should get state update from new props (default country changed to `undefined`) (has `value`)', () => {\r\n\t\texpect(getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: 'FR'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: undefined\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t)).to.deep.equal({\r\n\t\t\tcountry: 'FR',\r\n\t\t\tphoneDigits: undefined,\r\n\t\t\tvalue: undefined\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (`value` changed: undefined -> value)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535',\r\n\t\t\t\tdefaultCountry: 'FR'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'US'\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t).should.deep.equal({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tphoneDigits: '+78005553535',\r\n\t\t\tvalue: '+78005553535'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (`value` changed: value -> undefined)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535',\r\n\t\t\t\tdefaultCountry: 'RU'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535'\r\n\t\t\t}\r\n\t\t).should.deep.equal({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tphoneDigits: undefined,\r\n\t\t\tvalue: undefined,\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t})\r\n\t})\r\n\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/377\r\n\tit('should get state update from new props (`value` changed: undefined -> +78)', () => {\r\n\t\tgetPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{ value: '+78' },\r\n\t\t\t{},\r\n\t\t\t{}\r\n\t\t).should.deep.equal({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tphoneDigits: '+78',\r\n\t\t\tvalue: '+78'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should get state update from new props (`value` changed, but already displayed)', () => {\r\n\t\texpect(getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535'\r\n\t\t\t},\r\n\t\t\t{},\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535'\r\n\t\t\t}\r\n\t\t)).to.be.undefined\r\n\t})\r\n\r\n\tit('should get state update from new props (`value` did not change)', () => {\r\n\t\texpect(getPhoneInputWithCountryStateUpdateFromNewProps(\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: '+78005553535'\r\n\t\t\t},\r\n\t\t\t{}\r\n\t\t)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;;;;;;;AAEA,SAASA,+CAAT,CAAyDC,QAAzD,EAAmEC,SAAnE,EAA8EC,KAA9E,EAAqF;EACpF,OAAO,IAAAC,4DAAA,kCAEFH,QAFE;IAGLI,QAAQ,EAARA;EAHK,oCAMFH,SANE;IAOLG,QAAQ,EAARA;EAPK,IASNF,KATM,CAAP;AAWA;;AAEDG,QAAQ,CAAC,iDAAD,EAAoD,YAAM;EACjEC,EAAE,CAAC,gDAAD,EAAmD,YAAM;IAC1DP,+CAA+C,CAC9C;MACCQ,KAAK,EAAE,IADR;MAECC,cAAc,EAAE;IAFjB,CAD8C,EAK9C,EAL8C,EAM9C,EAN8C,CAA/C,CAOEC,MAPF,CAOSC,IAPT,CAOcC,KAPd,CAOoB;MACnBC,WAAW,EAAEC,SADM;MAEnBC,KAAK,EAAED,SAFY;MAGnBE,OAAO,EAAE,IAHU;MAInBC,uBAAuB,EAAEH;IAJN,CAPpB;EAaA,CAdC,CAAF;EAgBAP,EAAE,CAAC,gEAAD,EAAmE,YAAM;IAC1EP,+CAA+C,CAC9C;MACCQ,KAAK,EAAE,IADR;MAECU,aAAa,EAAE,IAFhB;MAGCT,cAAc,EAAE;IAHjB,CAD8C,EAM9C,EAN8C,EAO9C,EAP8C,CAA/C,CAQEC,MARF,CAQSC,IART,CAQcC,KARd,CAQoB;MACnBC,WAAW,EAAE,IADM;MAEnBE,KAAK,EAAED,SAFY;MAGnBE,OAAO,EAAE,IAHU;MAInBC,uBAAuB,EAAEH;IAJN,CARpB;EAcA,CAfC,CAAF;EAiBAP,EAAE,CAAC,yEAAD,EAA4E,YAAM;IACnFY,MAAM,CAACnB,+CAA+C,CACrD;MACCS,cAAc,EAAE;IADjB,CADqD,EAIrD;MACCA,cAAc,EAAE;IADjB,CAJqD,EAOrD,EAPqD,CAAhD,CAAN,CAQGW,EARH,CAQMC,EARN,CAQSP,SART;EASA,CAVC,CAAF;EAYAP,EAAE,CAAC,+EAAD,EAAkF,YAAM;IACzFP,+CAA+C,CAC9C;MACCS,cAAc,EAAE;IADjB,CAD8C,EAI9C;MACCA,cAAc,EAAE;IADjB,CAJ8C,EAO9C,EAP8C,CAA/C,CAQEC,MARF,CAQSC,IART,CAQcC,KARd,CAQoB;MACnBI,OAAO,EAAE,IADU;MAEnBH,WAAW,EAAEC,SAFM;MAGnBC,KAAK,EAAED;IAHY,CARpB;EAaA,CAdC,CAAF;EAgBAP,EAAE,CAAC,2GAAD,EAA8G,YAAM;IACrHY,MAAM,CAACnB,+CAA+C,CACrD;MACCS,cAAc,EAAE;IADjB,CADqD,EAIrD;MACCA,cAAc,EAAE;IADjB,CAJqD,EAOrD,EAPqD,CAAhD,CAAN,CAQGW,EARH,CAQMC,EARN,CAQSP,SART;EASA,CAVC,CAAF;EAYAP,EAAE,CAAC,2FAAD,EAA8F,YAAM;IACrGP,+CAA+C,CAC9C;MACCkB,aAAa,EAAE,IADhB;MAECT,cAAc,EAAE;IAFjB,CAD8C,EAK9C;MACCS,aAAa,EAAE,IADhB;MAECT,cAAc,EAAE;IAFjB,CAL8C,EAS9C;MACCM,KAAK,EAAE;IADR,CAT8C,CAA/C,CAYEL,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB;MACnBI,OAAO,EAAE,IADU;MAEnBH,WAAW,EAAE,IAFM;MAGnBE,KAAK,EAAED;IAHY,CAZpB;EAiBA,CAlBC,CAAF;EAoBAP,EAAE,CAAC,gFAAD,EAAmF,YAAM;IAC1FY,MAAM,CAACnB,+CAA+C,CACrD;MACCe,KAAK,EAAE,cADR;MAECN,cAAc,EAAE;IAFjB,CADqD,EAKrD;MACCM,KAAK,EAAE,cADR;MAECN,cAAc,EAAE;IAFjB,CALqD,EASrD,EATqD,CAAhD,CAAN,CAUGW,EAVH,CAUMC,EAVN,CAUSP,SAVT;EAWA,CAZC,CAAF;EAcAP,EAAE,CAAC,+FAAD,EAAkG,YAAM;IACzGY,MAAM,CAACnB,+CAA+C,CACrD;MACCe,KAAK,EAAED,SADR;MAECL,cAAc,EAAE;IAFjB,CADqD,EAKrD;MACCM,KAAK,EAAED,SADR;MAECL,cAAc,EAAEK;IAFjB,CALqD,EASrD,EATqD,CAAhD,CAAN,CAUGM,EAVH,CAUMT,IAVN,CAUWC,KAVX,CAUiB;MAChBI,OAAO,EAAE,IADO;MAEhBH,WAAW,EAAEC,SAFG;MAGhBC,KAAK,EAAED;IAHS,CAVjB;EAeA,CAhBC,CAAF;EAkBAP,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxFP,+CAA+C,CAC9C;MACCe,KAAK,EAAE,cADR;MAECN,cAAc,EAAE;IAFjB,CAD8C,EAK9C;MACCA,cAAc,EAAE;IADjB,CAL8C,EAQ9C,EAR8C,CAA/C,CASEC,MATF,CASSC,IATT,CAScC,KATd,CASoB;MACnBI,OAAO,EAAE,IADU;MAEnBH,WAAW,EAAE,cAFM;MAGnBE,KAAK,EAAE;IAHY,CATpB;EAcA,CAfC,CAAF;EAiBAR,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxFP,+CAA+C,CAC9C;MACCS,cAAc,EAAE;IADjB,CAD8C,EAI9C;MACCM,KAAK,EAAE,cADR;MAECN,cAAc,EAAE;IAFjB,CAJ8C,EAQ9C;MACCM,KAAK,EAAE;IADR,CAR8C,CAA/C,CAWEL,MAXF,CAWSC,IAXT,CAWcC,KAXd,CAWoB;MACnBI,OAAO,EAAE,IADU;MAEnBH,WAAW,EAAEC,SAFM;MAGnBC,KAAK,EAAED,SAHY;MAInBG,uBAAuB,EAAEH;IAJN,CAXpB;EAiBA,CAlBC,CAAF,CA/IiE,CAmKjE;;EACAP,EAAE,CAAC,4EAAD,EAA+E,YAAM;IACtFP,+CAA+C,CAC9C;MAAEe,KAAK,EAAE;IAAT,CAD8C,EAE9C,EAF8C,EAG9C,EAH8C,CAA/C,CAIEL,MAJF,CAISC,IAJT,CAIcC,KAJd,CAIoB;MACnBI,OAAO,EAAE,IADU;MAEnBH,WAAW,EAAE,KAFM;MAGnBE,KAAK,EAAE;IAHY,CAJpB;EASA,CAVC,CAAF;EAYAR,EAAE,CAAC,iFAAD,EAAoF,YAAM;IAC3FY,MAAM,CAACnB,+CAA+C,CACrD;MACCe,KAAK,EAAE;IADR,CADqD,EAIrD,EAJqD,EAKrD;MACCA,KAAK,EAAE;IADR,CALqD,CAAhD,CAAN,CAQGK,EARH,CAQMC,EARN,CAQSP,SART;EASA,CAVC,CAAF;EAYAP,EAAE,CAAC,iEAAD,EAAoE,YAAM;IAC3EY,MAAM,CAACnB,+CAA+C,CACrD;MACCe,KAAK,EAAE;IADR,CADqD,EAIrD;MACCA,KAAK,EAAE;IADR,CAJqD,EAOrD,EAPqD,CAAhD,CAAN,CAQGK,EARH,CAQMC,EARN,CAQSP,SART;EASA,CAVC,CAAF;AAWA,CAvMO,CAAR"}