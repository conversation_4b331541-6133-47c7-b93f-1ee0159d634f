# @mui/styled-engine

This package is a wrapper around the `@emotion/react` package.
It also provides a shared interface that can be used with other styled engines, like styled-components.
It is used internally in the `@mui/system` package.

## Documentation

<!-- #default-branch-switch -->

Visit [https://mui.com/material-ui/guides/styled-components/](https://mui.com/material-ui/guides/styled-components/) to view the full documentation.
