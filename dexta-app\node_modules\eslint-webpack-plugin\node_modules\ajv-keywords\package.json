{"name": "ajv-keywords", "version": "5.1.0", "description": "Additional JSON-Schema keywords for Ajv JSON validator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "prepublish": "npm run build", "prettier:write": "prettier --write \"./**/*.{md,json,yaml,js,ts}\"", "prettier:check": "prettier --list-different \"./**/*.{md,json,yaml,js,ts}\"", "test": "npm link && npm link ajv-keywords && npm run eslint && npm run test-cov", "eslint": "eslint \"src/**/*.*s\" \"spec/**/*.*s\"", "test-spec": "jest spec/*.ts", "test-cov": "jest spec/*.ts --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/ajv-keywords.git"}, "keywords": ["JSON-Schema", "ajv", "keywords"], "files": ["src", "dist", "ajv-keywords.d.ts"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/ajv-keywords/issues"}, "homepage": "https://github.com/epoberezkin/ajv-keywords#readme", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^16.4.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.8.2", "ajv-formats": "^2.0.0", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^7.0.1", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^11.1.1", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "prettier": "@ajv-validator/config/prettierrc.json", "husky": {"hooks": {"pre-commit": "lint-staged && npm test"}}, "lint-staged": {"*.{md,json,yaml,js,ts}": "prettier --write"}}