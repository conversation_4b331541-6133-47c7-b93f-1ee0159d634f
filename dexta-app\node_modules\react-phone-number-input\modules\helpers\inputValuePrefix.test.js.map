{"version": 3, "file": "inputValuePrefix.test.js", "names": ["metadata", "getInputValuePrefix", "removeInputValuePrefix", "describe", "it", "country", "should", "equal", "international", "withCountryCallingCode"], "sources": ["../../source/helpers/inputValuePrefix.test.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport { getInputValuePrefix, removeInputValuePrefix } from './inputValuePrefix.js'\r\n\r\ndescribe('inputValuePrefix', () => {\r\n\tit('should get input value prefix', () => {\r\n\t\tgetInputValuePrefix({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tmetadata\r\n\t\t}).should.equal('')\r\n\r\n\t\tgetInputValuePrefix({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tinternational: true,\r\n\t\t\twithCountryCallingCode: true,\r\n\t\t\tmetadata\r\n\t\t}).should.equal('')\r\n\r\n\t\tgetInputValuePrefix({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tinternational: true,\r\n\t\t\tmetadata\r\n\t\t}).should.equal('+7')\r\n\t})\r\n\r\n\tit('should remove input value prefix', () => {\r\n\t\tremoveInputValuePrefix('+78005553535', '+7').should.equal('8005553535')\r\n\t\tremoveInputValuePrefix('****** 555 35 35', '+7').should.equal('800 555 35 35')\r\n\t\tremoveInputValuePrefix('8 (800) 555-35-35', '').should.equal('8 (800) 555-35-35')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gCAArB;AAEA,SAASC,mBAAT,EAA8BC,sBAA9B,QAA4D,uBAA5D;AAEAC,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzCH,mBAAmB,CAAC;MACnBI,OAAO,EAAE,IADU;MAEnBL,QAAQ,EAARA;IAFmB,CAAD,CAAnB,CAGGM,MAHH,CAGUC,KAHV,CAGgB,EAHhB;IAKAN,mBAAmB,CAAC;MACnBI,OAAO,EAAE,IADU;MAEnBG,aAAa,EAAE,IAFI;MAGnBC,sBAAsB,EAAE,IAHL;MAInBT,QAAQ,EAARA;IAJmB,CAAD,CAAnB,CAKGM,MALH,CAKUC,KALV,CAKgB,EALhB;IAOAN,mBAAmB,CAAC;MACnBI,OAAO,EAAE,IADU;MAEnBG,aAAa,EAAE,IAFI;MAGnBR,QAAQ,EAARA;IAHmB,CAAD,CAAnB,CAIGM,MAJH,CAIUC,KAJV,CAIgB,IAJhB;EAKA,CAlBC,CAAF;EAoBAH,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5CF,sBAAsB,CAAC,cAAD,EAAiB,IAAjB,CAAtB,CAA6CI,MAA7C,CAAoDC,KAApD,CAA0D,YAA1D;IACAL,sBAAsB,CAAC,kBAAD,EAAqB,IAArB,CAAtB,CAAiDI,MAAjD,CAAwDC,KAAxD,CAA8D,eAA9D;IACAL,sBAAsB,CAAC,mBAAD,EAAsB,EAAtB,CAAtB,CAAgDI,MAAhD,CAAuDC,KAAvD,CAA6D,mBAA7D;EACA,CAJC,CAAF;AAKA,CA1BO,CAAR"}