# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v1.0.0 - 2021-08-17

### Commits

- Initial commit: pulled from es-abstract [`6e34a05`](https://github.com/inspect-js/get-symbol-description/commit/6e34a05ef10ce8620078cf4cecbe276c1fc1ae77)
- Initial commit [`3862092`](https://github.com/inspect-js/get-symbol-description/commit/3862092248d8ffa071ec90ec39d73e8be14ba6f1)
- [meta] do not publish github action workflow files [`9d1e2b9`](https://github.com/inspect-js/get-symbol-description/commit/9d1e2b94dd97664da5d0666985a3695c23f45865)
- npm init [`5051b32`](https://github.com/inspect-js/get-symbol-description/commit/5051b3221829f364c44b4d5e9a0c35aab3247f6a)
- Only apps should have lockfiles [`b866d1c`](https://github.com/inspect-js/get-symbol-description/commit/b866d1c4b4029277618d968cfb3cbe00f012d1a7)
