{"name": "jest-circus", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-circus"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./runner": "./runner.js"}, "dependencies": {"@jest/environment": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^0.7.0", "expect": "^27.5.1", "is-generator-fn": "^2.0.0", "jest-each": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1", "jest-runtime": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "pretty-format": "^27.5.1", "slash": "^3.0.0", "stack-utils": "^2.0.3", "throat": "^6.0.1"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/register": "^7.0.0", "@types/co": "^4.6.0", "@types/dedent": "^0.7.0", "@types/graceful-fs": "^4.1.3", "@types/stack-utils": "^2.0.0", "execa": "^5.0.0", "graceful-fs": "^4.2.9"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}