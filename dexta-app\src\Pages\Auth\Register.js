import React, { useState, useEffect, useRef } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as Yup from "yup";
import { useFormik } from "formik";
import "./Auth.css";
import { createUser } from "./hooks/createUser";
import { useNavigate, useLocation } from "react-router-dom";
import http from "../../http";
import queryString from "query-string";
import { toast } from "react-toastify";
import { ToastContainer, Zoom } from "react-toastify";
import { useDispatch } from "react-redux";
import { setPackageData } from "../../redux/reducers/packageData/packageDataSlice";
import TextField from "../../Components/Dexta/TextField/TextField";
import CustomButton from "../../Components/CustomButton/CustomButton";
import MainLogo from "../../Dexta_assets/SignupLogov2.png";
import { FaArrowRightLong } from "react-icons/fa6";
import Skeleton from "react-loading-skeleton";
import { FaInfoCircle } from "react-icons/fa";
import {
  setFreeToFalse,
  setFreeToTrue,
} from "../../redux/reducers/FreeUser/FreeUserSlice";
import { isRecruiterAuthenticated } from "../../Helpers/Auth";
import { handlePassword } from "../../Components/Regex/Regex";
import ReCAPTCHA from "react-google-recaptcha";
import { setAccountTypeofUser } from "../../redux/reducers/AccountType/AccountTypeSlice";
import { useTranslation } from "react-i18next";

const Register = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const [errorMessage, setErrorMessage] = useState("");
  const [error, setError] = useState(false);
  const [load, setLoad] = useState(true);
  const navigate = useNavigate();
  const recaptchaRef = useRef(null);
  const [packageNSelected, setPackageNSelected] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [packageDetail, setPackageDetail] = useState({});
  const [notfound, setnotfound] = useState(false);
  const [pkgError, setpkgError] = useState(false);
  const [dispatchLoad, setDispatchLoad] = useState(false);
  const [packageLoad, setPackageLoading] = useState(false);
  const [selectedType, setSelectedType] = useState(parsed?.interval);
  const [passwordValidation, setPasswordValidation] = useState({
    capital: false,
    number: false,
    length: false,
    special: false,
  });
  const [BarPercentage, setBarPercentage] = useState(0);

  //#region calculating trues for bar percentage
  useEffect(() => {
    const trueCount = Object.values(passwordValidation).filter(Boolean).length;
    const newBarPercentage = trueCount * 25;
    setBarPercentage(newBarPercentage);
  }, [passwordValidation]);
  //#endregion

  //#region checking password conditions
  const checkingPassword = (text) => {
    const { hasNumber, hasCapital, hasLength, hasSpecialCharacter } =
      handlePassword(text);
    setPasswordValidation({
      number: hasNumber,
      capital: hasCapital,
      length: hasLength,
      special: hasSpecialCharacter,
    });
  };
  //#endregion

  //#region password hide and unhide
  function togglePasswordVisibility() {
    setIsPasswordVisible((prevState) => !prevState);
  }
  //#endregion

  //#region disable / enable overflow
  useEffect(() => {
    if (parsed.package_id === undefined) {
      setPackageNSelected(true);
    } else if (parsed.package_id === "" || isNaN(parsed.package_id)) {
      setPackageNSelected(true);
    }
  }, []);
  //#endregion

  //#region getdscid
  useEffect(() => {
    if (parsed?.dsc) {
      localStorage.setItem("dsc", parsed.dsc);
      getDsc();
      getCouponsById();
    }
  }, [parsed.dsc]);

  // const getDsc = async () => {
  //   try {
  //     const response = await http.get(
  //       `/coupons/${parsed.dsc}/discounted-price?packageId=${parsed?.package_id}&currency=${parsed?.currency}&interval=${parsed?.interval}`
  //     );
  //     console.log(response,"responseresponse")

  //     localStorage.setItem("dsc-price", response?.data?.discountedPrice);

  //   } catch (error) {
  //     console.log(error.message);

  //   }
  // };
  const getDsc = async () => {
    try {
      const response = await http.get(
        `/coupons/${parsed.dsc}/discounted-price?packageId=${parsed?.package_id}&currency=${parsed?.currency}&interval=${parsed?.interval}`
      );

      localStorage.setItem("dsc-price", response?.data?.discountedPrice);
    } catch (err) {
      console.error("API Error:", err);
      let errorMessage;
      if (err.response?.data?.message) {
        errorMessage = Array.isArray(err.response.data.message)
          ? err.response.data.message[0]
          : err.response.data.message;
      }
      setTimeout(() => {
        toast.error(errorMessage, {
          toastId: "copy-success",
        });
      }, 500);
    }
  };

  const getCouponsById = async () => {
    try {
      const response = await http.get(`/coupons/${parsed.dsc}`);
      console.log(response, "coupons");
      localStorage.setItem("dsc-code", response?.data?.code);
    } catch (err) {
      console.log(err.message);
    }
  };

  useEffect(() => {
    if (packageNSelected) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  }, [packageNSelected]);
  //#endregion

  //#region creating new user
  const { mutate, isLoading } = useMutation(createUser, {
    onSuccess: (response) => {
      queryClient.invalidateQueries("/auth/sign-up");
      validation.values.firstName = "";
      validation.values.lastName = "";
      validation.values.email = "";
      validation.values.company = "";
      validation.values.password = "";
      if (response.accessToken) {
        localStorage.setItem("CP-USER-TOKEN", response.accessToken);
      }
      if (parseInt(parsed?.package_id) === 1) {
        dispatch(setFreeToTrue(true));
      }
      if (parseInt(parsed?.package_id) !== 1) {
        dispatch(setFreeToFalse(false));
      }
      dispatch(setAccountTypeofUser("owner"));
      if (response.user.id) {
        localStorage.setItem("CP-USER-ID", response.user.id);
      }
      // Use an immediately invoked async function to check authentication
      (async () => {
        if (parsed && parsed.package_id) {
          const isAuthenticated = await isRecruiterAuthenticated();

          if (isAuthenticated) {
            if (parsed?.package_id == 1) {
              getPackageUser(response.user.id);
            } else {
              localStorage.setItem("Registration", "initiated");
              if (parsed?.dsc) {
                localStorage.setItem("dsc", parsed.dsc);
                getDsc();
                getCouponsById();
              }
              navigate(
                `/place_order?package_id=${parsed?.package_id}&currency=${parsed?.currency}&interval=${selectedType}`
              );
            }
          }
        } else {
          if (parsed?.dsc) {
            localStorage.setItem("dsc", parsed.dsc);
            getDsc();
            getCouponsById();
          }
          getPackageUser(response.user.id);
        }
      })();
    },
    onError: (error) => {
      const errorMessage = error?.response?.data?.message;
      if (Array.isArray(errorMessage)) {
        setTimeout(() => {
          toast.error(errorMessage[0], {
            toastId: "copy-success",
          });
        }, 500);
      } else if (typeof errorMessage === "string") {
        setTimeout(() => {
          toast.error(errorMessage, {
            toastId: "copy-success",
          });
        }, 500);
      } else {
        setTimeout(() => {
          toast.error("An error occurred.", {
            toastId: "copy-success",
          });
        }, 500);
      }
    },
  });
  //#endregion

  //#region validations on register form
  const validation = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      company: "",
      password: "",
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required("First name required"),
      lastName: Yup.string().required("Last name required"),
      email: Yup.string().required("Email required"),
      password: Yup.string().required("Password required"),
    }),
    onSubmit: async (values) => {
      let token;
      try {
        token = await recaptchaRef.current.executeAsync();
        console.log(token, "token reCaptcha");
      } catch (e) {
        console.error("reCAPTCHA execution failed:", e);
        toast.error("reCAPTCHA validation failed. Please try again.", {
          toastId: "recaptcha-error",
        });
        return;
      }

      let data = JSON.stringify({
        firstName: values?.firstName,
        lastName: values?.lastName,
        companyName: values?.company,
        email: values?.email,
        password1: values?.password,
        password2: values?.password,
        recaptchaToken: token,
        // "referredBy": values?.refer
      });
      try {
        mutate(data);
      } catch (error) {
        //onError will automatically detect
      } finally {
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
        }
      }
    },
  });
  //#endregion

  //#region taking package to place order screen
  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      if (parsed && parsed.package_id) {
        localStorage.setItem("CP-PACKAGE-ID", String(parsed.package_id));

        // Check authentication using the API call instead of checking localStorage
        const isAuthenticated = await isRecruiterAuthenticated();

        if (isAuthenticated) {
          if (parsed.package_id == 1) {
            const ID = parseInt(localStorage.getItem("CP-USER-ID"));
            getPackageUser(ID);
            navigate("/dashboard");
          } else {
            localStorage.setItem("Registration", "initiated");
            navigate(
              `/place_order?package_id=${parsed?.package_id}&currency=${parsed?.currency}&interval=${selectedType}`
            );
          }
        }
      }
    };

    checkAuthAndRedirect();
  }, []);
  //#endregion

  //#region Fetching user package
  const getPackageUser = async (pID) => {
    try {
      setDispatchLoad(true);
      const response = await http.get(
        `/subscriptions/subscriptionsByUserId/${pID}`
      );
      dispatch(setPackageData(response?.data?.package?.code));
      navigate("/dashboard");
      setDispatchLoad(false);
    } catch (err) {
      console.log(err.message);
      setDispatchLoad(false);
    }
  };
  //#endregion

  //#region Fetching package details
  const getPackageDetail = (package_id) => {
    setPackageLoading(true);
    http
      .request({
        method: "get",
        maxBodyLength: Infinity,
        url: `/packages/${package_id}?currency=${parsed?.currency}&interval=${selectedType}`,
      })
      .then((response) => {
        setPackageDetail(response.data);
        const exist = Object.keys(response.data)?.length;
        if (exist === 0) {
          setnotfound(true);
          setpkgError(true);
        }
        setPackageLoading(false);
      })
      .catch((error) => {
        console.log(error);
        setPackageLoading(false);
      });
  };
  useEffect(() => {
    setLoad(true);
    setTimeout(() => {
      setLoad(false);
    }, 1500);
  }, [packageDetail, selectedType]);

  useEffect(() => {
    if (parsed && parsed.package_id) {
      getPackageDetail(Number(parsed.package_id));
    }
  }, [selectedType]);
  //#endregion

  const handleTermsAndPrivacy = (type) => {
    const url = `/candidate-recruiter/privacy-policy`;
    window.open(url, "_blank");
  };

  //#region taking user to top of screen
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  //#endregion
  document.title = "Register | Dexta";
  return (
    <React.Fragment>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />

      <div className="sm:grid-cols-1 lg:grid-cols-2 grid bg-bgAuth h-screen">
        <div className="bg-black sm:hidden lg:block">
          <img src={MainLogo} className="w-full lg:h-full  lg:object-cover" />
        </div>
        <div className="bg-bgAuth flex justify-center flex-col md:pb-40">
          <div className="sm:w-5/6 lg:w-1/2 mx-auto">
            <h1
              className="text-2xl text-left"
              style={{ fontFamily: "Archia Semibold" }}
            >
              {" "}
              {t("register")}
            </h1>
            <div style={{ fontFamily: "Silka" }} className="text-sm mt-6">
              <span>
                {t("already_have_account")}{" "}
                <span
                  className="underline cursor-pointer"
                  onClick={() => navigate("/login")}
                >
                  {t("sign_in")}
                </span>
              </span>
            </div>
            <div
              className={`px-5 py-5 mt-5 rounded border border-black flex justify-between ${
                packageDetail?.code === "free" && "bg-[#C0FF06]"
              }
                        ${packageDetail?.code === "pro" && "bg-[#FFB500]"}
                        ${
                          packageDetail?.code === "Enterprise" && "bg-[#FF5812]"
                        }
                        `}
            >
              <div
                className={`my-auto ${
                  packageDetail?.code === "Enterprise"
                    ? "text-white"
                    : "text-black"
                }`}
                style={{ fontFamily: "Archia Semibold" }}
              >
                {load ? (
                  <Skeleton width={200} height={20} />
                ) : (
                  <>
                    {packageDetail?.code === "free" && "Explore for free"}
                    {packageDetail?.code === "pro" && "Starter package"}
                    {packageDetail?.code === "Enterprise" && "Pro package"}
                  </>
                )}
              </div>
              {selectedType === "year" && "1 year commitment" && (
                <div className="border border-black text-xs my-auto py-1 rounded-xl bg-white px-3">
                  {load ? (
                    <Skeleton width={100} height={20} />
                  ) : (
                    selectedType === "year" && "1 year commitment"
                  )}
                </div>
              )}
            </div>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                validation.handleSubmit();
                return false;
              }}
            >
              <div className="mt-6">
                <div className="grid grid-cols-2 gap-3 h-[4.7rem]">
                  <div className="h-[4.7rem]">
                    <TextField
                      name="firstName"
                      id="firstName"
                      type="name"
                      placeholder={t("enter_first_name")}
                      onChange={validation.handleChange}
                      onBlur={validation.handleBlur}
                      value={validation.values.firstName || ""}
                    />
                    {validation.touched.firstName &&
                    validation.errors.firstName ? (
                      <div className="ml-1">
                        <p className="text-rose-500 fade-in-text-validations sm:text-xs md:text-sm">
                          {t("first_name_required")}
                        </p>
                      </div>
                    ) : null}
                  </div>
                  <div className="h-[4.7rem]">
                    <TextField
                      name="lastName"
                      id="lastName"
                      type="name"
                      placeholder={t("enter_last_name")}
                      onChange={validation.handleChange}
                      onBlur={validation.handleBlur}
                      value={validation.values.lastName || ""}
                    />
                    {validation.touched.lastName &&
                    validation.errors.lastName ? (
                      <div className="ml-w-1/2 ml-1">
                        <p className="text-rose-500 fade-in-text-validations sm:text-xs md:text-sm">
                          {t("last_name_required")}
                        </p>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
              <div className="h-[4.7rem]">
                <TextField
                  name="email"
                  id="email"
                  type="email"
                  placeholder={t("enter_email")}
                  onChange={validation.handleChange}
                  onBlur={validation.handleBlur}
                  marginTop="mt-2"
                  value={validation.values.email || ""}
                />
                {validation.touched.email && validation.errors.email ? (
                  <div className="ml-1">
                    <p className="text-rose-500 fade-in-text-validations sm:text-xs md:text-sm">
                      {t("invalid_email")}
                    </p>
                  </div>
                ) : null}
              </div>
              <div className="h-[4.7rem]">
                <TextField
                  name="company"
                  id="company"
                  type="name"
                  placeholder={t("company_name")}
                  onChange={validation.handleChange}
                  onBlur={validation.handleBlur}
                  marginTop="mt-2"
                  value={validation.values.company || ""}
                />
              </div>
              <div className="h-[4.7rem]">
                <TextField
                  name="password"
                  id="password"
                  type={isPasswordVisible ? "text" : "password"}
                  placeholder={t("enter_password")}
                  onChange={(e) => {
                    validation.handleChange(e);
                    checkingPassword(e.target.value);
                  }}
                  onInput={(e) => {
                    // This will catch browser autofill events
                    checkingPassword(e.target.value);
                  }}
                  onBlur={validation.handleBlur}
                  value={validation.values.password || ""}
                  marginTop="mt-2"
                  onClick={togglePasswordVisibility}
                  passwordIcon={true}
                  isPasswordVisible={isPasswordVisible}
                />
                {validation.touched.password && validation.errors.password ? (
                  <div className="ml-1">
                    <p className="text-rose-500 fade-in-text-validations sm:text-xs md:text-sm">
                      {t("password_requirements")}
                    </p>
                  </div>
                ) : null}
              </div>

              <ul
                class="max-w-md text-sm mt-6 pl-2 text-black list-inside space-y-1"
                style={{ fontFamily: "Silka" }}
              >
                <li class="flex items-center gap-2 rtl:space-x-reverse">
                  {passwordValidation.number ? (
                    <svg
                      class="flex-shrink-0 w-3.5 h-3.5 fade-in-image text-green-700 "
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 16 12"
                    >
                      <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M1 5.917 5.724 10.5 15 1.5"
                      />
                    </svg>
                  ) : (
                    <FaInfoCircle
                      className="w-4 h-4 fade-in-image"
                      color="#252E3A"
                    />
                  )}
                  <span>{t("contains_at_least_one_number")}</span>
                </li>
                <li class="flex items-center gap-2">
                  {passwordValidation.capital ? (
                    <svg
                      class="flex-shrink-0 w-3.5 h-3.5 text-green-700 fade-in-image"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 16 12"
                    >
                      <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M1 5.917 5.724 10.5 15 1.5"
                      />
                    </svg>
                  ) : (
                    <FaInfoCircle
                      className="w-4 h-4 fade-in-image"
                      color="#252E3A"
                    />
                  )}
                  {t("contains_at_least_one_upper_character")}
                </li>
                <li class="flex items-center gap-2">
                  {passwordValidation.special ? (
                    <svg
                      class="flex-shrink-0 w-3.5 h-3.5 text-green-700 fade-in-image"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 16 12"
                    >
                      <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M1 5.917 5.724 10.5 15 1.5"
                      />
                    </svg>
                  ) : (
                    <FaInfoCircle
                      className="w-4 h-4 fade-in-image"
                      color="#252E3A"
                    />
                  )}
                  {t("contains_at_least_one_special_character")}
                </li>
                <li class="flex items-center gap-2">
                  {passwordValidation?.length ? (
                    <svg
                      class="flex-shrink-0 w-3.5 h-3.5 text-green-700 fade-in-image"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 16 12"
                    >
                      <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M1 5.917 5.724 10.5 15 1.5"
                      />
                    </svg>
                  ) : (
                    <FaInfoCircle
                      className="w-4 h-4 fade-in-image"
                      color="#252E3A"
                    />
                  )}
                  {t("contains_at_least_12_characters")}
                </li>
              </ul>
              <CustomButton
                label={t("submit")}
                borderCustom="border border-black text-white"
                paddingY="0.7rem"
                hoverBgColor="#C0FF06"
                hoverTextColor="#252E3A"
                marginTop="mt-4"
                bgColor="#252E3A"
                iconR={FaArrowRightLong}
                noMarginIcon={false}
                autoLeftMargin="ml-auto"
                textMarginBotton="ml-auto"
                LoadingBtn={isLoading || dispatchLoad}
                loadingText={t("SUBMITTING")}
                disabledCheck={BarPercentage < 100}
                disableField={BarPercentage < 100}
                disabledColor="#252E3A"
                disabledTextColor="#FFFFFF"
                Cursor={
                  BarPercentage < 100 ? "cursor-not-allowed" : "cursor-pointer"
                }
              />
              <ReCAPTCHA
                ref={recaptchaRef}
                size="invisible"
                sitekey={process.env.REACT_APP_RECAPTCHA_SITE_KEY}
              />
            </form>
            <p className="text-xs mt-5" style={{ fontFamily: "Silka" }}>
              {t("by_creating_an_account_you_confirm_that_you_have_read_and_agree_to_Dexta's")}{" "}
              <b
                className="text-coalColor font-medium cursor-pointer"
                style={{ fontFamily: "Silka Bold" }}
                onClick={() => navigate("/term-of-use")}
              >
                {t("term_of_use")}
              </b>{" "}
              and{" "}
              <b
                className="text-coalColor font-medium cursor-pointer"
                style={{ fontFamily: "Silka Bold" }}
                onClick={handleTermsAndPrivacy}
              >
                {t("privacy_policy")}
              </b>
            </p>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default Register;
