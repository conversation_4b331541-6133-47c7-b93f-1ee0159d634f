import _formatPhoneNumber, { formatPhoneNumberIntl as _formatPhoneNumberIntl } from './formatPhoneNumber.js';
import metadata from 'libphonenumber-js/min/metadata';

function call(func, _arguments) {
  var args = Array.prototype.slice.call(_arguments);
  args.push(metadata);
  return func.apply(this, args);
}

function formatPhoneNumber() {
  return call(_formatPhoneNumber, arguments);
}

function formatPhoneNumberIntl() {
  return call(_formatPhoneNumberIntl, arguments);
}

describe('formatPhoneNumber', function () {
  it('should format phone numbers', function () {
    expect(function () {
      return formatPhoneNumber();
    }).to["throw"]('must be a string'); // formatPhoneNumber().should.equal('')

    formatPhoneNumber(null).should.equal('');
    formatPhoneNumber('').should.equal('');
    expect(function () {
      return _formatPhoneNumber('+1', 'NATIONAL');
    }).to["throw"]('`metadata` argument not passed');
    expect(function () {
      return _formatPhoneNumber('+12133734253', undefined, metadata);
    }).to["throw"]('Unknown "format"');
    expect(function () {
      return _formatPhoneNumber('+12133734253', '123', metadata);
    }).to["throw"]('Unknown "format"');
    formatPhoneNumber('+1', 'NATIONAL').should.equal('');
    formatPhoneNumber('+12133734253', 'NATIONAL').should.equal('(*************');
    formatPhoneNumber('+12133734253').should.equal('(*************');
    formatPhoneNumber('+12133734253', 'INTERNATIONAL').should.equal('****** 373 4253'); // Deprecated.
    // Legacy `format`s.

    formatPhoneNumber('+12133734253', 'National').should.equal('(*************');
    formatPhoneNumber('+12133734253', 'International').should.equal('****** 373 4253');
  });
  it('should format international phone numbers', function () {
    formatPhoneNumberIntl('+12133734253').should.equal('****** 373 4253');
  });
});
//# sourceMappingURL=formatPhoneNumber.test.js.map