import { useEffect, useState } from "react";
import { Dialog } from "@headlessui/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";
import closeIcon from "../../../../../Dexta_assets/closeModal.png";
import { ToastContainer, toast, Zoom } from "react-toastify";
import CustomButton from "../../../../../Components/CustomButton/CustomButton";
import { updateHiringStatus } from "../hooks/updateHiringStatus";
import queryString from "query-string";
import { useLocation, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import King from "../../../../../Assets/preee.png";
import Premium from "../../../../../Components/Modals/Premium";
import CustomizeHiringEmail from "../../../../../Components/CustomizeEmail/CustomizeHiringEmail";
import { updateAssessmentStatus } from "../hooks/updateStatus";
import { updateHiringStatuses } from "../hooks/updateHiringStatuses";
import { useTranslation } from "react-i18next";

export default function EmailConfirmation({
  EmailConfirmationOpen,
  setEmailConfirmationOpen,
  typeHiring,
  checkboxText,
  firstName,
  lastName,
  uID,
  noCustomization,
  HiringType,
  selectedCandidates,
  setSelectedRows,
  setSelectAll,
  hiringOpen,
  setHiringOpen,
  candidateData,
  setSendAll,
  sendAll,
  emailcandidateData,
}) {
  const [isChecked, setIsChecked] = useState(false);
  const queryClient = useQueryClient();
  const { id } = useParams();
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const [customizeCustomModal, setCustomizeCustomModal] = useState(false);
  const [emailContent, setEmailContent] = useState("");
  const [emailSubject, setEmailSubject] = useState("");
  const [emailType, setEmailType] = useState("");
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const [premiumOpen, setPremiumOpen] = useState(false);
  const { t } = useTranslation();

  //#region updating hiring status
  const { mutate: hiringMutate, isLoading: hiringLoading } = useMutation(
    updateHiringStatus,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/assessment/candidate");
        toast.success(
          t("invite_candidates.toasts.email_confirmation.hiring_stage_changed"),
          {
            toastId: "copy-success",
          }
        );
        setEmailConfirmationOpen(false);
        setIsChecked(false);
        setEmailConfirmationOpen(false);
        setHiringOpen(false);
      },
      onError: () => {},
    }
  );
  const handlehiringChange = () => {
    let data = {
      IDS: {
        userID: uID ? uID : parsed.user_ID,
        Assessment: id,
      },
      postData: JSON.stringify({
        sendApprovalEmail: typeHiring === "PASSED" && isChecked,
        sendInviteEmail: typeHiring === "INVITED FOR INTERVIEW" && isChecked,
        status: typeHiring,
        sendOfferEmail: typeHiring === "OFFER SENT" && isChecked,
        sendRejectionEmail: typeHiring === "REJECTED" && isChecked,
        sendReminderToRespondToOfferEmail:
          typeHiring === "REMINDER TO RESPOND TO OFFER" && isChecked,
        sendHiredEmail: typeHiring === "HIRED" && isChecked,
        sendPositionWithdrawnEmail:
          typeHiring === "POSITION WITHDRAWN" && isChecked,
      }),
    };

    try {
      hiringMutate(data);
    } catch (err) {
      // react-query will handle error
    }
  };
  //#endregion

  //#region updating hiring statuses
  const { mutate: hiringMutateMultiple, isLoading: hiringLoadingMultiple } =
    useMutation(updateHiringStatuses, {
      onSuccess: (response) => {
        queryClient.invalidateQueries(
          "/assessment/multiple/candidate/update/hiring/status/assessment"
        );
        toast.success(
          t("invite_candidates.toasts.email_confirmation.hiring_stage_changed"),
          {
            toastId: "copy-success",
          }
        );
        setSelectedRows([]);
        setEmailConfirmationOpen(false);
        setHiringOpen(false);
        setIsChecked(false);
        setSelectAll(false);
        setEmailConfirmationOpen(false);
        setHiringOpen(false);
        setSendAll(false);
      },
      onError: () => {},
    });

  const handlehiringChangeofMultiple = () => {
    const allCandidateIds =
      emailcandidateData?.map((candidate) => candidate?.candidates?.id) || [];

    console.log(HiringType, "checking hiring");
    let data = {
      IDS: {
        Assessment: id,
      },
      postData: JSON.stringify({
        status:
          HiringType === "Accept"
            ? "OFFER SENT"
            : HiringType === "position"
            ? "POSITION WITHDRAWN"
            : "REJECTED",
        sendOfferEmail: HiringType === "Accept" && isChecked,
        sendRejectionEmail: HiringType === "Reject" && isChecked,
        sendPositionWithdrawnEmail: HiringType === "position" && isChecked,
        userIds: sendAll === true ? allCandidateIds : selectedCandidates,
      }),
    };

    try {
      hiringMutateMultiple(data);
    } catch (err) {
      // react-query will handle error
    }
  };
  //#endregion

  //#region opening rejection template
  const handleRejectEmailTemplate = () => {
    if (typeHiring === "INVITED FOR INTERVIEW") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">We are pleased to inform you that you have been shortlisted for an interview.</span>
        <p><span style="font-size: 16px;">You will soon receive a detailed email from our team with information on the date, time, and location of the interview, as well as any additional instructions to help you prepare.</span></p>
        <p><span style="font-size: 16px;">We look forward to meeting you and discussing how you can contribute to our team!</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
      `);
      setEmailType("INVITED FOR INTERVIEW");
      setCustomizeCustomModal(true);
    } else if (typeHiring === "OFFER SENT") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">We are thrilled to offer you the opportunity to join </span>
        <strong style="font-size: 16px;">{company_name}.</strong>
        <p><span style="font-size: 16px;">You will soon receive an official offer letter from our team, outlining all the important details about your role, salary, benefits, and more.</span></p>
        <p><span style="font-size: 16px;">We are excited to welcome you aboard and are confident that your skills and expertise will make a meaningful impact at <span><strong style="font-size: 16px;">{company_name}.</strong></span></span></p>
        <p><span style="font-size: 16px;">Please don't hesitate to reach out if you have any questions. We look forward to having you on the team!</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>

      `);
      setEmailType("OFFER");
      setCustomizeCustomModal(true);
    } else if (typeHiring === "REJECTED") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">Thank you for applying to the <strong>{role_name}</strong> role. We sincerely appreciate your interest in our company, and we are grateful for the time and energy you've invested in our test.</span>
        <p><span style="font-size: 16px;">Unfortunately, after careful consideration, we will not be able to invite you to the next phase of our selection process. We wish you success in your job search and all of your future endeavors.</span></p>
        <p><span style="font-size: 16px;">Once again, we thank you for your interest in working with us.</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
      `);
      setEmailType("REJECTION");
      setCustomizeCustomModal(true);
    } else if (typeHiring === "REMINDER TO RESPOND TO OFFER") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">We hope this message finds you well. We wanted to follow up on the job offer we sent you from </span>
        <strong style="font-size: 16px;">{company_name}.</strong>
        <p><span style="font-size: 16px;">We are very excited about the prospect of you joining our team and would appreciate your response at your earliest convenience.</span></p>
        <p><span style="font-size: 16px;">If you have any questions or need further information to make your decision, please do not hesitate to reach out to us.</span></p>
        <p><span style="font-size: 16px;">We look forward to hearing from you soon and hope to welcome you to <span><strong style="font-size: 16px;">{company_name}.</strong></span></span></p>
        `);
      setEmailType("REMINDER TO RESPOND TO OFFER");
      setCustomizeCustomModal(true);
    } else if (typeHiring === "HIRED") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <p><span style="font-size: 16px;">We are delighted to officially welcome you to the</span>
       <strong style="font-size: 16px;"> {company_name}</strong> team.</p>
        <p><span style="font-size: 16px;">Your skills, experience, and enthusiasm truly stood out during the interview process, and we're excited to have you join us. We're confident that you'll be a valuable addition to the team and look forward to seeing the impact you'll make on our projects and goals.</span></p>
        <p><span style="font-size: 16px;">If you have any questions or need further details to help with your transition, please don't hesitate to reach out.</span></p>
        <p><span style="font-size: 16px;">Once again, congratulations and welcome to the team!</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
        `);
      setEmailType("HIRED");
      setCustomizeCustomModal(true);
    } else if (typeHiring === "PASSED") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p><span style="font-size: 16px;">We are excited to inform you that after a thorough review and evaluation by the recruitment team at <strong>{company_name}</strong>, you have successfully passed the evaluation process!.</span></p>
        <p><span style="font-size: 16px;">We will soon contact you with more details and the next steps in the hiring process, such as the offer letter and any additional information you may need.</span></p>
        <p><span style="font-size: 16px;">Congratulations once again! We look forward to welcoming you to the team and working together in the near future.</span></p>
        `);
      setEmailType("APPROVAL");
      setCustomizeCustomModal(true);
    } else if (typeHiring === "POSITION WITHDRAWN") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">Thank you for your interest in the position at <strong>{company_name}</strong> and for taking the time to apply.</span>
        <p><span style="font-size: 16px;">We regret to inform you that the position you applied for has been withdrawn and is no longer available. We appreciate the effort you put into your application and the interest you have shown in our company.</span></p>
        <p><span style="font-size: 16px;">We encourage you to keep an eye on our careers page for future openings that may match your skills and experience.</span></p>
        <p><span style="font-size: 16px;">Thank you once again for considering <strong>{company_name}</strong> as a potential employer.</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
        `);
      setEmailType("POSITION WITHDRAWN");
      setCustomizeCustomModal(true);
    }
  };

  const handleRejectEmailTemplateMultiple = () => {
    console.log(HiringType, "hhh");
    if (HiringType === "Accept") {
      setEmailContent(`
   <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">We are thrilled to offer you the opportunity to join </span>
        <strong style="font-size: 16px;">{company_name}.</strong>
        <p><span style="font-size: 16px;">You will soon receive an official offer letter from our team, outlining all the important details about your role, salary, benefits, and more.</span></p>
        <p><span style="font-size: 16px;">We are excited to welcome you aboard and are confident that your skills and expertise will make a meaningful impact at <span><strong style="font-size: 16px;">{company_name}.</strong></span></span></p>
        <p><span style="font-size: 16px;">Please don't hesitate to reach out if you have any questions. We look forward to having you on the team!</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
      `);
      setEmailType("OFFER");
      setCustomizeCustomModal(true);
    } else if (HiringType === "position") {
      setEmailContent(`
        <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">Thank you for your interest in the position at <strong>{company_name}</strong> and for taking the time to apply.</span>
        <p><span style="font-size: 16px;">We regret to inform you that the position you applied for has been withdrawn and is no longer available. We appreciate the effort you put into your application and the interest you have shown in our company.</span></p>
        <p><span style="font-size: 16px;">We encourage you to keep an eye on our careers page for future openings that may match your skills and experience.</span></p>
        <p><span style="font-size: 16px;">Thank you once again for considering <strong>{company_name}</strong> as a potential employer.</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
        `);
      setEmailType("POSITION WITHDRAWN");
      setCustomizeCustomModal(true);
    } else {
      setEmailContent(`
         <h1><strong style="font-size: 16px;">Dear {candidate_name}</strong></h1>
        <p>
        <span style="font-size: 16px;">Thank you for applying to the <strong>{role_name}</strong> role. We sincerely appreciate your interest in our company, and we are grateful for the time and energy you've invested in our test.</span>
        <p><span style="font-size: 16px;">Unfortunately, after careful consideration, we will not be able to invite you to the next phase of our selection process. We wish you success in your job search and all of your future endeavors.</span></p>
        <p><span style="font-size: 16px;">Once again, we thank you for your interest in working with us.</span></p>
        <p><span style="font-size: 16px;">Kind regards,</span></p>
        <p><strong style="font-size: 16px;">{company_name}</strong></p>
      `);
      setEmailType("REJECTION");
      setCustomizeCustomModal(true);
    }
  };
  //#endregion

  useEffect(() => {
    if (isChecked) {
      if (noCustomization) {
        handlehiringChangeofMultiple();
      } else {
        handlehiringChange();
      }
    }
  }, [isChecked]);

  return (
    <Dialog
      open={EmailConfirmationOpen}
      onClose={() => {
        setIsChecked(false);
        if (noCustomization) {
          handlehiringChangeofMultiple();
        } else {
          handlehiringChange();
        }
      }}
      className="fixed inset-0 z-40 flex items-center justify-center overflow-y-auto "
    >
      <CustomizeHiringEmail
        customizeCustomModal={customizeCustomModal}
        setCustomizeCustomModal={setCustomizeCustomModal}
        emailContent={emailContent}
        emailSubject={emailSubject}
        emailType={emailType}
        passCheck={true}
        setEmailConfirmationOpen={setEmailConfirmationOpen}
        firstName={noCustomization ? "Candidate" : firstName}
        lastName={lastName}
      />
      <Premium premiumOpen={premiumOpen} setPremiumOpen={setPremiumOpen} />
      <div className="fixed inset-0 bg-black bg-opacity-0" />
      <Dialog.Panel className="relative bg-white rounded-lg overflow-hidden shadow-lg sm:pb-2 md:pb-0 transform transition-all sm:max-w-lg sm:w-full md:h-[13rem]">
        <div className="bg-white">
          <p
            className="text-lg font-medium text-coalColor pl-4 cursor-pointer w-5/6 hover:text-coalColor text-left pt-5"
            style={{ fontFamily: "Archia Bold" }}
          >
            {checkboxText}
          </p>
          <img
            src={closeIcon}
            className="absolute top-3 right-5 z-20 w-6 h-6 cursor-pointer"
            onClick={() => {
              setIsChecked(false);
              if (noCustomization) {
                handlehiringChangeofMultiple();
              } else {
                handlehiringChange();
              }
            }}
          />
          <p
            className="flex sm:flex-col md:flex-row mt-3 pl-4"
            style={{ fontFamily: "Silka" }}
          >
            {user_package_check !== "Enterprise" && (
              <img src={King} className="w-4 h-4 my-auto mr-2" />
            )}
            {t("invite_candidates.toasts.email_confirmation.customise_email")}{" "}
            <p
              className="text-black font-bold ml-1 underline cursor-pointer"
              style={{ fontFamily: "Archia Bold" }}
              onClick={() => {
                if (user_package_check !== "Enterprise") {
                  setPremiumOpen(true);
                } else {
                  if (noCustomization) {
                    handleRejectEmailTemplateMultiple();
                  } else {
                    handleRejectEmailTemplate();
                  }
                }
              }}
            >
              {t("invite_candidates.email_confirmation.here")}
            </p>
            .
          </p>
          <div className="ml-4 mt-5">
            <div className="flex flex-row justify-end px-8 gap-5 mt-8">
              <CustomButton
                label={t("invite_candidates.toasts.email_confirmation.cancel")}
                textSize="text-base"
                borderCustom="border border-coalColor text-white"
                bgColor="#252E3A"
                hoverBgColor="#C0FF06"
                hoverTextColor="#252E3A"
                widthButton="w-[7rem]"
                paddingx="px-2"
                paddingY="0.3rem"
                textColor="black"
                loadingColor="white"
                LoadingBtn={
                  !isChecked && (hiringLoading || hiringLoadingMultiple)
                }
                // loadingText={t(
                //   "invite_candidates.toasts.email_confirmation.saving"
                // )}
                onClickButton={() => {
                  setIsChecked(false);
                  if (noCustomization) {
                    handlehiringChangeofMultiple();
                  } else {
                    handlehiringChange();
                  }
                }}
              />
              <CustomButton
                label={t(
                  "invite_candidates.toasts.email_confirmation.send_email"
                )}
                textSize="text-base"
                bgColor="#C0FF06"
                widthButton="w-auto"
                textColor="black"
                paddingx="px-2"
                borderCustom="border border-black text-coalColor"
                hoverBgColor="#252E3A"
                hoverTextColor="white"
                paddingY="0.3rem"
                disabledColor="#D3D5D8"
                disabledTextColor="#7C8289"
                loadingColor="black"
                LoadingBtn={
                  isChecked && (hiringLoading || hiringLoadingMultiple)
                }
                loadingText={t(
                  "invite_candidates.toasts.email_confirmation.sending"
                )}
                onClickButton={() => {
                  setIsChecked(true);
                }}
              />
            </div>
          </div>
        </div>
      </Dialog.Panel>
    </Dialog>
  );
}
