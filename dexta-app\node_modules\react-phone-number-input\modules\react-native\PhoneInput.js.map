{"version": 3, "file": "PhoneInput.js", "names": ["React", "PropTypes", "PhoneTextInput", "PhoneInput_", "InputBasic", "metadata", "metadataType", "createPhoneInput", "defaultMetadata", "PhoneInput", "ref", "inputComponent", "rest", "forwardRef", "propTypes", "elementType"], "sources": ["../../source/react-native/PhoneInput.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport PhoneTextInput from './PhoneTextInput.js'\r\nimport PhoneInput_ from '../PhoneInput.js'\r\nimport InputBasic from '../InputBasic.js'\r\n\r\nimport { metadata as metadataType } from '../PropTypes.js'\r\n\r\n/**\r\n * This is an _experimental_ React Native component.\r\n * Feedback thread: https://github.com/catamphetamine/react-phone-number-input/issues/296\r\n */\r\nexport function createPhoneInput(defaultMetadata) {\r\n\tlet PhoneInput = ({\r\n\t\tinputComponent,\r\n\t\tmetadata = defaultMetadata,\r\n\t\t...rest\r\n\t}, ref) => (\r\n\t\t<PhoneInput_\r\n\t\t\t{...rest}\r\n\t\t\tref={ref}\r\n\t\t\tmetadata={metadata}\r\n\t\t\tComponent={InputBasic}\r\n\t\t\tinputComponent={PhoneTextInput}\r\n\t\t\tTextInputComponent={inputComponent}\r\n\t\t/>\r\n\t)\r\n\r\n\tPhoneInput = React.forwardRef(PhoneInput)\r\n\r\n\tPhoneInput.propTypes = {\r\n\t\t/**\r\n\t\t * Allows specifying a custom input field component,\r\n\t\t * like a \"Material UI\" input field or something.\r\n\t\t */\r\n\t\tinputComponent: PropTypes.elementType,\r\n\r\n\t\t/**\r\n\t\t * `libphonenumber-js` metadata.\r\n\t\t */\r\n\t\tmetadata: metadataType\r\n\t}\r\n\r\n\treturn PhoneInput\r\n}\r\n\r\nexport default createPhoneInput()"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,SAAP,MAAsB,YAAtB;AAEA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,OAAOC,UAAP,MAAuB,kBAAvB;AAEA,SAASC,QAAQ,IAAIC,YAArB,QAAyC,iBAAzC;AAEA;AACA;AACA;AACA;;AACA,OAAO,SAASC,gBAAT,CAA0BC,eAA1B,EAA2C;EACjD,IAAIC,UAAU,GAAG,0BAIdC,GAJc;IAAA,IAChBC,cADgB,QAChBA,cADgB;IAAA,yBAEhBN,QAFgB;IAAA,IAEhBA,QAFgB,8BAELG,eAFK;IAAA,IAGbI,IAHa;;IAAA,oBAKhB,oBAAC,WAAD,eACKA,IADL;MAEC,GAAG,EAAEF,GAFN;MAGC,QAAQ,EAAEL,QAHX;MAIC,SAAS,EAAED,UAJZ;MAKC,cAAc,EAAEF,cALjB;MAMC,kBAAkB,EAAES;IANrB,GALgB;EAAA,CAAjB;;EAeAF,UAAU,gBAAGT,KAAK,CAACa,UAAN,CAAiBJ,UAAjB,CAAb;EAEAA,UAAU,CAACK,SAAX,GAAuB;IACtB;AACF;AACA;AACA;IACEH,cAAc,EAAEV,SAAS,CAACc,WALJ;;IAOtB;AACF;AACA;IACEV,QAAQ,EAAEC;EAVY,CAAvB;EAaA,OAAOG,UAAP;AACA;AAED,eAAeF,gBAAgB,EAA/B"}