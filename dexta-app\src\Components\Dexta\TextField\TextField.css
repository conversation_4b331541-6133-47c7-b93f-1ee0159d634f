/* Autofill detection */
@keyframes onAutoFillStart {
  from {/**/}
  to {/**/}
}

@keyframes onAutoFillCancel {
  from {/**/}
  to {/**/}
}

input:-webkit-autofill {
  animation-name: onAutoFillStart;
  transition: background-color 50000s ease-in-out 0s;
}

input:not(:-webkit-autofill) {
  animation-name: onAutoFillCancel;
}

/* Ensure text is visible during autofill */
input:-webkit-autofill {
  -webkit-text-fill-color: #000;
  caret-color: #000;
}
