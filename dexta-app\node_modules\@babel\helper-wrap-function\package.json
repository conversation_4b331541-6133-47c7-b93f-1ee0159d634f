{"name": "@babel/helper-wrap-function", "version": "7.22.10", "description": "Helper to wrap functions inside a function call.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-function-name": "^7.22.5", "@babel/template": "^7.22.5", "@babel/types": "^7.22.10"}, "engines": {"node": ">=6.9.0"}, "devDependencies": {"@babel/traverse": "^7.22.10"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}