{"name": "eslint-module-utils", "version": "2.8.0", "description": "Core utilities to support eslint-plugin-import and other module-related plugins.", "engines": {"node": ">=4"}, "scripts": {"prepublishOnly": "cp ../{LICENSE,.npmrc} ./", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/import-js/eslint-plugin-import.git"}, "keywords": ["eslint-plugin-import", "eslint", "modules", "<PERSON><PERSON><PERSON><PERSON>"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/import-js/eslint-plugin-import/issues"}, "homepage": "https://github.com/import-js/eslint-plugin-import#readme", "dependencies": {"debug": "^3.2.7"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}