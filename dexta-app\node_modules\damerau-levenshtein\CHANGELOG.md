# Change Log

All notable changes to this project will be documented in this file. The format is based on [Keep a Changelog](http://keepachangelog.com/). This project adheres to [Semantic Versioning](http://semver.org/).

## [Unreleased]


## [1.0.8] - 2021-12-20

Security:
- Upgrade mocha to > 9.0.0 (fixes three security vulnerabilities brought by transitive dependencies)

## [1.0.7] - 2021-05-05

Fixed:
- The scripts/update-changelog.sh wouldn't work without gsed in path

Security:
- Upgrade lodash (transitive development dependency) #17
- Upgrade yargs/y18n (transitive development dependency) #18


## [1.0.6] - 2020-01-27

Changed:
- Upgrade lodash to version 4.17.15 #16

## [1.0.5] - 2019-05-09

Changed:
- Upgrade Mocha to version 6.1.4 #12 by @whyboris 
- Example use in README.md by @whyboris

## [1.0.4] - 2017-03-24

Fixed:
- Fails in strict mode #7 by @gilly3

## [1.0.3] - 2016-09-26

Fixed:
- A title of this document :P

Added:
- List of contributors
- Bugs URL
- Git repository URL

## [1.0.2] - 2016-09-26

Fixed:
- Similarity 0 returned for equal strings #4 by @tad-lispy

## [1.0.1] - 2016-09-12

Fixed:
- Wrong results for transposition #2 by @g-adolph

Added:
- First unit test by @g-adolph
- A Change Log :) by @tad-lispy

## [1.0.0] - 2016-02-23

Fixed:
- Update README to match the actual output by @gilly3

## [0.1.3] - 2013-09-02

Fixed:
- Clear matrix on each call @tad-lispy
- Always return an object @tad-lispy

## [0.1.2] - 2013-08-29

Added:
- ReadMe

## [0.1.1] - 2013-08-28

Added:
- Initial working release @tad-lispy
