
# Node.js + ipapi (IP address location API)

## Installation
```
npm install ipapi.co
```

## Usage

### From Node.js REPL

```
var ipapi = require('ipapi.co');

var callback = function(loc){
    console.log(loc);
};

ipapi.location(callback)       // Complete location for your IP address
> { 
    ip: '********',
    city: 'Wilton',
    region: 'California',
    country: 'US',
    postal: 95693,
    latitude: 38.3926,
    longitude: -121.2429,
    timezone: 'America/Los_Angeles' 
  }


ipapi.location(callback, '', '', 'ip')         // Your external IP address
********

ipapi.location(callback, '', '', 'city')       // Your city
Wilton

ipapi.location(callback, '', '', 'country')    // Your country
US

ipapi.location(callback, '*******')            // Complete location for IP address *******
> { 
    ip: '*******',
    city: 'Mountain View',
    region: 'California',
    country: 'US',
    postal: '94035',
    latitude: 37.386,
    longitude: -122.0838,
    timezone: 'America/Los_Angeles' 
}

ipapi.location(callback, '*******', '', 'city')       // City for IP address *******
Mountain View

ipapi.location(callback, '*******', '', 'country')       // Country for IP address *******
US
```



### With API Key

API key can be specified in the following ways : 

1. Inside `ipapi.js` by setting `API_KEY` variable
2. As a function argument e.g. `ipapi.location(callback, '*******', 'secret-key')`


### Function arguments
- Optional arguments (e.g. IP address, key, field) can be an empty string `''` or `undefined`
