{"name": "is-array-buffer", "version": "3.0.2", "description": "Is this value a JS ArrayBuffer?", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=.js,.mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only --", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-array-buffer.git"}, "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "available-typed-arrays": "^1.0.5", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "tape": "^5.6.3"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "2.0.1"}, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "publishConfig": {"ignore": [".github/workflows"]}}