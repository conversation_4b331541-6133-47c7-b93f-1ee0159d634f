/**
 * @mui/system v5.14.12
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  experimental_sx: true,
  css: true,
  keyframes: true,
  StyledEngineProvider: true,
  GlobalStyles: true,
  borders: true,
  breakpoints: true,
  handleBreakpoints: true,
  mergeBreakpointsInOrder: true,
  unstable_resolveBreakpointValues: true,
  compose: true,
  display: true,
  flexbox: true,
  grid: true,
  palette: true,
  positions: true,
  shadows: true,
  sizing: true,
  spacing: true,
  style: true,
  getPath: true,
  getStyleValue: true,
  typography: true,
  unstable_styleFunctionSx: true,
  unstable_createStyleFunctionSx: true,
  unstable_extendSxProp: true,
  unstable_defaultSxConfig: true,
  unstable_getThemeValue: true,
  Box: true,
  createBox: true,
  createStyled: true,
  styled: true,
  createTheme: true,
  createBreakpoints: true,
  createSpacing: true,
  shape: true,
  useThemeProps: true,
  getThemeProps: true,
  useTheme: true,
  useThemeWithoutDefault: true,
  ThemeProvider: true,
  unstable_createCssVarsProvider: true,
  unstable_createGetCssVar: true,
  unstable_cssVarsParser: true,
  unstable_prepareCssVars: true,
  unstable_createCssVarsTheme: true,
  responsivePropType: true,
  createContainer: true,
  Container: true,
  Unstable_Grid: true,
  Stack: true
};
Object.defineProperty(exports, "Box", {
  enumerable: true,
  get: function () {
    return _Box.default;
  }
});
Object.defineProperty(exports, "Container", {
  enumerable: true,
  get: function () {
    return _Container.default;
  }
});
Object.defineProperty(exports, "GlobalStyles", {
  enumerable: true,
  get: function () {
    return _GlobalStyles.default;
  }
});
Object.defineProperty(exports, "Stack", {
  enumerable: true,
  get: function () {
    return _Stack.default;
  }
});
Object.defineProperty(exports, "StyledEngineProvider", {
  enumerable: true,
  get: function () {
    return _styledEngine.StyledEngineProvider;
  }
});
Object.defineProperty(exports, "ThemeProvider", {
  enumerable: true,
  get: function () {
    return _ThemeProvider.default;
  }
});
Object.defineProperty(exports, "Unstable_Grid", {
  enumerable: true,
  get: function () {
    return _Grid.default;
  }
});
Object.defineProperty(exports, "borders", {
  enumerable: true,
  get: function () {
    return _borders.default;
  }
});
Object.defineProperty(exports, "breakpoints", {
  enumerable: true,
  get: function () {
    return _breakpoints.default;
  }
});
Object.defineProperty(exports, "compose", {
  enumerable: true,
  get: function () {
    return _compose.default;
  }
});
Object.defineProperty(exports, "createBox", {
  enumerable: true,
  get: function () {
    return _createBox.default;
  }
});
Object.defineProperty(exports, "createBreakpoints", {
  enumerable: true,
  get: function () {
    return _createBreakpoints.default;
  }
});
Object.defineProperty(exports, "createContainer", {
  enumerable: true,
  get: function () {
    return _createContainer.default;
  }
});
Object.defineProperty(exports, "createSpacing", {
  enumerable: true,
  get: function () {
    return _createSpacing.default;
  }
});
Object.defineProperty(exports, "createStyled", {
  enumerable: true,
  get: function () {
    return _createStyled.default;
  }
});
Object.defineProperty(exports, "createTheme", {
  enumerable: true,
  get: function () {
    return _createTheme.default;
  }
});
Object.defineProperty(exports, "css", {
  enumerable: true,
  get: function () {
    return _styledEngine.css;
  }
});
Object.defineProperty(exports, "display", {
  enumerable: true,
  get: function () {
    return _display.default;
  }
});
exports.experimental_sx = experimental_sx;
Object.defineProperty(exports, "flexbox", {
  enumerable: true,
  get: function () {
    return _flexbox.default;
  }
});
Object.defineProperty(exports, "getPath", {
  enumerable: true,
  get: function () {
    return _style.getPath;
  }
});
Object.defineProperty(exports, "getStyleValue", {
  enumerable: true,
  get: function () {
    return _style.getStyleValue;
  }
});
Object.defineProperty(exports, "getThemeProps", {
  enumerable: true,
  get: function () {
    return _useThemeProps.getThemeProps;
  }
});
Object.defineProperty(exports, "grid", {
  enumerable: true,
  get: function () {
    return _cssGrid.default;
  }
});
Object.defineProperty(exports, "handleBreakpoints", {
  enumerable: true,
  get: function () {
    return _breakpoints.handleBreakpoints;
  }
});
Object.defineProperty(exports, "keyframes", {
  enumerable: true,
  get: function () {
    return _styledEngine.keyframes;
  }
});
Object.defineProperty(exports, "mergeBreakpointsInOrder", {
  enumerable: true,
  get: function () {
    return _breakpoints.mergeBreakpointsInOrder;
  }
});
Object.defineProperty(exports, "palette", {
  enumerable: true,
  get: function () {
    return _palette.default;
  }
});
Object.defineProperty(exports, "positions", {
  enumerable: true,
  get: function () {
    return _positions.default;
  }
});
Object.defineProperty(exports, "responsivePropType", {
  enumerable: true,
  get: function () {
    return _responsivePropType.default;
  }
});
Object.defineProperty(exports, "shadows", {
  enumerable: true,
  get: function () {
    return _shadows.default;
  }
});
Object.defineProperty(exports, "shape", {
  enumerable: true,
  get: function () {
    return _shape.default;
  }
});
Object.defineProperty(exports, "sizing", {
  enumerable: true,
  get: function () {
    return _sizing.default;
  }
});
Object.defineProperty(exports, "spacing", {
  enumerable: true,
  get: function () {
    return _spacing.default;
  }
});
Object.defineProperty(exports, "style", {
  enumerable: true,
  get: function () {
    return _style.default;
  }
});
Object.defineProperty(exports, "styled", {
  enumerable: true,
  get: function () {
    return _styled.default;
  }
});
Object.defineProperty(exports, "typography", {
  enumerable: true,
  get: function () {
    return _typography.default;
  }
});
Object.defineProperty(exports, "unstable_createCssVarsProvider", {
  enumerable: true,
  get: function () {
    return _createCssVarsProvider.default;
  }
});
Object.defineProperty(exports, "unstable_createCssVarsTheme", {
  enumerable: true,
  get: function () {
    return _createCssVarsTheme.default;
  }
});
Object.defineProperty(exports, "unstable_createGetCssVar", {
  enumerable: true,
  get: function () {
    return _createGetCssVar.default;
  }
});
Object.defineProperty(exports, "unstable_createStyleFunctionSx", {
  enumerable: true,
  get: function () {
    return _styleFunctionSx.unstable_createStyleFunctionSx;
  }
});
Object.defineProperty(exports, "unstable_cssVarsParser", {
  enumerable: true,
  get: function () {
    return _cssVarsParser.default;
  }
});
Object.defineProperty(exports, "unstable_defaultSxConfig", {
  enumerable: true,
  get: function () {
    return _styleFunctionSx.unstable_defaultSxConfig;
  }
});
Object.defineProperty(exports, "unstable_extendSxProp", {
  enumerable: true,
  get: function () {
    return _styleFunctionSx.extendSxProp;
  }
});
Object.defineProperty(exports, "unstable_getThemeValue", {
  enumerable: true,
  get: function () {
    return _getThemeValue.default;
  }
});
Object.defineProperty(exports, "unstable_prepareCssVars", {
  enumerable: true,
  get: function () {
    return _prepareCssVars.default;
  }
});
Object.defineProperty(exports, "unstable_resolveBreakpointValues", {
  enumerable: true,
  get: function () {
    return _breakpoints.resolveBreakpointValues;
  }
});
Object.defineProperty(exports, "unstable_styleFunctionSx", {
  enumerable: true,
  get: function () {
    return _styleFunctionSx.default;
  }
});
Object.defineProperty(exports, "useTheme", {
  enumerable: true,
  get: function () {
    return _useTheme.default;
  }
});
Object.defineProperty(exports, "useThemeProps", {
  enumerable: true,
  get: function () {
    return _useThemeProps.default;
  }
});
Object.defineProperty(exports, "useThemeWithoutDefault", {
  enumerable: true,
  get: function () {
    return _useThemeWithoutDefault.default;
  }
});
var _utils = require("@mui/utils");
var _styledEngine = require("@mui/styled-engine");
var _GlobalStyles = _interopRequireDefault(require("./GlobalStyles"));
var _borders = _interopRequireWildcard(require("./borders"));
Object.keys(_borders).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _borders[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _borders[key];
    }
  });
});
var _breakpoints = _interopRequireWildcard(require("./breakpoints"));
var _compose = _interopRequireDefault(require("./compose"));
var _display = _interopRequireDefault(require("./display"));
var _flexbox = _interopRequireWildcard(require("./flexbox"));
Object.keys(_flexbox).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _flexbox[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _flexbox[key];
    }
  });
});
var _cssGrid = _interopRequireWildcard(require("./cssGrid"));
Object.keys(_cssGrid).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _cssGrid[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _cssGrid[key];
    }
  });
});
var _palette = _interopRequireWildcard(require("./palette"));
Object.keys(_palette).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _palette[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _palette[key];
    }
  });
});
var _positions = _interopRequireWildcard(require("./positions"));
Object.keys(_positions).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _positions[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _positions[key];
    }
  });
});
var _shadows = _interopRequireDefault(require("./shadows"));
var _sizing = _interopRequireWildcard(require("./sizing"));
Object.keys(_sizing).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _sizing[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _sizing[key];
    }
  });
});
var _spacing = _interopRequireWildcard(require("./spacing"));
Object.keys(_spacing).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _spacing[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _spacing[key];
    }
  });
});
var _style = _interopRequireWildcard(require("./style"));
var _typography = _interopRequireWildcard(require("./typography"));
Object.keys(_typography).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _typography[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _typography[key];
    }
  });
});
var _styleFunctionSx = _interopRequireWildcard(require("./styleFunctionSx"));
var _getThemeValue = _interopRequireDefault(require("./getThemeValue"));
var _Box = _interopRequireDefault(require("./Box"));
var _createBox = _interopRequireDefault(require("./createBox"));
var _createStyled = _interopRequireWildcard(require("./createStyled"));
Object.keys(_createStyled).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _createStyled[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _createStyled[key];
    }
  });
});
var _styled = _interopRequireDefault(require("./styled"));
var _createTheme = _interopRequireDefault(require("./createTheme"));
var _createBreakpoints = _interopRequireDefault(require("./createTheme/createBreakpoints"));
var _createSpacing = _interopRequireDefault(require("./createTheme/createSpacing"));
var _shape = _interopRequireDefault(require("./createTheme/shape"));
var _useThemeProps = _interopRequireWildcard(require("./useThemeProps"));
var _useTheme = _interopRequireDefault(require("./useTheme"));
var _useThemeWithoutDefault = _interopRequireDefault(require("./useThemeWithoutDefault"));
var _colorManipulator = require("./colorManipulator");
Object.keys(_colorManipulator).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _colorManipulator[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _colorManipulator[key];
    }
  });
});
var _ThemeProvider = _interopRequireDefault(require("./ThemeProvider"));
var _createCssVarsProvider = _interopRequireDefault(require("./cssVars/createCssVarsProvider"));
var _createGetCssVar = _interopRequireDefault(require("./cssVars/createGetCssVar"));
var _cssVarsParser = _interopRequireDefault(require("./cssVars/cssVarsParser"));
var _prepareCssVars = _interopRequireDefault(require("./cssVars/prepareCssVars"));
var _createCssVarsTheme = _interopRequireDefault(require("./cssVars/createCssVarsTheme"));
var _responsivePropType = _interopRequireDefault(require("./responsivePropType"));
var _createContainer = _interopRequireDefault(require("./Container/createContainer"));
var _Container = _interopRequireWildcard(require("./Container"));
Object.keys(_Container).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _Container[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Container[key];
    }
  });
});
var _Grid = _interopRequireDefault(require("./Unstable_Grid/Grid"));
var _Unstable_Grid = require("./Unstable_Grid");
Object.keys(_Unstable_Grid).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _Unstable_Grid[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Unstable_Grid[key];
    }
  });
});
var _Stack = _interopRequireDefault(require("./Stack/Stack"));
var _Stack2 = require("./Stack");
Object.keys(_Stack2).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _Stack2[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Stack2[key];
    }
  });
});
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// TODO: Remove this function in v6
// eslint-disable-next-line @typescript-eslint/naming-convention
function experimental_sx() {
  throw new Error(process.env.NODE_ENV !== "production" ? `MUI: The \`experimental_sx\` has been moved to \`theme.unstable_sx\`.For more details, see https://github.com/mui/material-ui/pull/35150.` : (0, _utils.formatMuiErrorMessage)(20));
}

/** ----------------- */
/** Layout components */