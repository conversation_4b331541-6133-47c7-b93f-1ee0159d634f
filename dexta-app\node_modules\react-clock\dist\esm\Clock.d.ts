import React from 'react';
import type { formatHour as defaultFormatHour } from './shared/hourFormatter.js';
import type { ClassName, HandLength, HandWidth, MarkLength, MarkWidth, OppositeHandLength } from './shared/types.js';
export type ClockProps = {
    className?: ClassName;
    formatHour?: typeof defaultFormatHour;
    hourHandLength?: HandLength;
    hourHandOppositeLength?: OppositeHandLength;
    hourHandWidth?: HandWidth;
    hourMarksLength?: MarkLength;
    hourMarksWidth?: MarkWidth;
    locale?: string;
    minuteHandLength?: HandLength;
    minuteHandOppositeLength?: OppositeHandLength;
    minuteHandWidth?: HandWidth;
    minuteMarksLength?: MarkLength;
    minuteMarksWidth?: MarkWidth;
    renderHourMarks?: boolean;
    renderMinuteHand?: boolean;
    renderMinuteMarks?: boolean;
    renderNumbers?: boolean;
    renderSecondHand?: boolean;
    secondHandLength?: HandLength;
    secondHandOppositeLength?: OppositeHandLength;
    secondHandWidth?: HandWidth;
    size?: React.CSSProperties['width'];
    useMillisecondPrecision?: boolean;
    value?: string | Date | null;
};
declare const Clock: React.FC<ClockProps>;
export default Clock;
