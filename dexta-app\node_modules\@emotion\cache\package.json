{"name": "@emotion/cache", "version": "11.11.0", "description": "emotion's cache", "main": "dist/emotion-cache.cjs.js", "module": "dist/emotion-cache.esm.js", "browser": {"./dist/emotion-cache.esm.js": "./dist/emotion-cache.browser.esm.js"}, "exports": {".": {"module": {"worker": "./dist/emotion-cache.worker.esm.js", "browser": "./dist/emotion-cache.browser.esm.js", "default": "./dist/emotion-cache.esm.js"}, "import": "./dist/emotion-cache.cjs.mjs", "default": "./dist/emotion-cache.cjs.js"}, "./package.json": "./package.json"}, "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/cache", "scripts": {"test:typescript": "dtslint types"}, "dependencies": {"@emotion/memoize": "^0.8.1", "@emotion/sheet": "^1.2.2", "@emotion/utils": "^1.2.1", "@emotion/weak-memoize": "^0.3.1", "stylis": "4.2.0"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "@emotion/hash": "*", "typescript": "^4.5.5"}, "files": ["src", "dist", "types/*.d.ts"], "preconstruct": {"exports": {"envConditions": ["browser", "worker"]}}}