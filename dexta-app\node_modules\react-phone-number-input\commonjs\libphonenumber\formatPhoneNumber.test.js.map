{"version": 3, "file": "formatPhoneNumber.test.js", "names": ["call", "func", "_arguments", "args", "Array", "prototype", "slice", "push", "metadata", "apply", "formatPhoneNumber", "_formatPhoneNumber", "arguments", "formatPhoneNumberIntl", "_formatPhoneNumberIntl", "describe", "it", "expect", "to", "should", "equal", "undefined"], "sources": ["../../source/libphonenumber/formatPhoneNumber.test.js"], "sourcesContent": ["import _formatPhoneNumber, { formatPhoneNumberIntl as _formatPhoneNumberIntl } from './formatPhoneNumber.js'\r\nimport metadata from 'libphonenumber-js/min/metadata'\r\n\r\nfunction call(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}\r\n\r\nfunction formatPhoneNumber() {\r\n\treturn call(_formatPhoneNumber, arguments)\r\n}\r\n\r\nfunction formatPhoneNumberIntl() {\r\n\treturn call(_formatPhoneNumberIntl, arguments)\r\n}\r\n\r\ndescribe('formatPhoneNumber', () => {\r\n\tit('should format phone numbers', () => {\r\n\t\texpect(() => formatPhoneNumber()).to.throw('must be a string')\r\n\t\t// formatPhoneNumber().should.equal('')\r\n\t\tformatPhoneNumber(null).should.equal('')\r\n\t\tformatPhoneNumber('').should.equal('')\r\n\t\texpect(() => _formatPhoneNumber('+1', 'NATIONAL')).to.throw('`metadata` argument not passed')\r\n\t\texpect(() => _formatPhoneNumber('+12133734253', undefined, metadata)).to.throw('Unknown \"format\"')\r\n\t\texpect(() => _formatPhoneNumber('+12133734253', '123', metadata)).to.throw('Unknown \"format\"')\r\n\t\tformatPhoneNumber('+1', 'NATIONAL').should.equal('')\r\n\t\tformatPhoneNumber('+12133734253', 'NATIONAL').should.equal('(*************')\r\n\t\tformatPhoneNumber('+12133734253').should.equal('(*************')\r\n\t\tformatPhoneNumber('+12133734253', 'INTERNATIONAL').should.equal('****** 373 4253')\r\n\t\t// Deprecated.\r\n\t\t// Legacy `format`s.\r\n\t\tformatPhoneNumber('+12133734253', 'National').should.equal('(*************')\r\n\t\tformatPhoneNumber('+12133734253', 'International').should.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should format international phone numbers', () => {\r\n\t\tformatPhoneNumberIntl('+12133734253').should.equal('****** 373 4253')\r\n\t})\r\n})"], "mappings": ";;;;AAAA;;AACA;;;;;;;;AAEA,SAASA,IAAT,CAAcC,IAAd,EAAoBC,UAApB,EAAgC;EAC/B,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBN,IAAtB,CAA2BE,UAA3B,CAAX;EACAC,IAAI,CAACI,IAAL,CAAUC,oBAAV;EACA,OAAOP,IAAI,CAACQ,KAAL,CAAW,IAAX,EAAiBN,IAAjB,CAAP;AACA;;AAED,SAASO,iBAAT,GAA6B;EAC5B,OAAOV,IAAI,CAACW,8BAAD,EAAqBC,SAArB,CAAX;AACA;;AAED,SAASC,qBAAT,GAAiC;EAChC,OAAOb,IAAI,CAACc,yCAAD,EAAyBF,SAAzB,CAAX;AACA;;AAEDG,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,6BAAD,EAAgC,YAAM;IACvCC,MAAM,CAAC;MAAA,OAAMP,iBAAiB,EAAvB;IAAA,CAAD,CAAN,CAAkCQ,EAAlC,UAA2C,kBAA3C,EADuC,CAEvC;;IACAR,iBAAiB,CAAC,IAAD,CAAjB,CAAwBS,MAAxB,CAA+BC,KAA/B,CAAqC,EAArC;IACAV,iBAAiB,CAAC,EAAD,CAAjB,CAAsBS,MAAtB,CAA6BC,KAA7B,CAAmC,EAAnC;IACAH,MAAM,CAAC;MAAA,OAAM,IAAAN,8BAAA,EAAmB,IAAnB,EAAyB,UAAzB,CAAN;IAAA,CAAD,CAAN,CAAmDO,EAAnD,UAA4D,gCAA5D;IACAD,MAAM,CAAC;MAAA,OAAM,IAAAN,8BAAA,EAAmB,cAAnB,EAAmCU,SAAnC,EAA8Cb,oBAA9C,CAAN;IAAA,CAAD,CAAN,CAAsEU,EAAtE,UAA+E,kBAA/E;IACAD,MAAM,CAAC;MAAA,OAAM,IAAAN,8BAAA,EAAmB,cAAnB,EAAmC,KAAnC,EAA0CH,oBAA1C,CAAN;IAAA,CAAD,CAAN,CAAkEU,EAAlE,UAA2E,kBAA3E;IACAR,iBAAiB,CAAC,IAAD,EAAO,UAAP,CAAjB,CAAoCS,MAApC,CAA2CC,KAA3C,CAAiD,EAAjD;IACAV,iBAAiB,CAAC,cAAD,EAAiB,UAAjB,CAAjB,CAA8CS,MAA9C,CAAqDC,KAArD,CAA2D,gBAA3D;IACAV,iBAAiB,CAAC,cAAD,CAAjB,CAAkCS,MAAlC,CAAyCC,KAAzC,CAA+C,gBAA/C;IACAV,iBAAiB,CAAC,cAAD,EAAiB,eAAjB,CAAjB,CAAmDS,MAAnD,CAA0DC,KAA1D,CAAgE,iBAAhE,EAXuC,CAYvC;IACA;;IACAV,iBAAiB,CAAC,cAAD,EAAiB,UAAjB,CAAjB,CAA8CS,MAA9C,CAAqDC,KAArD,CAA2D,gBAA3D;IACAV,iBAAiB,CAAC,cAAD,EAAiB,eAAjB,CAAjB,CAAmDS,MAAnD,CAA0DC,KAA1D,CAAgE,iBAAhE;EACA,CAhBC,CAAF;EAkBAJ,EAAE,CAAC,2CAAD,EAA8C,YAAM;IACrDH,qBAAqB,CAAC,cAAD,CAArB,CAAsCM,MAAtC,CAA6CC,KAA7C,CAAmD,iBAAnD;EACA,CAFC,CAAF;AAGA,CAtBO,CAAR"}