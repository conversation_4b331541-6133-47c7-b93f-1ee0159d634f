{"version": 3, "file": "countries.test.js", "names": ["describe", "it", "sortCountryOptions", "value", "label", "should", "deep", "equal", "divider", "getSupportedCountryOptions", "metadata", "expect", "to", "be", "undefined", "isCountrySupportedWithError", "getSupportedCountries"], "sources": ["../../source/helpers/countries.test.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport {\r\n\tsortCountryOptions,\r\n\tgetSupportedCountryOptions,\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries\r\n} from './countries.js'\r\n\r\ndescribe('helpers/countries', () => {\r\n\tit('should sort country options (no `order`)', () => {\r\n\t\tsortCountryOptions([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t}\r\n\t\t]).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with a divider)', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '|', 'RU']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdivider: true\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with \"...\")', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '|', '...']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdivider: true\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with \"…\")', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '|', '…']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tdivider: true\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should sort country options (with \"🌐\")', () => {\r\n\t\tsortCountryOptions(\r\n\t\t\t[\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'RU',\r\n\t\t\t\t\tlabel: 'Russia'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: 'International'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 'US',\r\n\t\t\t\t\tlabel: 'United States'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\t['US', '🌐', '…']\r\n\t\t).should.deep.equal([\r\n\t\t\t{\r\n\t\t\t\tvalue: 'US',\r\n\t\t\t\tlabel: 'United States'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tlabel: 'International'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'RU',\r\n\t\t\t\tlabel: 'Russia'\r\n\t\t\t}\r\n\t\t])\r\n\t})\r\n\r\n\tit('should get supported country options', () => {\r\n\t\tgetSupportedCountryOptions([\r\n\t\t\t'🌐',\r\n\t\t\t'RU',\r\n\t\t\t'XX',\r\n\t\t\t'@',\r\n\t\t\t'|',\r\n\t\t\t'…',\r\n\t\t\t'...',\r\n\t\t\t'.'\r\n\t\t], metadata).should.deep.equal([\r\n\t\t\t'🌐',\r\n\t\t\t'RU',\r\n\t\t\t'|',\r\n\t\t\t'…',\r\n\t\t\t'...'\r\n\t\t])\r\n\t})\r\n\r\n\tit('should get supported country options (none supported)', () => {\r\n\t\texpect(getSupportedCountryOptions([\r\n\t\t\t'XX',\r\n\t\t\t'@',\r\n\t\t\t'.'\r\n\t\t], metadata)).to.be.undefined\r\n\t})\r\n\r\n\tit('should get supported country options (none supplied)', () => {\r\n\t\texpect(getSupportedCountryOptions(undefined, metadata)).to.be.undefined\r\n\t})\r\n\r\n\tit('should tell is country is supported with error', () => {\r\n\t\tisCountrySupportedWithError('RU', metadata).should.equal(true)\r\n\t\tisCountrySupportedWithError('XX', metadata).should.equal(false)\r\n\t})\r\n\r\n\tit('should get supported countries', () => {\r\n\t\tgetSupportedCountries(['RU', 'XX'], metadata).should.deep.equal(['RU'])\r\n\t})\r\n\r\n\tit('should get supported countries (none supported)', () => {\r\n\t\texpect(getSupportedCountries(['XX'], metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;AAOAA,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,0CAAD,EAA6C,YAAM;IACpD,IAAAC,6BAAA,EAAmB,CAClB;MACCC,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADkB,EAKlB;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALkB,CAAnB,EASGC,MATH,CASUC,IATV,CASeC,KATf,CASqB,CACpB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADoB,EAKpB;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALoB,CATrB;EAmBA,CApBC,CAAF;EAsBAH,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxD,IAAAC,6BAAA,EACC,CACC;MACCC,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALD,CADD,EAWC,CAAC,IAAD,EAAO,GAAP,EAAY,IAAZ,CAXD,EAYEC,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCI,OAAO,EAAE;IADV,CALmB,EAQnB;MACCL,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAZpB;EAyBA,CA1BC,CAAF;EA4BAH,EAAE,CAAC,0CAAD,EAA6C,YAAM;IACpD,IAAAC,6BAAA,EACC,CACC;MACCC,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALD,CADD,EAWC,CAAC,IAAD,EAAO,GAAP,EAAY,KAAZ,CAXD,EAYEC,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCI,OAAO,EAAE;IADV,CALmB,EAQnB;MACCL,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAZpB;EAyBA,CA1BC,CAAF;EA4BAH,EAAE,CAAC,wCAAD,EAA2C,YAAM;IAClD,IAAAC,6BAAA,EACC,CACC;MACCC,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CALD,CADD,EAWC,CAAC,IAAD,EAAO,GAAP,EAAY,GAAZ,CAXD,EAYEC,MAZF,CAYSC,IAZT,CAYcC,KAZd,CAYoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCI,OAAO,EAAE;IADV,CALmB,EAQnB;MACCL,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAZpB;EAyBA,CA1BC,CAAF;EA4BAH,EAAE,CAAC,yCAAD,EAA4C,YAAM;IACnD,IAAAC,6BAAA,EACC,CACC;MACCC,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADD,EAKC;MACCA,KAAK,EAAE;IADR,CALD,EAQC;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARD,CADD,EAcC,CAAC,IAAD,EAAO,IAAP,EAAa,GAAb,CAdD,EAeEC,MAfF,CAeSC,IAfT,CAecC,KAfd,CAeoB,CACnB;MACCJ,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CADmB,EAKnB;MACCA,KAAK,EAAE;IADR,CALmB,EAQnB;MACCD,KAAK,EAAE,IADR;MAECC,KAAK,EAAE;IAFR,CARmB,CAfpB;EA4BA,CA7BC,CAAF;EA+BAH,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAAQ,qCAAA,EAA2B,CAC1B,IAD0B,EAE1B,IAF0B,EAG1B,IAH0B,EAI1B,GAJ0B,EAK1B,GAL0B,EAM1B,GAN0B,EAO1B,KAP0B,EAQ1B,GAR0B,CAA3B,EASGC,oBATH,EASaL,MATb,CASoBC,IATpB,CASyBC,KATzB,CAS+B,CAC9B,IAD8B,EAE9B,IAF8B,EAG9B,GAH8B,EAI9B,GAJ8B,EAK9B,KAL8B,CAT/B;EAgBA,CAjBC,CAAF;EAmBAN,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjEU,MAAM,CAAC,IAAAF,qCAAA,EAA2B,CACjC,IADiC,EAEjC,GAFiC,EAGjC,GAHiC,CAA3B,EAIJC,oBAJI,CAAD,CAAN,CAIcE,EAJd,CAIiBC,EAJjB,CAIoBC,SAJpB;EAKA,CANC,CAAF;EAQAb,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChEU,MAAM,CAAC,IAAAF,qCAAA,EAA2BK,SAA3B,EAAsCJ,oBAAtC,CAAD,CAAN,CAAwDE,EAAxD,CAA2DC,EAA3D,CAA8DC,SAA9D;EACA,CAFC,CAAF;EAIAb,EAAE,CAAC,gDAAD,EAAmD,YAAM;IAC1D,IAAAc,sCAAA,EAA4B,IAA5B,EAAkCL,oBAAlC,EAA4CL,MAA5C,CAAmDE,KAAnD,CAAyD,IAAzD;IACA,IAAAQ,sCAAA,EAA4B,IAA5B,EAAkCL,oBAAlC,EAA4CL,MAA5C,CAAmDE,KAAnD,CAAyD,KAAzD;EACA,CAHC,CAAF;EAKAN,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1C,IAAAe,gCAAA,EAAsB,CAAC,IAAD,EAAO,IAAP,CAAtB,EAAoCN,oBAApC,EAA8CL,MAA9C,CAAqDC,IAArD,CAA0DC,KAA1D,CAAgE,CAAC,IAAD,CAAhE;EACA,CAFC,CAAF;EAIAN,EAAE,CAAC,iDAAD,EAAoD,YAAM;IAC3DU,MAAM,CAAC,IAAAK,gCAAA,EAAsB,CAAC,IAAD,CAAtB,EAA8BN,oBAA9B,CAAD,CAAN,CAAgDE,EAAhD,CAAmDC,EAAnD,CAAsDC,SAAtD;EACA,CAFC,CAAF;AAGA,CArLO,CAAR"}