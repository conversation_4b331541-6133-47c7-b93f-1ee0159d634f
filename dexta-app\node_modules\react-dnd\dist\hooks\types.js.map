{"version": 3, "sources": ["../../src/hooks/types.ts"], "sourcesContent": ["import type { SourceType, TargetType } from 'dnd-core'\n\nimport type {\n\tDragPreviewOptions,\n\tDragSourceMonitor,\n\tDragSourceOptions,\n\tDropTargetMonitor,\n\tDropTargetOptions,\n} from '../types/index.js'\n\nexport type FactoryOrInstance<T> = T | (() => T)\nexport type DragObjectFactory<T> = (monitor: DragSourceMonitor<T>) => T | null\nexport interface DragSourceHookSpec<DragObject, DropResult, CollectedProps> {\n\t/**\n\t * The type of item being dragged. This is required when using the function form of spec.item.\n\t * If spec.item is a static object, the type may either be defined on that object as `item.type`, or it may\n\t * be defined here.\n\t */\n\ttype: SourceType\n\n\t/**\n\t * This property generates or defines a plain javascript item describing\n\t * the data being dragged. This is the only information available to the\n\t * drop targets about the drag source so it's important to pick the minimal\n\t * data they need to know.\n\t *\n\t * You may be tempted to put a reference to the component or complex object here,\n\t * but you should try very hard to avoid doing this because it couples the\n\t * drag sources and drop targets. It's a good idea to use something like\n\t * { id: props.id }\n\t *\n\t * If a function-form is used, it is invoked when the drag begins and returns a draggable item.\n\t * If the function returns null, the drag is canceled\n\t *\n\t */\n\titem?: DragObject | DragObjectFactory<DragObject>\n\n\t/**\n\t * The drag source options\n\t */\n\toptions?: DragSourceOptions\n\n\t/**\n\t * DragPreview options\n\t */\n\tpreviewOptions?: DragPreviewOptions\n\n\t/**\n\t * Optional.\n\t * When the dragging stops, endDrag is called. For every beginDrag call, a corresponding endDrag call is guaranteed.\n\t * You may call monitor.didDrop() to check whether or not the drop was handled by a compatible drop target. If it was handled,\n\t * and the drop target specified a drop result by returning a plain object from its drop() method, it will be available as\n\t * monitor.getDropResult(). This method is a good place to fire a Flux action. Note: If the component is unmounted while dragging,\n\t * component parameter is set to be null.\n\t */\n\tend?: (\n\t\tdraggedItem: DragObject,\n\t\tmonitor: DragSourceMonitor<DragObject, DropResult>,\n\t) => void\n\n\t/**\n\t * Optional.\n\t * Use it to specify whether the dragging is currently allowed. If you want to always allow it, just omit this method.\n\t * Specifying it is handy if you'd like to disable dragging based on some predicate over props. Note: You may not call\n\t * monitor.canDrag() inside this method.\n\t */\n\tcanDrag?:\n\t\t| boolean\n\t\t| ((monitor: DragSourceMonitor<DragObject, DropResult>) => boolean)\n\n\t/**\n\t * Optional.\n\t * By default, only the drag source that initiated the drag operation is considered to be dragging. You can\n\t * override this behavior by defining a custom isDragging method. It might return something like props.id === monitor.getItem().id.\n\t * Do this if the original component may be unmounted during the dragging and later “resurrected” with a different parent.\n\t * For example, when moving a card across the lists in a Kanban board, you want it to retain the dragged appearance—even though\n\t * technically, the component gets unmounted and a different one gets mounted every time you move it to another list.\n\t *\n\t * Note: You may not call monitor.isDragging() inside this method.\n\t */\n\tisDragging?: (monitor: DragSourceMonitor<DragObject, DropResult>) => boolean\n\n\t/**\n\t * A function to collect rendering properties\n\t */\n\tcollect?: (\n\t\tmonitor: DragSourceMonitor<DragObject, DropResult>,\n\t) => CollectedProps\n}\n\n/**\n * Interface for the DropTarget specification object\n */\nexport interface DropTargetHookSpec<DragObject, DropResult, CollectedProps> {\n\t/**\n\t * The kinds of dragItems this dropTarget accepts\n\t */\n\taccept: TargetType\n\n\t/**\n\t * The drop target options\n\t */\n\toptions?: DropTargetOptions\n\n\t/**\n\t * Optional.\n\t * Called when a compatible item is dropped on the target. You may either return undefined, or a plain object.\n\t * If you return an object, it is going to become the drop result and will be available to the drag source in its\n\t * endDrag method as monitor.getDropResult(). This is useful in case you want to perform different actions\n\t * depending on which target received the drop. If you have nested drop targets, you can test whether a nested\n\t * target has already handled drop by checking monitor.didDrop() and monitor.getDropResult(). Both this method and\n\t * the source's endDrag method are good places to fire Flux actions. This method will not be called if canDrop()\n\t * is defined and returns false.\n\t */\n\tdrop?: (\n\t\titem: DragObject,\n\t\tmonitor: DropTargetMonitor<DragObject, DropResult>,\n\t) => DropResult | undefined\n\n\t/**\n\t * Optional.\n\t * Called when an item is hovered over the component. You can check monitor.isOver({ shallow: true }) to test whether\n\t * the hover happens over just the current target, or over a nested one. Unlike drop(), this method will be called even\n\t * if canDrop() is defined and returns false. You can check monitor.canDrop() to test whether this is the case.\n\t */\n\thover?: (\n\t\titem: DragObject,\n\t\tmonitor: DropTargetMonitor<DragObject, DropResult>,\n\t) => void\n\n\t/**\n\t * Optional. Use it to specify whether the drop target is able to accept the item. If you want to always allow it, just\n\t * omit this method. Specifying it is handy if you'd like to disable dropping based on some predicate over props or\n\t * monitor.getItem(). Note: You may not call monitor.canDrop() inside this method.\n\t */\n\tcanDrop?: (\n\t\titem: DragObject,\n\t\tmonitor: DropTargetMonitor<DragObject, DropResult>,\n\t) => boolean\n\n\t/**\n\t * A function to collect rendering properties\n\t */\n\tcollect?: (\n\t\tmonitor: DropTargetMonitor<DragObject, DropResult>,\n\t) => CollectedProps\n}\n"], "names": [], "mappings": "AAAA,WAkJC"}