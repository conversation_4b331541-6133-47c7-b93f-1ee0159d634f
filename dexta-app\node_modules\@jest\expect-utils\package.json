{"name": "@jest/expect-utils", "version": "29.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/expect-utils"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"jest-get-type": "^29.4.3"}, "devDependencies": {"@tsd/typescript": "^5.0.4", "immutable": "^4.0.0", "jest-matcher-utils": "^29.6.2", "tsd-lite": "^0.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6"}