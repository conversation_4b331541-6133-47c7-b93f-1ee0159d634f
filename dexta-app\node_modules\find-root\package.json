{"name": "find-root", "author": "jsdnxx", "version": "1.1.0", "description": "find the closest package.json", "keywords": ["fs", "get", "find", "closest", "package", "module", "base", "root"], "main": "index.js", "scripts": {"pretest": "standard", "test": "mocha"}, "repository": "**************:js-n/find-root.git", "license": "MIT", "readmeFilename": "README.md", "devDependencies": {"chai": "^4.0.2", "mocha": "^3.4.2", "moquire": "^1.5.5", "standard": "^10.0.2"}}