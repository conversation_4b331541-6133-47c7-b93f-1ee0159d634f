{"name": "flat-cache", "version": "3.0.4", "description": "A stupidly simple key/value storage using files to persist some data", "repository": "royriojas/flat-cache", "license": "MIT", "author": {"name": "<PERSON>", "url": "http://royriojas.com"}, "main": "src/cache.js", "files": ["src/cache.js", "src/del.js", "src/utils.js"], "engines": {"node": "^10.12.0 || >=12.0.0"}, "precommit": ["npm run verify --silent"], "prepush": ["npm run verify --silent"], "scripts": {"eslint": "eslint --cache --cache-location=node_modules/.cache/ ./src/**/*.js ./test/**/*.js", "eslint-fix": "npm run eslint -- --fix", "autofix": "npm run eslint-fix", "check": "npm run eslint", "verify": "npm run eslint && npm run test:cache", "install-hooks": "prepush install && changelogx install-hook && precommit install", "changelog": "changelogx -f markdown -o ./changelog.md", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "pre-v": "npm run verify", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "test:cache": "mocha -R spec test/specs", "test": "npm run verify --silent", "cover": "istanbul cover test/runner.js html text-summary", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary"}, "keywords": ["json cache", "simple cache", "file cache", "key par", "key value", "cache"], "changelogx": {"ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)", "commitURL": "https://github.com/royriojas/flat-cache/commit/{0}", "authorURL": "https://github.com/{0}", "issueIDURL": "https://github.com/royriojas/flat-cache/issues/{0}", "projectName": "flat-cache"}, "devDependencies": {"chai": "^4.2.0", "changelogx": "^5.0.6", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-mocha": "^8.0.0", "eslint-plugin-prettier": "^3.1.4", "glob-expand": "^0.2.1", "istanbul": "^0.4.5", "mocha": "^8.2.1", "precommit": "^1.2.2", "prepush": "^3.1.11", "prettier": "^2.1.2", "watch-run": "^1.2.5"}, "dependencies": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}}