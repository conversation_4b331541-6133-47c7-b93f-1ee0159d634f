{"name": "@emotion/utils", "version": "1.2.1", "description": "internal utils for emotion", "main": "dist/emotion-utils.cjs.js", "module": "dist/emotion-utils.esm.js", "browser": {"./dist/emotion-utils.esm.js": "./dist/emotion-utils.browser.esm.js"}, "exports": {".": {"module": {"worker": "./dist/emotion-utils.worker.esm.js", "browser": "./dist/emotion-utils.browser.esm.js", "default": "./dist/emotion-utils.esm.js"}, "import": "./dist/emotion-utils.cjs.mjs", "default": "./dist/emotion-utils.cjs.js"}, "./package.json": "./package.json"}, "types": "types/index.d.ts", "license": "MIT", "scripts": {"test:typescript": "dtslint types"}, "repository": "https://github.com/emotion-js/emotion/tree/main/packages/utils", "publishConfig": {"access": "public"}, "files": ["src", "dist", "types/*.d.ts"], "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^4.5.5"}, "preconstruct": {"exports": {"envConditions": ["browser", "worker"]}}}