{"name": "@mui/material", "version": "5.10.5", "private": false, "author": "MUI Team", "description": "React components that implement Google's Material Design.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "material-ui", "material design"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-material"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/material-ui/getting-started/overview/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "dependencies": {"@babel/runtime": "^7.18.9", "@mui/base": "5.0.0-alpha.97", "@mui/core-downloads-tracker": "^5.10.5", "@mui/system": "^5.10.5", "@mui/types": "^7.2.0", "@mui/utils": "^5.10.3", "@types/react-transition-group": "^4.4.5", "clsx": "^1.2.1", "csstype": "^3.1.0", "prop-types": "^15.8.1", "react-is": "^18.2.0", "react-transition-group": "^4.4.5"}, "sideEffects": false, "publishConfig": {"access": "public"}, "engines": {"node": ">=12.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}