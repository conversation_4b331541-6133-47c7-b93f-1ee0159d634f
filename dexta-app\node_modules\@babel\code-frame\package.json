{"name": "@babel/code-frame", "version": "7.22.10", "description": "Generate errors that contain a code frame that point to source locations.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-code-frame"}, "main": "./lib/index.js", "dependencies": {"@babel/highlight": "^7.22.10", "chalk": "^2.4.2"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}