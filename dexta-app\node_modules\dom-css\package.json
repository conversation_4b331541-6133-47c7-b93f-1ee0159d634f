{"name": "dom-css", "version": "2.1.0", "description": "fast dom CSS styling", "main": "index.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {"add-px-to-style": "1.0.0", "prefix-style": "2.0.1", "to-camel-case": "1.0.0"}, "devDependencies": {"browserify": "^8.1.1", "smokestack": "^3.2.0", "standard": "^5.4.1", "tap-closer": "^1.0.0", "tap-spec": "^2.1.2", "tape": "^3.2.0"}, "scripts": {"test": "standard && browserify test.js | tap-closer | smokestack | tap-spec"}, "keywords": ["dom", "css", "style", "sheet", "animate", "element", "px", "pixels"], "repository": {"type": "git", "url": "git://github.com/mattdesl/dom-css.git"}, "homepage": "https://github.com/mattdesl/dom-css", "bugs": {"url": "https://github.com/mattdesl/dom-css/issues"}}