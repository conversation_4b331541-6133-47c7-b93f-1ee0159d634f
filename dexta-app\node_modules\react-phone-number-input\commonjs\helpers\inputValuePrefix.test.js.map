{"version": 3, "file": "inputValuePrefix.test.js", "names": ["describe", "it", "getInputValuePrefix", "country", "metadata", "should", "equal", "international", "withCountryCallingCode", "removeInputValuePrefix"], "sources": ["../../source/helpers/inputValuePrefix.test.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport { getInputValuePrefix, removeInputValuePrefix } from './inputValuePrefix.js'\r\n\r\ndescribe('inputValuePrefix', () => {\r\n\tit('should get input value prefix', () => {\r\n\t\tgetInputValuePrefix({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tmetadata\r\n\t\t}).should.equal('')\r\n\r\n\t\tgetInputValuePrefix({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tinternational: true,\r\n\t\t\twithCountryCallingCode: true,\r\n\t\t\tmetadata\r\n\t\t}).should.equal('')\r\n\r\n\t\tgetInputValuePrefix({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tinternational: true,\r\n\t\t\tmetadata\r\n\t\t}).should.equal('+7')\r\n\t})\r\n\r\n\tit('should remove input value prefix', () => {\r\n\t\tremoveInputValuePrefix('+78005553535', '+7').should.equal('8005553535')\r\n\t\tremoveInputValuePrefix('****** 555 35 35', '+7').should.equal('800 555 35 35')\r\n\t\tremoveInputValuePrefix('8 (800) 555-35-35', '').should.equal('8 (800) 555-35-35')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;AAEAA,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzC,IAAAC,qCAAA,EAAoB;MACnBC,OAAO,EAAE,IADU;MAEnBC,QAAQ,EAARA;IAFmB,CAApB,EAGGC,MAHH,CAGUC,KAHV,CAGgB,EAHhB;IAKA,IAAAJ,qCAAA,EAAoB;MACnBC,OAAO,EAAE,IADU;MAEnBI,aAAa,EAAE,IAFI;MAGnBC,sBAAsB,EAAE,IAHL;MAInBJ,QAAQ,EAARA;IAJmB,CAApB,EAKGC,MALH,CAKUC,KALV,CAKgB,EALhB;IAOA,IAAAJ,qCAAA,EAAoB;MACnBC,OAAO,EAAE,IADU;MAEnBI,aAAa,EAAE,IAFI;MAGnBH,QAAQ,EAARA;IAHmB,CAApB,EAIGC,MAJH,CAIUC,KAJV,CAIgB,IAJhB;EAKA,CAlBC,CAAF;EAoBAL,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C,IAAAQ,wCAAA,EAAuB,cAAvB,EAAuC,IAAvC,EAA6CJ,MAA7C,CAAoDC,KAApD,CAA0D,YAA1D;IACA,IAAAG,wCAAA,EAAuB,kBAAvB,EAA2C,IAA3C,EAAiDJ,MAAjD,CAAwDC,KAAxD,CAA8D,eAA9D;IACA,IAAAG,wCAAA,EAAuB,mBAAvB,EAA4C,EAA5C,EAAgDJ,MAAhD,CAAuDC,KAAvD,CAA6D,mBAA7D;EACA,CAJC,CAAF;AAKA,CA1BO,CAAR"}