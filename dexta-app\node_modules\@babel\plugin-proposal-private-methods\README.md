# @babel/plugin-proposal-private-methods

> This plugin transforms private class methods

See our website [@babel/plugin-proposal-private-methods](https://babeljs.io/docs/en/babel-plugin-proposal-private-methods) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-private-methods
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-private-methods --dev
```
