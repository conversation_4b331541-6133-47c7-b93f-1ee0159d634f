{"name": "filter-obj", "version": "5.1.0", "description": "Filter object keys and values into a new object", "license": "MIT", "repository": "sindresorhus/filter-obj", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["filter", "object", "key", "keys", "value", "values", "iterate", "iterator", "include", "exclude", "pick", "omit"], "devDependencies": {"ava": "^4.3.1", "tsd": "^0.22.0", "xo": "^0.51.0"}}