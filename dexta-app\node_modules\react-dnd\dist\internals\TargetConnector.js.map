{"version": 3, "sources": ["../../src/internals/TargetConnector.ts"], "sourcesContent": ["import { shallowEqual } from '@react-dnd/shallowequal'\nimport type { Backend, Identifier, Unsubscribe } from 'dnd-core'\nimport type { RefObject } from 'react'\n\nimport type { DropTargetOptions } from '../types/index.js'\nimport { isRef } from './isRef.js'\nimport type { Connector } from './SourceConnector.js'\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js'\n\nexport class TargetConnector implements Connector {\n\tpublic hooks = wrapConnectorHooks({\n\t\tdropTarget: (node: any, options: DropTargetOptions) => {\n\t\t\tthis.clearDropTarget()\n\t\t\tthis.dropTargetOptions = options\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dropTargetRef = node\n\t\t\t} else {\n\t\t\t\tthis.dropTargetNode = node\n\t\t\t}\n\t\t\tthis.reconnect()\n\t\t},\n\t})\n\n\tprivate handlerId: Identifier | null = null\n\t// The drop target may either be attached via ref or connect function\n\tprivate dropTargetRef: RefObject<any> | null = null\n\tprivate dropTargetNode: any\n\tprivate dropTargetOptionsInternal: DropTargetOptions | null = null\n\tprivate unsubscribeDropTarget: Unsubscribe | undefined\n\n\tprivate lastConnectedHandlerId: Identifier | null = null\n\tprivate lastConnectedDropTarget: any = null\n\tprivate lastConnectedDropTargetOptions: DropTargetOptions | null = null\n\tprivate readonly backend: Backend\n\n\tpublic constructor(backend: Backend) {\n\t\tthis.backend = backend\n\t}\n\n\tpublic get connectTarget(): any {\n\t\treturn this.dropTarget\n\t}\n\n\tpublic reconnect(): void {\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didDropTargetChange() ||\n\t\t\tthis.didOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDropTarget()\n\t\t}\n\n\t\tconst dropTarget = this.dropTarget\n\t\tif (!this.handlerId) {\n\t\t\treturn\n\t\t}\n\t\tif (!dropTarget) {\n\t\t\tthis.lastConnectedDropTarget = dropTarget\n\t\t\treturn\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDropTarget = dropTarget\n\t\t\tthis.lastConnectedDropTargetOptions = this.dropTargetOptions\n\n\t\t\tthis.unsubscribeDropTarget = this.backend.connectDropTarget(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdropTarget,\n\t\t\t\tthis.dropTargetOptions,\n\t\t\t)\n\t\t}\n\t}\n\n\tpublic receiveHandlerId(newHandlerId: Identifier | null): void {\n\t\tif (newHandlerId === this.handlerId) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.handlerId = newHandlerId\n\t\tthis.reconnect()\n\t}\n\n\tpublic get dropTargetOptions(): DropTargetOptions {\n\t\treturn this.dropTargetOptionsInternal\n\t}\n\tpublic set dropTargetOptions(options: DropTargetOptions) {\n\t\tthis.dropTargetOptionsInternal = options\n\t}\n\n\tprivate didHandlerIdChange(): boolean {\n\t\treturn this.lastConnectedHandlerId !== this.handlerId\n\t}\n\n\tprivate didDropTargetChange(): boolean {\n\t\treturn this.lastConnectedDropTarget !== this.dropTarget\n\t}\n\n\tprivate didOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDropTargetOptions,\n\t\t\tthis.dropTargetOptions,\n\t\t)\n\t}\n\n\tpublic disconnectDropTarget() {\n\t\tif (this.unsubscribeDropTarget) {\n\t\t\tthis.unsubscribeDropTarget()\n\t\t\tthis.unsubscribeDropTarget = undefined\n\t\t}\n\t}\n\n\tprivate get dropTarget() {\n\t\treturn (\n\t\t\tthis.dropTargetNode || (this.dropTargetRef && this.dropTargetRef.current)\n\t\t)\n\t}\n\n\tprivate clearDropTarget() {\n\t\tthis.dropTargetRef = null\n\t\tthis.dropTargetNode = null\n\t}\n}\n"], "names": ["shallowEqual", "isRef", "wrapConnectorHooks", "TargetConnector", "connectTarget", "drop<PERSON>ar<PERSON>", "reconnect", "<PERSON><PERSON><PERSON><PERSON>", "didHandlerIdChange", "didDropTargetChange", "didOptionsChange", "disconnectDropTarget", "handlerId", "lastConnectedDropTarget", "lastConnectedHandlerId", "lastConnectedDropTargetOptions", "dropTargetOptions", "unsubscribeDropTarget", "backend", "connectDropTarget", "receiveHandlerId", "newHandlerId", "dropTargetOptionsInternal", "options", "undefined", "dropTargetNode", "dropTargetRef", "current", "clearDropTarget", "hooks", "node"], "mappings": "AAAA,SAASA,YAAY,QAAQ,yBAAyB,CAAA;AAKtD,SAASC,KAAK,QAAQ,YAAY,CAAA;AAElC,SAASC,kBAAkB,QAAQ,yBAAyB,CAAA;AAE5D,OAAO,MAAMC,eAAe;IA8B3B,IAAWC,aAAa,GAAQ;QAC/B,OAAO,IAAI,CAACC,UAAU,CAAA;KACtB;IAED,AAAOC,SAAS,GAAS;QACxB,gDAAgD;QAChD,MAAMC,SAAS,GACd,IAAI,CAACC,kBAAkB,EAAE,IACzB,IAAI,CAACC,mBAAmB,EAAE,IAC1B,IAAI,CAACC,gBAAgB,EAAE;QAExB,IAAIH,SAAS,EAAE;YACd,IAAI,CAACI,oBAAoB,EAAE;SAC3B;QAED,MAAMN,UAAU,GAAG,IAAI,CAACA,UAAU;QAClC,IAAI,CAAC,IAAI,CAACO,SAAS,EAAE;YACpB,OAAM;SACN;QACD,IAAI,CAACP,UAAU,EAAE;YAChB,IAAI,CAACQ,uBAAuB,GAAGR,UAAU;YACzC,OAAM;SACN;QAED,IAAIE,SAAS,EAAE;YACd,IAAI,CAACO,sBAAsB,GAAG,IAAI,CAACF,SAAS;YAC5C,IAAI,CAACC,uBAAuB,GAAGR,UAAU;YACzC,IAAI,CAACU,8BAA8B,GAAG,IAAI,CAACC,iBAAiB;YAE5D,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAC1D,IAAI,CAACP,SAAS,EACdP,UAAU,EACV,IAAI,CAACW,iBAAiB,CACtB;SACD;KACD;IAED,AAAOI,gBAAgB,CAACC,YAA+B,EAAQ;QAC9D,IAAIA,YAAY,KAAK,IAAI,CAACT,SAAS,EAAE;YACpC,OAAM;SACN;QAED,IAAI,CAACA,SAAS,GAAGS,YAAY;QAC7B,IAAI,CAACf,SAAS,EAAE;KAChB;IAED,IAAWU,iBAAiB,GAAsB;QACjD,OAAO,IAAI,CAACM,yBAAyB,CAAA;KACrC;IACD,IAAWN,iBAAiB,CAACO,OAA0B,EAAE;QACxD,IAAI,CAACD,yBAAyB,GAAGC,OAAO;KACxC;IAED,AAAQf,kBAAkB,GAAY;QACrC,OAAO,IAAI,CAACM,sBAAsB,KAAK,IAAI,CAACF,SAAS,CAAA;KACrD;IAED,AAAQH,mBAAmB,GAAY;QACtC,OAAO,IAAI,CAACI,uBAAuB,KAAK,IAAI,CAACR,UAAU,CAAA;KACvD;IAED,AAAQK,gBAAgB,GAAY;QACnC,OAAO,CAACV,YAAY,CACnB,IAAI,CAACe,8BAA8B,EACnC,IAAI,CAACC,iBAAiB,CACtB,CAAA;KACD;IAED,AAAOL,oBAAoB,GAAG;QAC7B,IAAI,IAAI,CAACM,qBAAqB,EAAE;YAC/B,IAAI,CAACA,qBAAqB,EAAE;YAC5B,IAAI,CAACA,qBAAqB,GAAGO,SAAS;SACtC;KACD;IAED,IAAYnB,UAAU,GAAG;QACxB,OACC,IAAI,CAACoB,cAAc,IAAK,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,OAAO,AAAC,CACzE;KACD;IAED,AAAQC,eAAe,GAAG;QACzB,IAAI,CAACF,aAAa,GAAG,IAAI;QACzB,IAAI,CAACD,cAAc,GAAG,IAAI;KAC1B;IAxFD,YAAmBP,OAAgB,CAAE;QAzBrC,KAAOW,KAAK,GAAG3B,kBAAkB,CAAC;YACjCG,UAAU,EAAE,CAACyB,IAAS,EAAEP,OAA0B,GAAK;gBACtD,IAAI,CAACK,eAAe,EAAE;gBACtB,IAAI,CAACZ,iBAAiB,GAAGO,OAAO;gBAChC,IAAItB,KAAK,CAAC6B,IAAI,CAAC,EAAE;oBAChB,IAAI,CAACJ,aAAa,GAAGI,IAAI;iBACzB,MAAM;oBACN,IAAI,CAACL,cAAc,GAAGK,IAAI;iBAC1B;gBACD,IAAI,CAACxB,SAAS,EAAE;aAChB;SACD,CAAC,AArBH,CAqBG;QAEF,KAAQM,SAAS,GAAsB,IAAI,AAvB5C,CAuB4C;QAC3C,qEAAqE;QACrE,KAAQc,aAAa,GAA0B,IAAI,AAzBpD,CAyBoD;QAEnD,KAAQJ,yBAAyB,GAA6B,IAAI,AA3BnE,CA2BmE;QAGlE,KAAQR,sBAAsB,GAAsB,IAAI,AA9BzD,CA8ByD;QACxD,KAAQD,uBAAuB,GAAQ,IAAI,AA/B5C,CA+B4C;QAC3C,KAAQE,8BAA8B,GAA6B,IAAI,AAhCxE,CAgCwE;QAItE,IAAI,CAACG,OAAO,GAAGA,OAAO;KACtB;CAuFD"}