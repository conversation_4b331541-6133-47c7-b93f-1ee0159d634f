{"version": 3, "sources": ["../../src/hooks/useCollector.ts"], "sourcesContent": ["import equal from 'fast-deep-equal'\nimport { useCallback, useState } from 'react'\n\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js'\n\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\nexport function useCollector<T, S>(\n\tmonitor: T,\n\tcollect: (monitor: T) => S,\n\tonUpdate?: () => void,\n): [S, () => void] {\n\tconst [collected, setCollected] = useState(() => collect(monitor))\n\n\tconst updateCollected = useCallback(() => {\n\t\tconst nextValue = collect(monitor)\n\t\t// This needs to be a deep-equality check because some monitor-collected values\n\t\t// include XYCoord objects that may be equivalent, but do not have instance equality.\n\t\tif (!equal(collected, nextValue)) {\n\t\t\tsetCollected(nextValue)\n\t\t\tif (onUpdate) {\n\t\t\t\tonUpdate()\n\t\t\t}\n\t\t}\n\t}, [collected, monitor, onUpdate])\n\n\t// update the collected properties after react renders.\n\t// Note that the \"Dustbin Stress Test\" fails if this is not\n\t// done when the component updates\n\tuseIsomorphicLayoutEffect(updateCollected)\n\n\treturn [collected, updateCollected]\n}\n"], "names": ["equal", "useCallback", "useState", "useIsomorphicLayoutEffect", "useCollector", "monitor", "collect", "onUpdate", "collected", "setCollected", "updateCollected", "nextValue"], "mappings": "AAAA,OAAOA,KAAK,MAAM,iBAAiB,CAAA;AACnC,SAASC,WAAW,EAAEC,QAAQ,QAAQ,OAAO,CAAA;AAE7C,SAASC,yBAAyB,QAAQ,gCAAgC,CAAA;AAE1E;;;;;GAKG,CACH,OAAO,SAASC,YAAY,CAC3BC,OAAU,EACVC,OAA0B,EAC1BC,QAAqB,EACH;IAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,IAAMI,OAAO,CAACD,OAAO,CAAC;IAAA,CAAC;IAElE,MAAMK,eAAe,GAAGT,WAAW,CAAC,IAAM;QACzC,MAAMU,SAAS,GAAGL,OAAO,CAACD,OAAO,CAAC;QAClC,+EAA+E;QAC/E,qFAAqF;QACrF,IAAI,CAACL,KAAK,CAACQ,SAAS,EAAEG,SAAS,CAAC,EAAE;YACjCF,YAAY,CAACE,SAAS,CAAC;YACvB,IAAIJ,QAAQ,EAAE;gBACbA,QAAQ,EAAE;aACV;SACD;KACD,EAAE;QAACC,SAAS;QAAEH,OAAO;QAAEE,QAAQ;KAAC,CAAC;IAElC,uDAAuD;IACvD,2DAA2D;IAC3D,kCAAkC;IAClCJ,yBAAyB,CAACO,eAAe,CAAC;IAE1C,OAAO;QAACF,SAAS;QAAEE,eAAe;KAAC,CAAA;CACnC"}