{"name": "@babel/plugin-transform-modules-amd", "version": "7.22.5", "description": "This plugin transforms ES2015 modules to AMD", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-amd"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-transforms": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-external-helpers": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}