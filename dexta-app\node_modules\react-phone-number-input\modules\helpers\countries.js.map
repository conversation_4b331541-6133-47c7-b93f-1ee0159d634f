{"version": 3, "file": "countries.js", "names": ["isSupportedCountry", "getCountries", "sortCountryOptions", "options", "order", "optionsOnTop", "optionsOnBottom", "appendTo", "element", "push", "divider", "countryCode", "undefined", "index", "indexOf", "filter", "option", "value", "splice", "concat", "getSupportedCountryOptions", "countryOptions", "metadata", "isCountrySupportedWithError", "length", "country", "console", "error", "getSupportedCountries", "countries"], "sources": ["../../source/helpers/countries.js"], "sourcesContent": ["import { isSupportedCountry } from 'libphonenumber-js/core'\r\nexport { getCountries } from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Sorts country `<select/>` options.\r\n * Can move some country `<select/>` options\r\n * to the top of the list, for example.\r\n * @param  {object[]} countryOptions — Country `<select/>` options.\r\n * @param  {string[]} [countryOptionsOrder] — Country `<select/>` options order. Example: `[\"US\", \"CA\", \"AU\", \"|\", \"...\"]`.\r\n * @return {object[]}\r\n */\r\nexport function sortCountryOptions(options, order) {\r\n\tif (!order) {\r\n\t\treturn options\r\n\t}\r\n\tconst optionsOnTop = []\r\n\tconst optionsOnBottom = []\r\n\tlet appendTo = optionsOnTop\r\n\tfor (const element of order) {\r\n\t\tif (element === '|') {\r\n\t\t\tappendTo.push({ divider: true })\r\n\t\t} else if (element === '...' || element === '…') {\r\n\t\t\tappendTo = optionsOnBottom\r\n\t\t} else {\r\n\t\t\tlet countryCode\r\n\t\t\tif (element === '🌐') {\r\n\t\t\t\tcountryCode = undefined\r\n\t\t\t} else {\r\n\t\t\t\tcountryCode = element\r\n\t\t\t}\r\n\t\t\t// Find the position of the option.\r\n\t\t\tconst index = options.indexOf(options.filter(option => option.value === countryCode)[0])\r\n\t\t\t// Get the option.\r\n\t\t\tconst option = options[index]\r\n\t\t\t// Remove the option from its default position.\r\n\t\t\toptions.splice(index, 1)\r\n\t\t\t// Add the option on top.\r\n\t\t\tappendTo.push(option)\r\n\t\t}\r\n\t}\r\n\treturn optionsOnTop.concat(options).concat(optionsOnBottom)\r\n}\r\n\r\nexport function getSupportedCountryOptions(countryOptions, metadata) {\r\n\tif (countryOptions) {\r\n\t\tcountryOptions = countryOptions.filter((option) => {\r\n\t\t\tswitch (option) {\r\n\t\t\t\tcase '🌐':\r\n\t\t\t\tcase '|':\r\n\t\t\t\tcase '...':\r\n\t\t\t\tcase '…':\r\n\t\t\t\t\treturn true\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn isCountrySupportedWithError(option, metadata)\r\n\t\t\t}\r\n\t\t})\r\n\t\tif (countryOptions.length > 0) {\r\n\t\t\treturn countryOptions\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isCountrySupportedWithError(country, metadata) {\r\n\tif (isSupportedCountry(country, metadata)) {\r\n\t\treturn true\r\n\t} else {\r\n\t\tconsole.error(`Country not found: ${country}`)\r\n\t\treturn false\r\n\t}\r\n}\r\n\r\nexport function getSupportedCountries(countries, metadata) {\r\n\tif (countries) {\r\n\t\tcountries = countries.filter(country => isCountrySupportedWithError(country, metadata))\r\n\t\tif (countries.length === 0) {\r\n\t\t\tcountries = undefined\r\n\t\t}\r\n\t}\r\n\treturn countries\r\n}"], "mappings": ";;;;;;AAAA,SAASA,kBAAT,QAAmC,wBAAnC;AACA,SAASC,YAAT,QAA6B,wBAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,kBAAT,CAA4BC,OAA5B,EAAqCC,KAArC,EAA4C;EAClD,IAAI,CAACA,KAAL,EAAY;IACX,OAAOD,OAAP;EACA;;EACD,IAAME,YAAY,GAAG,EAArB;EACA,IAAMC,eAAe,GAAG,EAAxB;EACA,IAAIC,QAAQ,GAAGF,YAAf;;EACA,qDAAsBD,KAAtB,wCAA6B;IAAA,IAAlBI,OAAkB;;IAC5B,IAAIA,OAAO,KAAK,GAAhB,EAAqB;MACpBD,QAAQ,CAACE,IAAT,CAAc;QAAEC,OAAO,EAAE;MAAX,CAAd;IACA,CAFD,MAEO,IAAIF,OAAO,KAAK,KAAZ,IAAqBA,OAAO,KAAK,GAArC,EAA0C;MAChDD,QAAQ,GAAGD,eAAX;IACA,CAFM,MAEA;MAAA;QACN,IAAIK,WAAW,SAAf;;QACA,IAAIH,OAAO,KAAK,IAAhB,EAAsB;UACrBG,WAAW,GAAGC,SAAd;QACA,CAFD,MAEO;UACND,WAAW,GAAGH,OAAd;QACA,CANK,CAON;;;QACA,IAAMK,KAAK,GAAGV,OAAO,CAACW,OAAR,CAAgBX,OAAO,CAACY,MAAR,CAAe,UAAAC,MAAM;UAAA,OAAIA,MAAM,CAACC,KAAP,KAAiBN,WAArB;QAAA,CAArB,EAAuD,CAAvD,CAAhB,CAAd,CARM,CASN;;QACA,IAAMK,MAAM,GAAGb,OAAO,CAACU,KAAD,CAAtB,CAVM,CAWN;;QACAV,OAAO,CAACe,MAAR,CAAeL,KAAf,EAAsB,CAAtB,EAZM,CAaN;;QACAN,QAAQ,CAACE,IAAT,CAAcO,MAAd;MAdM;IAeN;EACD;;EACD,OAAOX,YAAY,CAACc,MAAb,CAAoBhB,OAApB,EAA6BgB,MAA7B,CAAoCb,eAApC,CAAP;AACA;AAED,OAAO,SAASc,0BAAT,CAAoCC,cAApC,EAAoDC,QAApD,EAA8D;EACpE,IAAID,cAAJ,EAAoB;IACnBA,cAAc,GAAGA,cAAc,CAACN,MAAf,CAAsB,UAACC,MAAD,EAAY;MAClD,QAAQA,MAAR;QACC,KAAK,IAAL;QACA,KAAK,GAAL;QACA,KAAK,KAAL;QACA,KAAK,GAAL;UACC,OAAO,IAAP;;QACD;UACC,OAAOO,2BAA2B,CAACP,MAAD,EAASM,QAAT,CAAlC;MAPF;IASA,CAVgB,CAAjB;;IAWA,IAAID,cAAc,CAACG,MAAf,GAAwB,CAA5B,EAA+B;MAC9B,OAAOH,cAAP;IACA;EACD;AACD;AAED,OAAO,SAASE,2BAAT,CAAqCE,OAArC,EAA8CH,QAA9C,EAAwD;EAC9D,IAAItB,kBAAkB,CAACyB,OAAD,EAAUH,QAAV,CAAtB,EAA2C;IAC1C,OAAO,IAAP;EACA,CAFD,MAEO;IACNI,OAAO,CAACC,KAAR,8BAAoCF,OAApC;IACA,OAAO,KAAP;EACA;AACD;AAED,OAAO,SAASG,qBAAT,CAA+BC,SAA/B,EAA0CP,QAA1C,EAAoD;EAC1D,IAAIO,SAAJ,EAAe;IACdA,SAAS,GAAGA,SAAS,CAACd,MAAV,CAAiB,UAAAU,OAAO;MAAA,OAAIF,2BAA2B,CAACE,OAAD,EAAUH,QAAV,CAA/B;IAAA,CAAxB,CAAZ;;IACA,IAAIO,SAAS,CAACL,MAAV,KAAqB,CAAzB,EAA4B;MAC3BK,SAAS,GAAGjB,SAAZ;IACA;EACD;;EACD,OAAOiB,SAAP;AACA"}