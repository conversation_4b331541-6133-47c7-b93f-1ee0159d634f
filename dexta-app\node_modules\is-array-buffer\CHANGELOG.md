# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v3.0.2](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.1...v3.0.2) - 2023-03-01

### Commits

- [Fix] `node` 0.8: an object arg to a TA only throws a RangeError when it is an ArrayBuffer of an incompatible byte length [`d5108f6`](https://github.com/fengyuanchen/is-array-buffer/commit/d5108f6d06245e616b6c563995f214a38732243c)
- [Dev <PERSON>] update `object-inspect`, `tape` [`400f456`](https://github.com/fengyuanchen/is-array-buffer/commit/400f4563ccbe27c7fbb485665352c76210bba9cb)
- [Deps] update `get-intrinsic` [`133732e`](https://github.com/fengyuanchen/is-array-buffer/commit/133732ec88f8dded1c705b758badc2240077a6d8)

## [v3.0.1](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.0...v3.0.1) - 2023-01-05

### Commits

- [Fix] in node 0.8, TAs do not coerce Uint8Arrays to an ArrayBuffer properly [`e488763`](https://github.com/fengyuanchen/is-array-buffer/commit/e48876346f446825dad619e55dcc830ed93f2853)
- [Dev Deps] update `@ljharb/eslint-config`, `aud` [`8eebfa2`](https://github.com/fengyuanchen/is-array-buffer/commit/8eebfa21881f3a9fa5094f8c486f00e496658ea9)

## [v3.0.0](https://github.com/fengyuanchen/is-array-buffer/compare/v2.0.0...v3.0.0) - 2023-01-04

### Commits

- [Breaking] replace package implementation [`b65f929`](https://github.com/fengyuanchen/is-array-buffer/commit/b65f929d856d2a42f043be0f5a0fc2e067370ed1)
- Initial implementation, tests, readme [`06afa73`](https://github.com/fengyuanchen/is-array-buffer/commit/06afa73e775960802ea9257cc6b4cdf768c72d3f)
- Initial commit [`051813f`](https://github.com/fengyuanchen/is-array-buffer/commit/051813f15e3cbf515e2447306761dd9c42819150)
- npm init [`946d3de`](https://github.com/fengyuanchen/is-array-buffer/commit/946d3de82b15471fb2c00a4a2a5a52eb0515eb04)
- [meta] use `npmignore` to autogenerate an npmignore file [`ca4c446`](https://github.com/fengyuanchen/is-array-buffer/commit/ca4c446f37daf5ab8cc590f2194574c2706561ed)
- Only apps should have lockfiles [`be7d8eb`](https://github.com/fengyuanchen/is-array-buffer/commit/be7d8eb09dc5033c04df85d7ba9a8714f4e54357)
- docs: fix badge link [`9ea7fb6`](https://github.com/fengyuanchen/is-array-buffer/commit/9ea7fb638e79f8938161b3b7370cb965d8e93a8b)

<!-- auto-changelog-above -->

## 2.0.0 (Feb 12, 2021)

- Refactor in TypeScript.
- Drop the `dist` directory.
- Drop the UMD bundled file.
- Add a declaration file for TypeScript.

## 1.0.1 (Apr 1, 2018)

- Improve code style.

## 1.0.0 (Jul 25, 2017)

- Supports UMD, CommonJS and ES Module.

## 0.1.0 (Nov 28, 2015)

- Check if ArrayBuffer is defined first.

## 0.0.1 (Nov 11, 2015)

- Initial release.
