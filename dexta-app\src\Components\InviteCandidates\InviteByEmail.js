import React, { useEffect, useState } from "react";
import TextFieldSmall from "../TextFieldSmall/TextFieldSmall";
import { FiPlusCircle } from "react-icons/fi";
import CustomButton from "../CustomButton/CustomButton";
import { RxCross2 } from "react-icons/rx";
import { toast } from "react-toastify";
import { useSelector, useDispatch } from "react-redux";
import { setClearToFalse } from "../../redux/reducers/ClearRows/ClearRowsSlice";
import GeneralModal from "../Modals/GeneralModal";
import Scrollbars from "react-custom-scrollbars";
import { MdError } from "react-icons/md";
import { FaCross, FaEdit } from "react-icons/fa";
import { FaCheck } from "react-icons/fa";
import { IoIosCloseCircle } from "react-icons/io";
import { useTranslation } from "react-i18next";

const InviteByEmail = (props) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const clearRows = useSelector((state) => state.clearrow.setClearRow);
  const [rows, setRows] = useState([
    { id: 1, firstName: "", lastName: "", email: "" },
  ]);
  const [addedRowIds, setAddedRowIds] = useState([0]);
  const [disableField, setDisableField] = useState([0]);
  const [nextId, setNextId] = useState(2);
  const [addRequested, setAddRequested] = useState(false);
  const [generalModal, setGeneralModal] = useState(false);
  const [heading, setHeading] = useState("");
  const [description, setdescription] = useState("");
  // Track which rows are being edited
  const [editingRows, setEditingRows] = useState([]);
  // Store temporary edits before saving
  const [tempEdits, setTempEdits] = useState({});
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  useEffect(() => {
    if (
      props?.inviteData &&
      props?.inviteData.length > 0 &&
      addRequested === false
    ) {
      const newRows = props.inviteData.map((item, index) => ({
        id: index + 1,
        firstName: item.firstName || "",
        lastName: item.lastName || "",
        email: item.email || "",
      }));
      setRows([
        ...newRows,
        { id: newRows.length + 1, firstName: "", lastName: "", email: "" },
      ]);
      setNextId(newRows.length + 2);
      setAddedRowIds(newRows.map((row) => row.id));
      setDisableField(newRows.map((row) => row.id));
      setAddRequested(false);
    }
  }, [props.inviteData, addRequested]);

  const handleAddRow = () => {
    const newId = nextId - 1;
    const newRowEmail = rows.find((row) => row.id === newId)?.email || "";
    const isEmailAlreadyAdded = rows
      .slice(0, rows.length - 1)
      .some((row) => row.email === newRowEmail);
    if (!isEmailAlreadyAdded) {
      const updatedRows = [
        ...rows,
        { id: nextId, firstName: "", lastName: "", email: "" },
      ];
      setRows(updatedRows);
      setNextId((prevId) => prevId + 1);

      // Always add the current row to addedRowIds when Add button is clicked
      setAddedRowIds((prevIds) => [...prevIds, newId]);
      setDisableField((prevIds) => [...prevIds, newId]);

      const filteredRows = updatedRows.filter((row) => row.email !== "");
      props.setInviteData(filteredRows);
    } else {
      toast.error(t("candidates_tab.invite_by_email.table.toasts.email_exists"), {
        toastId: "copy-success",
      });
    }
  };

  const handleFirstNameChange = (id, value) => {
    setRows((prevRows) => {
      const updatedRows = prevRows.map((row) => {
        if (row.id === id) {
          return { ...row, firstName: value };
        }
        return row;
      });
      const filteredRows = updatedRows.filter((row) => row.email !== "");
      props.setInviteData(filteredRows); // Update parent component
      setAddRequested(true);
      return updatedRows;
    });
  };

  const handleLastNameChange = (id, value) => {
    setRows((prevRows) => {
      const updatedRows = prevRows.map((row) => {
        if (row.id === id) {
          return { ...row, lastName: value };
        }
        return row;
      });
      const filteredRows = updatedRows.filter((row) => row.email !== "");
      props.setInviteData(filteredRows);
      setAddRequested(true);
      return updatedRows;
    });
  };

  const handleEmailChange = (id, value) => {
    setRows((prevRows) => {
      const updatedRows = prevRows.map((row) => {
        if (row.id === id) {
          return { ...row, email: value };
        }
        return row;
      });
      const filteredRows = updatedRows.filter((row) => row.email !== "");
      props.setInviteData(filteredRows);
      setAddRequested(true);
      return updatedRows;
    });
  };

  const handleClearRow = (id) => {
    const updatedRows = rows.filter((row) => row.id !== id);
    setRows(updatedRows);
    const filteredRows = updatedRows.filter((row) => row.email !== "");
    props.setInviteData(filteredRows);
  };

  useEffect(() => {
    if (clearRows) {
      setRows([{ id: 1, firstName: "", lastName: "", email: "" }]);
      props.setInviteData([]);
      setDisableField([]);
      setAddedRowIds([]);
      setNextId(2);
      dispatch(setClearToFalse(false));
    }
  }, [clearRows]);

  const checkifcandidatesAllowed = () => {
    if (
      props?.disableInput &&
      props?.candidatesAllowed === 1 &&
      rows?.length === 5
    ) {
      return true;
    } else if (
      props?.disableInput &&
      props?.candidatesAllowed === 2 &&
      rows?.length === 4
    ) {
      return true;
    } else if (
      props?.disableInput &&
      props?.candidatesAllowed === 3 &&
      rows?.length === 3
    ) {
      return true;
    } else if (
      props?.disableInput &&
      props?.candidatesAllowed === 4 &&
      rows?.length === 2
    ) {
      return true;
    } else if (
      props?.disableInput &&
      props?.candidatesAllowed === 5 &&
      rows?.length === 1
    ) {
      return true;
    }
    return false;
  };

  const checkifcandidatesAllowedSignup = () => {
    if (rows?.length === 6 && user_package_check === "free") {
      return true;
    }
    return false;
  };

  const isEmailValid = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.(com|io|net|co|ac\.uk)$/;
    return emailRegex.test(email);
  };

  // Toggle edit mode for a specific row
  const toggleEditRow = (id) => {
    setEditingRows((prev) => {
      if (prev.includes(id)) {
        // Save changes
        if (tempEdits[id]) {
          // Update parent component with filtered rows
          // We need to update the rows first, then update the parent component
          const updatedRows = [...rows];
          const rowIndex = updatedRows.findIndex((row) => row.id === id);
          if (rowIndex !== -1) {
            updatedRows[rowIndex] = {
              ...updatedRows[rowIndex],
              firstName:
                tempEdits[id].firstName !== undefined
                  ? tempEdits[id].firstName
                  : updatedRows[rowIndex].firstName,
              lastName:
                tempEdits[id].lastName !== undefined
                  ? tempEdits[id].lastName
                  : updatedRows[rowIndex].lastName,
              email:
                tempEdits[id].email !== undefined
                  ? tempEdits[id].email
                  : updatedRows[rowIndex].email,
            };

            // Update the rows state
            setRows(updatedRows);

            // Update parent component with filtered rows
            const filteredRows = updatedRows.filter((row) => row.email !== "");
            props.setInviteData(filteredRows);
          }

          // Clear temp edits for this row
          setTempEdits((prev) => {
            const newEdits = { ...prev };
            delete newEdits[id];
            return newEdits;
          });
        }
        return prev.filter((rowId) => rowId !== id);
      } else {
        // Start editing - store current values
        const currentRow = rows.find((row) => row.id === id);
        if (currentRow) {
          setTempEdits((prev) => ({
            ...prev,
            [id]: {
              firstName: currentRow.firstName,
              lastName: currentRow.lastName,
              email: currentRow.email,
            },
          }));
        }
        return [...prev, id];
      }
    });
  };

  return (
    <>
      <GeneralModal
        generalModal={generalModal}
        setGeneralModal={setGeneralModal}
        heading={t("candidates_tab.invite_by_email.modal.title")}
        description={t("candidates_tab.invite_by_email.modal.description")}
      />
      <div
        className={`relative overflow-x-auto mt-5 rounded-md ${
          rows?.length > 4 ? "enable-scrollbar2" : ""
        } ${
          props && props?.createFlow === "yes"
            ? `${props?.inviteData?.length > 4 ? "h-[25rem]" : "h-auto"}`
            : "h-[25rem]"
        }`}
      >
        {!(props?.createFlow === "yes" && props?.inviteData.length < 5) ? (
          <Scrollbars
            style={{ width: "100%", height: "100%", overflowX: "hidden" }}
          >
            <table className="w-full text-sm text-left rtl:text-right min-w-[600px] md:min-w-full">
              <thead className="text-xs bg-gray-200 uppercase">
                <tr>
                  <th scope="col" className="px-2 py-4 md:px-6">
                    {t("candidates_tab.invite_by_email.table.headers.first_name")}
                  </th>
                  <th scope="col" className="px-2 py-4 md:px-6">
                    {t("candidates_tab.invite_by_email.table.headers.last_name")}
                  </th>
                  <th scope="col" className="px-2 py-4 md:px-6">
                    {t("candidates_tab.invite_by_email.table.headers.email")}
                  </th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {rows.map((row) => (
                  <tr
                    key={row.id}
                    className={` border-b ${
                      addedRowIds.includes(row.id) ? "bg-gray-100" : "bg-white"
                    }`}
                  >
                    <td className="px-2 py-4 md:px-6">
                      <TextFieldSmall
                        id={`firstName-${row.id}`}
                        type="text"
                        name={`firstName-${row.id}`}
                        label={t("candidates_tab.invite_by_email.table.labels.first_name")}
                        value={
                          editingRows.includes(row.id) &&
                          tempEdits[row.id]?.firstName !== undefined
                            ? tempEdits[row.id].firstName
                            : row.firstName
                        }
                        mobileFont="16px"
                        disabledField={
                          row.firstName &&
                          row.lastName &&
                          row.email &&
                          addedRowIds.includes(row.id) &&
                          !editingRows.includes(row.id)
                        }
                        onChangeValue={(e) => {
                          if (
                            checkifcandidatesAllowed() ||
                            checkifcandidatesAllowedSignup()
                          ) {
                            setGeneralModal(true);
                          } else if (editingRows.includes(row.id)) {
                            // Update temp edits instead of actual data
                            setTempEdits((prev) => ({
                              ...prev,
                              [row.id]: {
                                ...prev[row.id],
                                firstName: e.target.value,
                              },
                            }));
                          } else {
                            handleFirstNameChange(row.id, e.target.value);
                          }
                        }}
                      />
                    </td>
                    <td className="px-2 py-4 md:px-6">
                      <TextFieldSmall
                        id={`lastName-${row.id}`}
                        type="text"
                        name={`lastName-${row.id}`}
                        label={t("candidates_tab.invite_by_email.table.labels.last_name")}
                        mobileFont="16px"
                        value={
                          editingRows.includes(row.id) &&
                          tempEdits[row.id]?.lastName !== undefined
                            ? tempEdits[row.id].lastName
                            : row.lastName
                        }
                        disabledField={
                          row.firstName &&
                          row.lastName &&
                          row.email &&
                          addedRowIds.includes(row.id) &&
                          !editingRows.includes(row.id)
                        }
                        onChangeValue={(e) => {
                          if (
                            checkifcandidatesAllowed() ||
                            checkifcandidatesAllowedSignup()
                          ) {
                            setGeneralModal(true);
                          } else if (editingRows.includes(row.id)) {
                            // Update temp edits instead of actual data
                            setTempEdits((prev) => ({
                              ...prev,
                              [row.id]: {
                                ...prev[row.id],
                                lastName: e.target.value,
                              },
                            }));
                          } else {
                            handleLastNameChange(row.id, e.target.value);
                          }
                        }}
                      />
                    </td>
                    <td className="px-2 py-4 md:px-6 flex flex-col">
                      <TextFieldSmall
                        id={`email-${row.id}`}
                        type="text"
                        name={`email-${row.id}`}
                        label={t("candidates_tab.invite_by_email.table.labels.email")}
                        errorColor={
                          props?.errorEmails?.includes(row.email) &&
                          "border-alertRed"
                        }
                        value={
                          editingRows.includes(row.id) &&
                          tempEdits[row.id]?.email !== undefined
                            ? tempEdits[row.id].email
                            : row.email
                        }
                        mobileFont="16px"
                        disabledField={
                          row.firstName &&
                          row.lastName &&
                          row.email &&
                          addedRowIds.includes(row.id) &&
                          !editingRows.includes(row.id)
                        }
                        onChangeValue={(e) => {
                          if (
                            checkifcandidatesAllowed() ||
                            checkifcandidatesAllowedSignup()
                          ) {
                            setGeneralModal(true);
                          } else if (editingRows.includes(row.id)) {
                            // Update temp edits instead of actual data
                            setTempEdits((prev) => ({
                              ...prev,
                              [row.id]: {
                                ...prev[row.id],
                                email: e.target.value,
                              },
                            }));
                          } else {
                            handleEmailChange(row.id, e.target.value);
                          }
                        }}
                      />
                    </td>
                    {/* {props?.errorEmails?.includes(row.email) &&
                      "Must be valid email"} */}

                    <td
                      className={`px-2 py-4 md:px-6 ${
                        props?.buttonSize && props?.buttonSize
                      }`}
                    >
                      {row.firstName &&
                      row.lastName &&
                      row.email &&
                      addedRowIds.includes(row.id) ? (
                        <div className="flex flex-row items-center gap-2">
                          {editingRows.includes(row.id) ? (
                            <FaCheck
                              className="cursor-pointer w-5 h-5 text-coalColor"
                              onClick={() => toggleEditRow(row.id)}
                              title={t("candidates_tab.invite_by_email.table.buttons.save")}
                            />
                          ) : (
                            <FaEdit
                              className="cursor-pointer w-5 h-5 text-coalColor hover:text-coalColor"
                              onClick={() => toggleEditRow(row.id)}
                              title={t("candidates_tab.invite_by_email.table.buttons.edit")}
                            />
                          )}
                          <IoIosCloseCircle
                            className={`${
                              editingRows.includes(row.id)
                                ? "text-coalColor hover:text-coalColor"
                                : "cursor-pointer"
                            } w-5 h-5`}
                            onClick={() =>
                              !editingRows.includes(row.id) &&
                              handleClearRow(row.id)
                            }
                          />
                        </div>
                      ) : (
                        <CustomButton
                          label={t("candidates_tab.invite_by_email.table.buttons.add")}
                          icon={FiPlusCircle}
                          bgColor="#252E3A"
                          textColor="#C0FF06"
                          onClickButton={() => {
                            if (props?.disableAdd) {
                              setHeading(t("candidates_tab.invite_by_email.modal.title"));
                              setdescription(t("candidates_tab.invite_by_email.modal.description"));
                              setGeneralModal(true);
                            } else {
                              handleAddRow();
                            }
                          }}
                          disabledCheck={
                            row.firstName === "" ||
                            row.lastName === "" ||
                            row.email === ""
                          }
                          disableField={
                            row.firstName === "" ||
                            row.lastName === "" ||
                            row.email === ""
                          }
                          disabledColor="#D3D5D8"
                          disabledTextColor="#7C8289"
                          hoverBgColor="#C0FF06"
                          hoverTextColor="#252E3A"
                          borderCustom="border border-coalColor text-white"
                        />
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Scrollbars>
        ) : (
          <table className="w-full text-sm text-left rtl:text-right min-w-[600px] md:min-w-full">
            <thead className="text-xs bg-gray-200 uppercase">
              <tr>
                <th scope="col" className="px-2 py-4 md:px-6">
                  {t("candidates_tab.invite_by_email.table.headers.first_name")}
                </th>
                <th scope="col" className="px-2 py-4 md:px-6">
                  {t("candidates_tab.invite_by_email.table.headers.last_name")}
                </th>
                <th scope="col" className="px-2 py-4 md:px-6">
                  {t("candidates_tab.invite_by_email.table.headers.email")}
                </th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {rows.map((row) => (
                <tr
                  key={row.id}
                  className={` border-b ${
                    addedRowIds.includes(row.id) ? "bg-gray-100" : "bg-white"
                  }`}
                >
                  <td className="px-2 py-4 md:px-6 ">
                    <TextFieldSmall
                      id={`firstName-${row.id}`}
                      type="text"
                      name={`firstName-${row.id}`}
                      label={t("candidates_tab.invite_by_email.table.labels.first_name")}
                      value={
                        editingRows.includes(row.id) &&
                        tempEdits[row.id]?.firstName !== undefined
                          ? tempEdits[row.id].firstName
                          : row.firstName
                      }
                      mobileFont="16px"
                      disabledField={
                        row.firstName &&
                        row.lastName &&
                        row.email &&
                        addedRowIds.includes(row.id) &&
                        !editingRows.includes(row.id)
                      }
                      onChangeValue={(e) => {
                        if (
                          checkifcandidatesAllowed() ||
                          checkifcandidatesAllowedSignup()
                        ) {
                          setGeneralModal(true);
                        } else if (editingRows.includes(row.id)) {
                          // Update temp edits instead of actual data
                          setTempEdits((prev) => ({
                            ...prev,
                            [row.id]: {
                              ...prev[row.id],
                              firstName: e.target.value,
                            },
                          }));
                        } else {
                          handleFirstNameChange(row.id, e.target.value);
                        }
                      }}
                    />
                  </td>
                  <td className="px-2 py-4 md:px-6">
                    <TextFieldSmall
                      id={`lastName-${row.id}`}
                      type="text"
                      name={`lastName-${row.id}`}
                      label={t("candidates_tab.invite_by_email.table.labels.last_name")}
                      mobileFont="16px"
                      value={
                        editingRows.includes(row.id) &&
                        tempEdits[row.id]?.lastName !== undefined
                          ? tempEdits[row.id].lastName
                          : row.lastName
                      }
                      disabledField={
                        row.firstName &&
                        row.lastName &&
                        row.email &&
                        addedRowIds.includes(row.id) &&
                        !editingRows.includes(row.id)
                      }
                      onChangeValue={(e) => {
                        if (
                          checkifcandidatesAllowed() ||
                          checkifcandidatesAllowedSignup()
                        ) {
                          setGeneralModal(true);
                        } else if (editingRows.includes(row.id)) {
                          // Update temp edits instead of actual data
                          setTempEdits((prev) => ({
                            ...prev,
                            [row.id]: {
                              ...prev[row.id],
                              lastName: e.target.value,
                            },
                          }));
                        } else {
                          handleLastNameChange(row.id, e.target.value);
                        }
                      }}
                    />
                  </td>
                  <td className="px-2 py-4 md:px-6">
                    <TextFieldSmall
                      id={`email-${row.id}`}
                      type="text"
                      name={`email-${row.id}`}
                      label={t("candidates_tab.invite_by_email.table.labels.email")}
                      value={
                        editingRows.includes(row.id) &&
                        tempEdits[row.id]?.email !== undefined
                          ? tempEdits[row.id].email
                          : row.email
                      }
                      errorColor={
                        props?.errorEmails?.includes(row.email) &&
                        "border-alertRed"
                      }
                      mobileFont="16px"
                      disabledField={
                        row.firstName &&
                        row.lastName &&
                        row.email &&
                        addedRowIds.includes(row.id) &&
                        !editingRows.includes(row.id)
                      }
                      onChangeValue={(e) => {
                        if (
                          checkifcandidatesAllowed() ||
                          checkifcandidatesAllowedSignup()
                        ) {
                          setGeneralModal(true);
                        } else if (editingRows.includes(row.id)) {
                          // Update temp edits instead of actual data
                          setTempEdits((prev) => ({
                            ...prev,
                            [row.id]: {
                              ...prev[row.id],
                              email: e.target.value,
                            },
                          }));
                        } else {
                          handleEmailChange(row.id, e.target.value);
                        }
                      }}
                    />
                  </td>
                  <td
                    className={`px-2 py-4 md:px-6 ${
                      props?.buttonSize && props?.buttonSize
                    }`}
                  >
                    {row.firstName &&
                    row.lastName &&
                    row.email &&
                    addedRowIds.includes(row.id) ? (
                      <div className="flex flex-row items-center gap-2">
                        {editingRows.includes(row.id) ? (
                          <FaCheck
                            className="cursor-pointer w-5 h-5 text-coalColor"
                            onClick={() => toggleEditRow(row.id)}
                            title={t("candidates_tab.invite_by_email.table.buttons.save")}
                          />
                        ) : (
                          <FaEdit
                            className="cursor-pointer w-5 h-5 text-coalColor hover:text-coalColor"
                            onClick={() => toggleEditRow(row.id)}
                            title={t("candidates_tab.invite_by_email.table.buttons.edit")}
                          />
                        )}
                        <IoIosCloseCircle
                          className={`${
                            editingRows.includes(row.id)
                              ? "text-coalColor hover:text-coalColor"
                              : "cursor-pointer"
                          } w-5 h-5`}
                          onClick={() =>
                            !editingRows.includes(row.id) &&
                            handleClearRow(row.id)
                          }
                        />
                      </div>
                    ) : (
                      <CustomButton
                        label={t("candidates_tab.invite_by_email.table.buttons.add")}
                        icon={FiPlusCircle}
                        bgColor="#252E3A"
                        textColor="#C0FF06"
                        onClickButton={() => {
                          if (props?.disableAdd) {
                            setHeading(t("candidates_tab.invite_by_email.modal.title"));
                            setdescription(t("candidates_tab.invite_by_email.modal.description"));
                            setGeneralModal(true);
                          } else {
                            handleAddRow();
                          }
                        }}
                        disabledCheck={
                          row.firstName === "" ||
                          row.lastName === "" ||
                          row.email === ""
                        }
                        disableField={
                          row.firstName === "" ||
                          row.lastName === "" ||
                          row.email === ""
                        }
                        disabledColor="#D3D5D8"
                        disabledTextColor="#7C8289"
                        hoverBgColor="#C0FF06"
                        hoverTextColor="#252E3A"
                        borderCustom="border border-coalColor text-white"
                      />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </>
  );
};

export default InviteByEmail;
